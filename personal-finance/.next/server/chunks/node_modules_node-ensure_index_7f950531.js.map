{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/node_modules/node-ensure/index.js"], "sourcesContent": ["/**\n *  Just run the callback through setImmediate, so that it appears\n *  asynchronous, even when unnecessary.\n */\n\nfunction ensure(modules, callback) {\n  setImmediate(callback);\n}\n\nmodule.exports = ensure;\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS,OAAO,OAAO,EAAE,QAAQ;IAC/B,aAAa;AACf;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}]}