{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { useRouter } from 'next/navigation'\n\ninterface User {\n  id: string\n  email: string\n  name: string\n  avatar?: string\n  familyId?: string\n  familyRole?: string\n  family?: {\n    id: string\n    name: string\n    role: string\n  }\n}\n\ninterface AuthState {\n  user: User | null\n  token: string | null\n  loading: boolean\n  isAuthenticated: boolean\n}\n\nexport function useAuth() {\n  const router = useRouter()\n  const [authState, setAuthState] = useState<AuthState>({\n    user: null,\n    token: null,\n    loading: true,\n    isAuthenticated: false,\n  })\n\n  // 定义logout函数（需要在useEffect之前定义以避免循环依赖）\n  const logout = useCallback(() => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('token')\n      localStorage.removeItem('user')\n    }\n\n    setAuthState({\n      user: null,\n      token: null,\n      loading: false,\n      isAuthenticated: false,\n    })\n\n    router.push('/auth/login')\n  }, [router])\n\n  useEffect(() => {\n    // 确保只在客户端执行\n    if (typeof window === 'undefined') {\n      setAuthState(prev => ({ ...prev, loading: false }))\n      return\n    }\n\n    // 从localStorage获取认证信息\n    const token = localStorage.getItem('token')\n    const userStr = localStorage.getItem('user')\n\n    if (token && userStr) {\n      try {\n        const user = JSON.parse(userStr)\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n      } catch (error) {\n        console.error('Failed to parse user data:', error)\n        logout()\n      }\n    } else {\n      setAuthState(prev => ({ ...prev, loading: false }))\n    }\n  }, [logout])\n\n  const login = async (email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        const { user, token } = data.data\n\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', token)\n          localStorage.setItem('user', JSON.stringify(user))\n        }\n\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n\n        return { success: true }\n      } else {\n        return { success: false, error: data.error }\n      }\n    } catch (error) {\n      console.error('Login error:', error)\n      return { success: false, error: '网络错误，请重试' }\n    }\n  }\n\n  const register = async (name: string, email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ name, email, password }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        const { user, token } = data.data\n\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', token)\n          localStorage.setItem('user', JSON.stringify(user))\n        }\n\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n\n        return { success: true }\n      } else {\n        return { success: false, error: data.error }\n      }\n    } catch (error) {\n      console.error('Register error:', error)\n      return { success: false, error: '网络错误，请重试' }\n    }\n  }\n\n\n\n  const updateUser = (updatedUser: Partial<User>) => {\n    if (authState.user) {\n      const newUser = { ...authState.user, ...updatedUser }\n      if (typeof window !== 'undefined') {\n        localStorage.setItem('user', JSON.stringify(newUser))\n      }\n      setAuthState(prev => ({\n        ...prev,\n        user: newUser,\n      }))\n    }\n  }\n\n  const getAuthHeaders = useCallback(() => {\n    if (authState.token) {\n      return {\n        'Authorization': `Bearer ${authState.token}`,\n        'Content-Type': 'application/json',\n      }\n    }\n    return {\n      'Content-Type': 'application/json',\n    }\n  }, [authState.token])\n\n  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {\n    // 如果body是FormData，不要设置Content-Type，让浏览器自动设置\n    const isFormData = options.body instanceof FormData\n\n    const headers = {\n      ...(isFormData ? {} : getAuthHeaders()), // FormData时只设置Authorization\n      ...options.headers,\n    }\n\n    // 如果是FormData，只添加Authorization头\n    if (isFormData && authState.token) {\n      headers['Authorization'] = `Bearer ${authState.token}`\n    }\n\n    const response = await fetch(url, {\n      ...options,\n      headers,\n    })\n\n    if (response.status === 401) {\n      // Token过期或无效，自动登出\n      logout()\n      throw new Error('认证失败，请重新登录')\n    }\n\n    return response\n  }, [getAuthHeaders, logout])\n\n  return {\n    ...authState,\n    login,\n    register,\n    logout,\n    updateUser,\n    getAuthHeaders,\n    apiCall,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AA0BO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM;QACN,OAAO;QACP,SAAS;QACT,iBAAiB;IACnB;IAEA,sCAAsC;IACtC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzB,uCAAmC;;QAGnC;QAEA,aAAa;YACX,MAAM;YACN,OAAO;YACP,SAAS;YACT,iBAAiB;QACnB;QAEA,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;QACZ,wCAAmC;YACjC,aAAa,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;gBAAM,CAAC;YACjD;QACF;;QAEA,sBAAsB;QACtB,MAAM;QACN,MAAM;IAkBR,GAAG;QAAC;KAAO;IAEX,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI;gBAEjC,uCAAmC;;gBAGnC;gBAEA,aAAa;oBACX;oBACA;oBACA,SAAS;oBACT,iBAAiB;gBACnB;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK;gBAAC;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;IAEA,MAAM,WAAW,OAAO,MAAc,OAAe;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;oBAAO;gBAAS;YAC/C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI;gBAEjC,uCAAmC;;gBAGnC;gBAEA,aAAa;oBACX;oBACA;oBACA,SAAS;oBACT,iBAAiB;gBACnB;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK;gBAAC;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;IAIA,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU,IAAI,EAAE;YAClB,MAAM,UAAU;gBAAE,GAAG,UAAU,IAAI;gBAAE,GAAG,WAAW;YAAC;YACpD,uCAAmC;;YAEnC;YACA,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,MAAM;gBACR,CAAC;QACH;IACF;IAEA,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,UAAU,KAAK,EAAE;YACnB,OAAO;gBACL,iBAAiB,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE;gBAC5C,gBAAgB;YAClB;QACF;QACA,OAAO;YACL,gBAAgB;QAClB;IACF,GAAG;QAAC,UAAU,KAAK;KAAC;IAEpB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,KAAa,UAAuB,CAAC,CAAC;QACvE,4CAA4C;QAC5C,MAAM,aAAa,QAAQ,IAAI,YAAY;QAE3C,MAAM,UAAU;YACd,GAAI,aAAa,CAAC,IAAI,gBAAgB;YACtC,GAAG,QAAQ,OAAO;QACpB;QAEA,gCAAgC;QAChC,IAAI,cAAc,UAAU,KAAK,EAAE;YACjC,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE;QACxD;QAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV;QACF;QAEA,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,kBAAkB;YAClB;YACA,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,GAAG;QAAC;QAAgB;KAAO;IAE3B,OAAO;QACL,GAAG,SAAS;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ui/Toast.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { CheckCircleIcon, ExclamationCircleIcon, XCircleIcon, InformationCircleIcon, XMarkIcon } from '@heroicons/react/24/outline'\n\nexport type ToastType = 'success' | 'error' | 'warning' | 'info'\n\nexport interface ToastMessage {\n  id: string\n  type: ToastType\n  title: string\n  message?: string\n  duration?: number\n}\n\ninterface ToastProps {\n  toast: ToastMessage\n  onClose: (id: string) => void\n}\n\nfunction Toast({ toast, onClose }: ToastProps) {\n  const { id, type, title, message, duration = 5000 } = toast\n\n  useEffect(() => {\n    if (duration > 0) {\n      const timer = setTimeout(() => {\n        onClose(id)\n      }, duration)\n\n      return () => clearTimeout(timer)\n    }\n  }, [id, duration, onClose])\n\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return <CheckCircleIcon className=\"h-6 w-6 text-green-400\" />\n      case 'error':\n        return <XCircleIcon className=\"h-6 w-6 text-red-400\" />\n      case 'warning':\n        return <ExclamationCircleIcon className=\"h-6 w-6 text-yellow-400\" />\n      case 'info':\n        return <InformationCircleIcon className=\"h-6 w-6 text-blue-400\" />\n    }\n  }\n\n  const getBackgroundColor = () => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200'\n      case 'error':\n        return 'bg-red-50 border-red-200'\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200'\n      case 'info':\n        return 'bg-blue-50 border-blue-200'\n    }\n  }\n\n  const getTextColor = () => {\n    switch (type) {\n      case 'success':\n        return 'text-green-800'\n      case 'error':\n        return 'text-red-800'\n      case 'warning':\n        return 'text-yellow-800'\n      case 'info':\n        return 'text-blue-800'\n    }\n  }\n\n  return (\n    <div className={`max-w-sm w-full ${getBackgroundColor()} border rounded-lg shadow-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden`}>\n      <div className=\"p-4\">\n        <div className=\"flex items-start\">\n          <div className=\"flex-shrink-0\">\n            {getIcon()}\n          </div>\n          <div className=\"ml-3 w-0 flex-1 pt-0.5\">\n            <p className={`text-sm font-medium ${getTextColor()}`}>\n              {title}\n            </p>\n            {message && (\n              <p className={`mt-1 text-sm ${getTextColor()} opacity-75`}>\n                {message}\n              </p>\n            )}\n          </div>\n          <div className=\"ml-4 flex-shrink-0 flex\">\n            <button\n              className={`rounded-md inline-flex ${getTextColor()} hover:opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500`}\n              onClick={() => onClose(id)}\n            >\n              <span className=\"sr-only\">关闭</span>\n              <XMarkIcon className=\"h-5 w-5\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\ninterface ToastContainerProps {\n  toasts: ToastMessage[]\n  onClose: (id: string) => void\n}\n\nexport function ToastContainer({ toasts, onClose }: ToastContainerProps) {\n  return (\n    <div\n      aria-live=\"assertive\"\n      className=\"fixed inset-0 flex items-end justify-center px-4 py-6 pointer-events-none sm:p-6 sm:items-start sm:justify-end z-50\"\n    >\n      <div className=\"w-full flex flex-col items-center space-y-4 sm:items-end\">\n        {toasts.map((toast) => (\n          <Toast key={toast.id} toast={toast} onClose={onClose} />\n        ))}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAoBA,SAAS,MAAM,EAAE,KAAK,EAAE,OAAO,EAAc;IAC3C,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,IAAI,EAAE,GAAG;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,GAAG;YAChB,MAAM,QAAQ,WAAW;gBACvB,QAAQ;YACV,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAI;QAAU;KAAQ;IAE1B,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,6NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,qNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,yOAAA,CAAA,wBAAqB;oBAAC,WAAU;;;;;;YAC1C,KAAK;gBACH,qBAAO,8OAAC,yOAAA,CAAA,wBAAqB;oBAAC,WAAU;;;;;;QAC5C;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,gBAAgB,EAAE,qBAAqB,iGAAiG,CAAC;kBACxJ,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAEH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAW,CAAC,oBAAoB,EAAE,gBAAgB;0CAClD;;;;;;4BAEF,yBACC,8OAAC;gCAAE,WAAW,CAAC,aAAa,EAAE,eAAe,WAAW,CAAC;0CACtD;;;;;;;;;;;;kCAIP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAW,CAAC,uBAAuB,EAAE,eAAe,2FAA2F,CAAC;4BAChJ,SAAS,IAAM,QAAQ;;8CAEvB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnC;AAOO,SAAS,eAAe,EAAE,MAAM,EAAE,OAAO,EAAuB;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;sBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;oBAAqB,OAAO;oBAAO,SAAS;mBAAjC,MAAM,EAAE;;;;;;;;;;;;;;;AAK9B", "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/hooks/useToast.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useCallback, ReactNode } from 'react'\nimport { ToastContainer, ToastMessage, ToastType } from '@/components/ui/Toast'\n\ninterface ToastContextType {\n  showToast: (type: ToastType, title: string, message?: string, duration?: number) => void\n  showSuccess: (title: string, message?: string) => void\n  showError: (title: string, message?: string) => void\n  showWarning: (title: string, message?: string) => void\n  showInfo: (title: string, message?: string) => void\n  removeToast: (id: string) => void\n}\n\nconst ToastContext = createContext<ToastContextType | undefined>(undefined)\n\nexport function useToast() {\n  const context = useContext(ToastContext)\n  if (context === undefined) {\n    throw new Error('useToast must be used within a ToastProvider')\n  }\n  return context\n}\n\ninterface ToastProviderProps {\n  children: ReactNode\n}\n\nexport function ToastProvider({ children }: ToastProviderProps) {\n  const [toasts, setToasts] = useState<ToastMessage[]>([])\n\n  const removeToast = useCallback((id: string) => {\n    setToasts((prev) => prev.filter((toast) => toast.id !== id))\n  }, [])\n\n  const showToast = useCallback((\n    type: ToastType,\n    title: string,\n    message?: string,\n    duration?: number\n  ) => {\n    const id = Math.random().toString(36).substr(2, 9)\n    const newToast: ToastMessage = {\n      id,\n      type,\n      title,\n      message,\n      duration,\n    }\n\n    setToasts((prev) => [...prev, newToast])\n  }, [])\n\n  const showSuccess = useCallback((title: string, message?: string) => {\n    showToast('success', title, message)\n  }, [showToast])\n\n  const showError = useCallback((title: string, message?: string) => {\n    showToast('error', title, message)\n  }, [showToast])\n\n  const showWarning = useCallback((title: string, message?: string) => {\n    showToast('warning', title, message)\n  }, [showToast])\n\n  const showInfo = useCallback((title: string, message?: string) => {\n    showToast('info', title, message)\n  }, [showToast])\n\n  const value = {\n    showToast,\n    showSuccess,\n    showError,\n    showWarning,\n    showInfo,\n    removeToast,\n  }\n\n  return (\n    <ToastContext.Provider value={value}>\n      {children}\n      <ToastContainer toasts={toasts} onClose={removeToast} />\n    </ToastContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAcA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAEvD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,UAAU,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK;IAC1D,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAC5B,MACA,OACA,SACA;QAEA,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,WAAyB;YAC7B;YACA;YACA;YACA;YACA;QACF;QAEA,UAAU,CAAC,OAAS;mBAAI;gBAAM;aAAS;IACzC,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC9C,UAAU,WAAW,OAAO;IAC9B,GAAG;QAAC;KAAU;IAEd,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC5C,UAAU,SAAS,OAAO;IAC5B,GAAG;QAAC;KAAU;IAEd,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC9C,UAAU,WAAW,OAAO;IAC9B,GAAG;QAAC;KAAU;IAEd,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC3C,UAAU,QAAQ,OAAO;IAC3B,GAAG;QAAC;KAAU;IAEd,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;;YAC3B;0BACD,8OAAC,iIAAA,CAAA,iBAAc;gBAAC,QAAQ;gBAAQ,SAAS;;;;;;;;;;;;AAG/C", "debugId": null}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency = 'CNY'): string {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n  }).format(amount)\n}\n\nexport function formatDate(date: Date | string, format: 'short' | 'long' | 'time' = 'short'): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  \n  switch (format) {\n    case 'short':\n      return d.toLocaleDateString('zh-CN')\n    case 'long':\n      return d.toLocaleDateString('zh-CN', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        weekday: 'long',\n      })\n    case 'time':\n      return d.toLocaleString('zh-CN')\n    default:\n      return d.toLocaleDateString('zh-CN')\n  }\n}\n\nexport function formatRelativeTime(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) {\n    return '刚刚'\n  } else if (diffInSeconds < 3600) {\n    return `${Math.floor(diffInSeconds / 60)}分钟前`\n  } else if (diffInSeconds < 86400) {\n    return `${Math.floor(diffInSeconds / 3600)}小时前`\n  } else if (diffInSeconds < 2592000) {\n    return `${Math.floor(diffInSeconds / 86400)}天前`\n  } else {\n    return formatDate(d)\n  }\n}\n\nexport function getTransactionTypeLabel(type: string): string {\n  const labels: Record<string, string> = {\n    INCOME: '收入',\n    EXPENSE: '支出',\n    TRANSFER: '转账',\n  }\n  return labels[type] || type\n}\n\nexport function getCardTypeLabel(type: string): string {\n  const labels: Record<string, string> = {\n    BANK_CARD: '银行卡',\n    CREDIT_CARD: '信用卡',\n    WECHAT: '微信',\n    ALIPAY: '支付宝',\n    CASH: '现金',\n    OTHER: '其他',\n  }\n  return labels[type] || type\n}\n\nexport function getTransactionStatusLabel(status: string): string {\n  const labels: Record<string, string> = {\n    PENDING: '待确认',\n    CONFIRMED: '已确认',\n    CANCELLED: '已取消',\n    LINKED: '已关联',\n  }\n  return labels[status] || status\n}\n\nexport function getStatusColor(status: string): string {\n  const colors: Record<string, string> = {\n    PENDING: 'bg-yellow-100 text-yellow-800',\n    CONFIRMED: 'bg-green-100 text-green-800',\n    CANCELLED: 'bg-red-100 text-red-800',\n    LINKED: 'bg-blue-100 text-blue-800',\n  }\n  return colors[status] || 'bg-gray-100 text-gray-800'\n}\n\nexport function getTypeColor(type: string): string {\n  const colors: Record<string, string> = {\n    INCOME: 'text-green-600',\n    EXPENSE: 'text-red-600',\n    TRANSFER: 'text-blue-600',\n  }\n  return colors[type] || 'text-gray-600'\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) {\n      clearTimeout(timeout)\n    }\n    \n    timeout = setTimeout(() => {\n      func(...args)\n    }, wait)\n  }\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB,EAAE,SAAoC,OAAO;IACzF,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAEtD,OAAQ;QACN,KAAK;YACH,OAAO,EAAE,kBAAkB,CAAC;QAC9B,KAAK;YACH,OAAO,EAAE,kBAAkB,CAAC,SAAS;gBACnC,MAAM;gBACN,OAAO;gBACP,KAAK;gBACL,SAAS;YACX;QACF,KAAK;YACH,OAAO,EAAE,cAAc,CAAC;QAC1B;YACE,OAAO,EAAE,kBAAkB,CAAC;IAChC;AACF;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,GAAG,CAAC;IAC/C,OAAO,IAAI,gBAAgB,OAAO;QAChC,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,GAAG,CAAC;IACjD,OAAO,IAAI,gBAAgB,SAAS;QAClC,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,EAAE,CAAC;IACjD,OAAO;QACL,OAAO,WAAW;IACpB;AACF;AAEO,SAAS,wBAAwB,IAAY;IAClD,MAAM,SAAiC;QACrC,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,SAAiC;QACrC,WAAW;QACX,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,OAAO;IACT;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,0BAA0B,MAAc;IACtD,MAAM,SAAiC;QACrC,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,SAAiC;QACrC,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAEO,SAAS,aAAa,IAAY;IACvC,MAAM,SAAiC;QACrC,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS;YACX,aAAa;QACf;QAEA,UAAU,WAAW;YACnB,QAAQ;QACV,GAAG;IACL;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 663, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n  children: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading = false, disabled, children, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    }\n    \n    const sizes = {\n      sm: 'h-8 px-3 text-sm',\n      md: 'h-10 px-4 text-sm',\n      lg: 'h-12 px-6 text-base',\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC/F,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ui/ConfirmDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { ExclamationTriangleIcon } from '@heroicons/react/24/outline'\nimport { Button } from './Button'\n\ninterface ConfirmDialogProps {\n  isOpen: boolean\n  onClose: () => void\n  onConfirm: () => void\n  title: string\n  message: string\n  confirmText?: string\n  cancelText?: string\n  type?: 'danger' | 'warning' | 'info'\n}\n\nexport function ConfirmDialog({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  message,\n  confirmText = '确认',\n  cancelText = '取消',\n  type = 'danger'\n}: ConfirmDialogProps) {\n  const handleConfirm = () => {\n    onConfirm()\n    onClose()\n  }\n\n  const getIconColor = () => {\n    switch (type) {\n      case 'danger':\n        return 'text-red-600'\n      case 'warning':\n        return 'text-yellow-600'\n      case 'info':\n        return 'text-blue-600'\n      default:\n        return 'text-red-600'\n    }\n  }\n\n  const getConfirmButtonVariant = () => {\n    switch (type) {\n      case 'danger':\n        return 'destructive'\n      case 'warning':\n        return 'default'\n      case 'info':\n        return 'default'\n      default:\n        return 'destructive'\n    }\n  }\n\n  return (\n    <Transition appear show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={onClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-black bg-opacity-25\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 overflow-y-auto\">\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 scale-95\"\n              enterTo=\"opacity-100 scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 scale-100\"\n              leaveTo=\"opacity-0 scale-95\"\n            >\n              <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\n                <div className=\"flex items-center\">\n                  <div className={`mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10`}>\n                    <ExclamationTriangleIcon\n                      className={`h-6 w-6 ${getIconColor()}`}\n                      aria-hidden=\"true\"\n                    />\n                  </div>\n                  <div className=\"ml-4\">\n                    <Dialog.Title\n                      as=\"h3\"\n                      className=\"text-lg font-medium leading-6 text-gray-900\"\n                    >\n                      {title}\n                    </Dialog.Title>\n                  </div>\n                </div>\n\n                <div className=\"mt-4\">\n                  <p className=\"text-sm text-gray-500\">\n                    {message}\n                  </p>\n                </div>\n\n                <div className=\"mt-6 flex space-x-3 justify-end\">\n                  <Button\n                    variant=\"outline\"\n                    onClick={onClose}\n                  >\n                    {cancelText}\n                  </Button>\n                  <Button\n                    variant={getConfirmButtonVariant() as any}\n                    onClick={handleConfirm}\n                  >\n                    {confirmText}\n                  </Button>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAkBO,SAAS,cAAc,EAC5B,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,cAAc,IAAI,EAClB,aAAa,IAAI,EACjB,OAAO,QAAQ,EACI;IACnB,MAAM,gBAAgB;QACpB;QACA;IACF;IAEA,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,0BAA0B;QAC9B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU;QAAC,MAAM;QAAC,MAAM;QAAQ,IAAI,qMAAA,CAAA,WAAQ;kBAC3C,cAAA,8OAAC,+KAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,qMAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAU;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,gHAAgH,CAAC;0DAChI,cAAA,8OAAC,6OAAA,CAAA,0BAAuB;oDACtB,WAAW,CAAC,QAAQ,EAAE,gBAAgB;oDACtC,eAAY;;;;;;;;;;;0DAGhB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;oDACX,IAAG;oDACH,WAAU;8DAET;;;;;;;;;;;;;;;;;kDAKP,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDACV;;;;;;;;;;;kDAIL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS;0DAER;;;;;;0DAEH,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,SAAS;0DAER;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrB", "debugId": null}}, {"offset": {"line": 950, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/hooks/useConfirm.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useCallback, ReactNode } from 'react'\nimport { ConfirmDialog } from '@/components/ui/ConfirmDialog'\n\ninterface ConfirmOptions {\n  title: string\n  message: string\n  confirmText?: string\n  cancelText?: string\n  type?: 'danger' | 'warning' | 'info'\n}\n\ninterface ConfirmContextType {\n  confirm: (options: ConfirmOptions) => Promise<boolean>\n}\n\nconst ConfirmContext = createContext<ConfirmContextType | undefined>(undefined)\n\nexport function useConfirm() {\n  const context = useContext(ConfirmContext)\n  if (context === undefined) {\n    throw new Error('useConfirm must be used within a ConfirmProvider')\n  }\n  return context\n}\n\ninterface ConfirmProviderProps {\n  children: ReactNode\n}\n\ninterface ConfirmState extends ConfirmOptions {\n  isOpen: boolean\n  resolve?: (value: boolean) => void\n}\n\nexport function ConfirmProvider({ children }: ConfirmProviderProps) {\n  const [confirmState, setConfirmState] = useState<ConfirmState>({\n    isOpen: false,\n    title: '',\n    message: '',\n  })\n\n  const confirm = useCallback((options: ConfirmOptions): Promise<boolean> => {\n    return new Promise((resolve) => {\n      setConfirmState({\n        ...options,\n        isOpen: true,\n        resolve,\n      })\n    })\n  }, [])\n\n  const handleClose = useCallback(() => {\n    setConfirmState((prev) => ({\n      ...prev,\n      isOpen: false,\n    }))\n    if (confirmState.resolve) {\n      confirmState.resolve(false)\n    }\n  }, [confirmState.resolve])\n\n  const handleConfirm = useCallback(() => {\n    if (confirmState.resolve) {\n      confirmState.resolve(true)\n    }\n  }, [confirmState.resolve])\n\n  const value = {\n    confirm,\n  }\n\n  return (\n    <ConfirmContext.Provider value={value}>\n      {children}\n      <ConfirmDialog\n        isOpen={confirmState.isOpen}\n        onClose={handleClose}\n        onConfirm={handleConfirm}\n        title={confirmState.title}\n        message={confirmState.message}\n        confirmText={confirmState.confirmText}\n        cancelText={confirmState.cancelText}\n        type={confirmState.type}\n      />\n    </ConfirmContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAiBA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAkC;AAE9D,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAWO,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,QAAQ;QACR,OAAO;QACP,SAAS;IACX;IAEA,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3B,OAAO,IAAI,QAAQ,CAAC;YAClB,gBAAgB;gBACd,GAAG,OAAO;gBACV,QAAQ;gBACR;YACF;QACF;IACF,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,gBAAgB,CAAC,OAAS,CAAC;gBACzB,GAAG,IAAI;gBACP,QAAQ;YACV,CAAC;QACD,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC;QACvB;IACF,GAAG;QAAC,aAAa,OAAO;KAAC;IAEzB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC;QACvB;IACF,GAAG;QAAC,aAAa,OAAO;KAAC;IAEzB,MAAM,QAAQ;QACZ;IACF;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;;YAC7B;0BACD,8OAAC,yIAAA,CAAA,gBAAa;gBACZ,QAAQ,aAAa,MAAM;gBAC3B,SAAS;gBACT,WAAW;gBACX,OAAO,aAAa,KAAK;gBACzB,SAAS,aAAa,OAAO;gBAC7B,aAAa,aAAa,WAAW;gBACrC,YAAY,aAAa,UAAU;gBACnC,MAAM,aAAa,IAAI;;;;;;;;;;;;AAI/B", "debugId": null}}, {"offset": {"line": 1036, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface ClientOnlyProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n}\n\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  const [hasMounted, setHasMounted] = useState(false)\n\n  useEffect(() => {\n    setHasMounted(true)\n  }, [])\n\n  if (!hasMounted) {\n    return <>{fallback}</>\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 1064, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/hooks/useAuth'\nimport { ClientOnly } from '@/components/ClientOnly'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requireFamily?: boolean // 是否需要用户已加入家庭\n  requireFamilyAdmin?: boolean // 是否需要家庭管理员权限\n}\n\nconst LoadingSpinner = () => (\n  <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n    <div className=\"text-center\">\n      <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      <p className=\"mt-4 text-gray-600\">加载中...</p>\n    </div>\n  </div>\n)\n\nfunction ProtectedRouteContent({\n  children,\n  requireFamily = false,\n  requireFamilyAdmin = false\n}: ProtectedRouteProps) {\n  const router = useRouter()\n  const { user, loading, isAuthenticated } = useAuth()\n\n  useEffect(() => {\n    if (!loading) {\n      if (!isAuthenticated) {\n        router.push('/auth/login')\n        return\n      }\n\n      if (requireFamily && !user?.familyId) {\n        router.push('/family')\n        return\n      }\n\n      if (requireFamilyAdmin && user?.familyRole !== 'OWNER' && user?.familyRole !== 'ADMIN') {\n        router.push('/dashboard')\n        return\n      }\n    }\n  }, [loading, isAuthenticated, user, router, requireFamily, requireFamilyAdmin])\n\n  if (loading) {\n    return <LoadingSpinner />\n  }\n\n  if (!isAuthenticated) {\n    return null // 将重定向到登录页面\n  }\n\n  if (requireFamily && !user?.familyId) {\n    return null // 将重定向到家庭页面\n  }\n\n  if (requireFamilyAdmin && user?.familyRole !== 'OWNER' && user?.familyRole !== 'ADMIN') {\n    return null // 将重定向到仪表板\n  }\n\n  return <>{children}</>\n}\n\nexport function ProtectedRoute(props: ProtectedRouteProps) {\n  return (\n    <ClientOnly fallback={<LoadingSpinner />}>\n      <ProtectedRouteContent {...props} />\n    </ClientOnly>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAaA,MAAM,iBAAiB,kBACrB,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;;;;;;;;;;;;AAKxC,SAAS,sBAAsB,EAC7B,QAAQ,EACR,gBAAgB,KAAK,EACrB,qBAAqB,KAAK,EACN;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;YACZ,IAAI,CAAC,iBAAiB;gBACpB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,iBAAiB,CAAC,MAAM,UAAU;gBACpC,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,sBAAsB,MAAM,eAAe,WAAW,MAAM,eAAe,SAAS;gBACtF,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;IACF,GAAG;QAAC;QAAS;QAAiB;QAAM;QAAQ;QAAe;KAAmB;IAE9E,IAAI,SAAS;QACX,qBAAO,8OAAC;;;;;IACV;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,KAAK,YAAY;;IAC1B;IAEA,IAAI,iBAAiB,CAAC,MAAM,UAAU;QACpC,OAAO,KAAK,YAAY;;IAC1B;IAEA,IAAI,sBAAsB,MAAM,eAAe,WAAW,MAAM,eAAe,SAAS;QACtF,OAAO,KAAK,WAAW;;IACzB;IAEA,qBAAO;kBAAG;;AACZ;AAEO,SAAS,eAAe,KAA0B;IACvD,qBACE,8OAAC,gIAAA,CAAA,aAAU;QAAC,wBAAU,8OAAC;;;;;kBACrB,cAAA,8OAAC;YAAuB,GAAG,KAAK;;;;;;;;;;;AAGtC", "debugId": null}}, {"offset": {"line": 1184, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/cards/CardGrid.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { PencilIcon, TrashIcon } from '@heroicons/react/24/outline'\n\ninterface Card {\n  id: string\n  name: string\n  type: string\n  accountNo?: string\n  bankName?: string\n  balance: number\n  isActive: boolean\n  createdAt: string\n  user: {\n    id: string\n    name: string\n  }\n  _count: {\n    transactions: number\n    billImports: number\n  }\n}\n\ninterface CardGridProps {\n  cards: Card[]\n  onEdit: (card: Card) => void\n  onDelete: (cardId: string) => void\n}\n\nexport function CardGrid({ cards, onEdit, onDelete }: CardGridProps) {\n  const getCardTypeLabel = (type: string) => {\n    const labels: Record<string, string> = {\n      DEBIT_CARD: '借记卡',\n      CREDIT_CARD: '信用卡',\n      WECHAT: '微信',\n      ALIPAY: '支付宝',\n      CASH: '现金',\n      OTHER: '其他',\n    }\n    return labels[type] || type\n  }\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY'\n    }).format(amount)\n  }\n\n  const getCardColor = (type: string) => {\n    const colors: Record<string, string> = {\n      DEBIT_CARD: 'from-blue-500 to-blue-600',\n      CREDIT_CARD: 'from-purple-500 to-purple-600',\n      WECHAT: 'from-green-500 to-green-600',\n      ALIPAY: 'from-blue-400 to-blue-500',\n      CASH: 'from-gray-500 to-gray-600',\n      OTHER: 'from-indigo-500 to-indigo-600',\n    }\n    return colors[type] || 'from-gray-500 to-gray-600'\n  }\n\n  if (cards.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-gray-500 text-lg mb-4\">暂无卡片</div>\n        <p className=\"text-gray-400\">点击上方\"添加卡片\"按钮创建您的第一张卡片</p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n      {cards.filter(card => card.isActive).map((card) => (\n        <div\n          key={card.id}\n          className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\"\n        >\n          {/* 卡片头部 - 模拟银行卡样式 */}\n          <div className={`bg-gradient-to-r ${getCardColor(card.type)} p-6 text-white relative`}>\n            <div className=\"flex justify-between items-start\">\n              <div>\n                <h3 className=\"text-lg font-semibold truncate\">{card.name}</h3>\n                <p className=\"text-sm opacity-90 mt-1\">{getCardTypeLabel(card.type)}</p>\n              </div>\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => onEdit(card)}\n                  className=\"p-1 hover:bg-white/20 rounded-full transition-colors\"\n                  title=\"编辑卡片\"\n                >\n                  <PencilIcon className=\"h-4 w-4\" />\n                </button>\n                <button\n                  onClick={() => onDelete(card.id)}\n                  className=\"p-1 hover:bg-white/20 rounded-full transition-colors\"\n                  title=\"删除卡片\"\n                >\n                  <TrashIcon className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </div>\n            \n            {card.accountNo && (\n              <div className=\"mt-4\">\n                <p className=\"text-sm opacity-75\">卡号</p>\n                <p className=\"font-mono text-lg tracking-wider\">{card.accountNo}</p>\n              </div>\n            )}\n            \n            <div className=\"mt-4\">\n              <p className=\"text-sm opacity-75\">余额</p>\n              <p className=\"text-2xl font-bold\">{formatCurrency(card.balance)}</p>\n            </div>\n          </div>\n\n          {/* 卡片内容 */}\n          <div className=\"p-6\">\n            {card.bankName && (\n              <div className=\"mb-4\">\n                <p className=\"text-sm text-gray-600\">银行: {card.bankName}</p>\n              </div>\n            )}\n\n            {/* 统计信息 */}\n            <div className=\"grid grid-cols-2 gap-4 mb-6\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-blue-600\">{card._count.transactions}</div>\n                <div className=\"text-sm text-gray-500\">交易记录</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-green-600\">{card._count.billImports}</div>\n                <div className=\"text-sm text-gray-500\">账单导入</div>\n              </div>\n            </div>\n\n            {/* 操作按钮 */}\n            <div className=\"space-y-3\">\n              <Link\n                href={`/import?cardId=${card.id}`}\n                className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-center block\"\n              >\n                账单导入\n              </Link>\n              <Link\n                href={`/transactions?cardId=${card.id}`}\n                className=\"w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors text-center block\"\n              >\n                查看交易\n              </Link>\n            </div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AA8BO,SAAS,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAiB;IACjE,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAiC;YACrC,YAAY;YACZ,aAAa;YACb,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,OAAO;QACT;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAiC;YACrC,YAAY;YACZ,aAAa;YACb,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,OAAO;QACT;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BAA6B;;;;;;8BAC5C,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,GAAG,CAAC,CAAC,qBACxC,8OAAC;gBAEC,WAAU;;kCAGV,8OAAC;wBAAI,WAAW,CAAC,iBAAiB,EAAE,aAAa,KAAK,IAAI,EAAE,wBAAwB,CAAC;;0CACnF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAkC,KAAK,IAAI;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAA2B,iBAAiB,KAAK,IAAI;;;;;;;;;;;;kDAEpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,OAAO;gDACtB,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,mNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,8OAAC;gDACC,SAAS,IAAM,SAAS,KAAK,EAAE;gDAC/B,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;4BAK1B,KAAK,SAAS,kBACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC;wCAAE,WAAU;kDAAoC,KAAK,SAAS;;;;;;;;;;;;0CAInE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC;wCAAE,WAAU;kDAAsB,eAAe,KAAK,OAAO;;;;;;;;;;;;;;;;;;kCAKlE,8OAAC;wBAAI,WAAU;;4BACZ,KAAK,QAAQ,kBACZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;wCAAwB;wCAAK,KAAK,QAAQ;;;;;;;;;;;;0CAK3D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAoC,KAAK,MAAM,CAAC,YAAY;;;;;;0DAC3E,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAqC,KAAK,MAAM,CAAC,WAAW;;;;;;0DAC3E,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAK3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE;wCACjC,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,qBAAqB,EAAE,KAAK,EAAE,EAAE;wCACvC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;eAxEA,KAAK,EAAE;;;;;;;;;;AAiFtB", "debugId": null}}, {"offset": {"line": 1520, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/cards/CardForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/hooks/useAuth'\nimport { useToast } from '@/hooks/useToast'\nimport { Button } from '@/components/ui/Button'\n\ninterface Card {\n  id: string\n  name: string\n  type: string\n  accountNo?: string\n  bankName?: string\n  balance: number\n  description?: string\n  isActive: boolean\n}\n\ninterface CardFormProps {\n  card?: Card | null\n  onSuccess: () => void\n  onCancel: () => void\n}\n\nconst CARD_TYPES = [\n  { value: 'DEBIT_CARD', label: '借记卡' },\n  { value: 'CREDIT_CARD', label: '信用卡' },\n  { value: 'WECHAT', label: '微信' },\n  { value: 'ALIPAY', label: '支付宝' },\n  { value: 'CASH', label: '现金' },\n  { value: 'OTHER', label: '其他' },\n]\n\nexport function CardForm({ card, onSuccess, onCancel }: CardFormProps) {\n  const { apiCall } = useAuth()\n  const { showSuccess, showError } = useToast()\n  const [loading, setLoading] = useState(false)\n  const [formData, setFormData] = useState({\n    name: '',\n    type: 'DEBIT_CARD',\n    accountNo: '',\n    bankName: '',\n    balance: 0,\n    description: '',\n    isActive: true,\n  })\n\n  useEffect(() => {\n    if (card) {\n      setFormData({\n        name: card.name,\n        type: card.type,\n        accountNo: card.accountNo || '',\n        bankName: card.bankName || '',\n        balance: card.balance,\n        description: card.description || '',\n        isActive: card.isActive,\n      })\n    }\n  }, [card])\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n\n    try {\n      const url = card ? `/api/cards/${card.id}` : '/api/cards'\n      const method = card ? 'PUT' : 'POST'\n\n      const response = await apiCall(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      })\n\n      const data = await response.json()\n      if (data.success) {\n        showSuccess(card ? '卡片更新成功' : '卡片创建成功')\n        onSuccess()\n      } else {\n        showError('操作失败', data.error || '请检查输入信息')\n      }\n    } catch (error) {\n      console.error('Form submission failed:', error)\n      showError('操作失败', '网络错误，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n    const { name, value, type } = e.target\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'number' ? parseFloat(value) || 0 : \n              type === 'checkbox' ? (e.target as HTMLInputElement).checked : value\n    }))\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\">\n        <h2 className=\"text-xl font-bold mb-4\">\n          {card ? '编辑卡片' : '添加卡片'}\n        </h2>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* 卡片名称 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              卡片名称 *\n            </label>\n            <input\n              type=\"text\"\n              name=\"name\"\n              value={formData.name}\n              onChange={handleChange}\n              required\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              placeholder=\"例如：招商银行储蓄卡\"\n            />\n          </div>\n\n          {/* 卡片类型 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              卡片类型 *\n            </label>\n            <select\n              name=\"type\"\n              value={formData.type}\n              onChange={handleChange}\n              required\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n            >\n              {CARD_TYPES.map(type => (\n                <option key={type.value} value={type.value}>\n                  {type.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* 账号 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              账号/卡号\n            </label>\n            <input\n              type=\"text\"\n              name=\"accountNo\"\n              value={formData.accountNo}\n              onChange={handleChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              placeholder=\"例如：****1234\"\n            />\n          </div>\n\n          {/* 银行名称 */}\n          {(formData.type === 'DEBIT_CARD' || formData.type === 'CREDIT_CARD') && (\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                银行名称\n              </label>\n              <input\n                type=\"text\"\n                name=\"bankName\"\n                value={formData.bankName}\n                onChange={handleChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n                placeholder=\"例如：招商银行\"\n              />\n            </div>\n          )}\n\n          {/* 余额 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              当前余额\n            </label>\n            <input\n              type=\"number\"\n              name=\"balance\"\n              value={formData.balance}\n              onChange={handleChange}\n              step=\"0.01\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              placeholder=\"0.00\"\n            />\n          </div>\n\n          {/* 描述 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              描述\n            </label>\n            <textarea\n              name=\"description\"\n              value={formData.description}\n              onChange={handleChange}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              placeholder=\"可选的描述信息\"\n            />\n          </div>\n\n          {/* 状态 */}\n          <div className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              name=\"isActive\"\n              checked={formData.isActive}\n              onChange={handleChange}\n              className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n            />\n            <label className=\"ml-2 block text-sm text-gray-700\">\n              启用此卡片\n            </label>\n          </div>\n\n          {/* 按钮 */}\n          <div className=\"flex space-x-3 pt-4\">\n            <Button\n              type=\"submit\"\n              disabled={loading}\n              className=\"flex-1\"\n            >\n              {loading ? '保存中...' : (card ? '更新' : '创建')}\n            </Button>\n            <Button\n              type=\"button\"\n              variant=\"secondary\"\n              onClick={onCancel}\n              disabled={loading}\n              className=\"flex-1\"\n            >\n              取消\n            </Button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAwBA,MAAM,aAAa;IACjB;QAAE,OAAO;QAAc,OAAO;IAAM;IACpC;QAAE,OAAO;QAAe,OAAO;IAAM;IACrC;QAAE,OAAO;QAAU,OAAO;IAAK;IAC/B;QAAE,OAAO;QAAU,OAAO;IAAM;IAChC;QAAE,OAAO;QAAQ,OAAO;IAAK;IAC7B;QAAE,OAAO;QAAS,OAAO;IAAK;CAC/B;AAEM,SAAS,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAiB;IACnE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,MAAM;QACN,WAAW;QACX,UAAU;QACV,SAAS;QACT,aAAa;QACb,UAAU;IACZ;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,YAAY;gBACV,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,WAAW,KAAK,SAAS,IAAI;gBAC7B,UAAU,KAAK,QAAQ,IAAI;gBAC3B,SAAS,KAAK,OAAO;gBACrB,aAAa,KAAK,WAAW,IAAI;gBACjC,UAAU,KAAK,QAAQ;YACzB;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,MAAM,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,GAAG;YAC7C,MAAM,SAAS,OAAO,QAAQ;YAE9B,MAAM,WAAW,MAAM,QAAQ,KAAK;gBAClC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,OAAO,WAAW;gBAC9B;YACF,OAAO;gBACL,UAAU,QAAQ,KAAK,KAAK,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,UAAU,QAAQ;QACpB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,WAAW,WAAW,UAAU,IACzC,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;YACzE,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BACX,OAAO,SAAS;;;;;;8BAGnB,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU;oCACV,QAAQ;oCACR,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU;oCACV,QAAQ;oCACR,WAAU;8CAET,WAAW,GAAG,CAAC,CAAA,qBACd,8OAAC;4CAAwB,OAAO,KAAK,KAAK;sDACvC,KAAK,KAAK;2CADA,KAAK,KAAK;;;;;;;;;;;;;;;;sCAQ7B,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,SAAS;oCACzB,UAAU;oCACV,WAAU;oCACV,aAAY;;;;;;;;;;;;wBAKf,CAAC,SAAS,IAAI,KAAK,gBAAgB,SAAS,IAAI,KAAK,aAAa,mBACjE,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAMlB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,OAAO;oCACvB,UAAU;oCACV,MAAK;oCACL,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,WAAW;oCAC3B,UAAU;oCACV,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,SAAS,SAAS,QAAQ;oCAC1B,UAAU;oCACV,WAAU;;;;;;8CAEZ,8OAAC;oCAAM,WAAU;8CAAmC;;;;;;;;;;;;sCAMtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,WAAY,OAAO,OAAO;;;;;;8CAEvC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/cards/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { useToast } from '@/hooks/useToast'\nimport { useConfirm } from '@/hooks/useConfirm'\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute'\nimport { ClientOnly } from '@/components/ClientOnly'\nimport { Button } from '@/components/ui/Button'\nimport { CardGrid } from '@/components/cards/CardGrid'\nimport { CardForm } from '@/components/cards/CardForm'\n\ninterface Card {\n  id: string\n  name: string\n  type: string\n  accountNo?: string\n  bankName?: string\n  balance: number\n  isActive: boolean\n  createdAt: string\n  user: {\n    id: string\n    name: string\n  }\n  _count: {\n    transactions: number\n    billImports: number\n  }\n}\n\nfunction CardsPageContent() {\n  const { apiCall, isAuthenticated, loading: authLoading } = useAuth()\n  const { showSuccess, showError } = useToast()\n  const { confirm } = useConfirm()\n  const [cards, setCards] = useState<Card[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showCreateForm, setShowCreateForm] = useState(false)\n  const [editingCard, setEditingCard] = useState<Card | null>(null)\n\n  useEffect(() => {\n    // 只有在认证完成且用户已登录时才获取卡片\n    if (!authLoading && isAuthenticated) {\n      fetchCards()\n    } else if (!authLoading && !isAuthenticated) {\n      setLoading(false)\n    }\n  }, [authLoading, isAuthenticated])\n\n  const fetchCards = async () => {\n    try {\n      setLoading(true)\n      const response = await apiCall('/api/cards')\n      const data = await response.json()\n\n      if (data.success) {\n        setCards(data.data)\n      } else {\n        console.error('Failed to fetch cards:', data.error)\n      }\n    } catch (error) {\n      console.error('Failed to fetch cards:', error)\n      // 如果是认证错误，用户会被自动重定向到登录页面\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleEdit = (card: Card) => {\n    setEditingCard(card)\n    setShowCreateForm(true)\n  }\n\n  const handleDelete = async (cardId: string) => {\n    const confirmed = await confirm({\n      title: '删除卡片',\n      message: '确定要删除这张卡片吗？删除后相关的交易记录也会被删除，此操作不可撤销。',\n      confirmText: '删除',\n      cancelText: '取消',\n      type: 'danger'\n    })\n\n    if (!confirmed) return\n\n    try {\n      const response = await apiCall(`/api/cards/${cardId}`, {\n        method: 'DELETE',\n      })\n\n      const data = await response.json()\n      if (data.success) {\n        // 硬删除：从列表中移除卡片\n        setCards(prev => prev.filter(card => card.id !== cardId))\n        showSuccess('删除成功', '卡片及相关交易记录已删除')\n      } else {\n        showError('删除失败', data.error || '请稍后重试')\n      }\n    } catch (error) {\n      console.error('Delete failed:', error)\n      showError('删除失败', '网络错误，请稍后重试')\n    }\n  }\n\n  const handleToggleStatus = async (cardId: string, isActive: boolean) => {\n    try {\n      const response = await apiCall(`/api/cards/${cardId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ isActive }),\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        if (data.success) {\n          setCards(prev => prev.map(card =>\n            card.id === cardId ? { ...card, isActive } : card\n          ))\n          showSuccess('状态更新成功', `卡片已${isActive ? '启用' : '停用'}`)\n        }\n      } else {\n        showError('状态更新失败', '请稍后重试')\n      }\n    } catch (error) {\n      console.error('Toggle status failed:', error)\n      showError('状态更新失败', '网络错误，请稍后重试')\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"bg-white shadow\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">卡片管理</h1>\n            <div className=\"flex space-x-4\">\n              <Link\n                href=\"/dashboard\"\n                className=\"text-blue-600 hover:text-blue-800 transition-colors\"\n              >\n                返回仪表板\n              </Link>\n              <Button\n                onClick={() => {\n                  setEditingCard(null)\n                  setShowCreateForm(true)\n                }}\n              >\n                添加卡片\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* 统计卡片 */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总卡片数</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">{cards.length}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">活跃卡片</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {cards.filter(card => card.isActive).length}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总交易数</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {cards.reduce((sum, card) => sum + card._count.transactions, 0)}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-yellow-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总导入数</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {cards.reduce((sum, card) => sum + card._count.billImports, 0)}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 卡片网格 */}\n        <CardGrid\n          cards={cards}\n          onEdit={handleEdit}\n          onDelete={handleDelete}\n        />\n      </div>\n\n      {/* 创建/编辑表单模态框 */}\n      {showCreateForm && (\n        <CardForm\n          card={editingCard}\n          onSuccess={() => {\n            setShowCreateForm(false)\n            setEditingCard(null)\n            fetchCards() // 重新获取卡片列表\n          }}\n          onCancel={() => {\n            setShowCreateForm(false)\n            setEditingCard(null)\n          }}\n        />\n      )}\n    </div>\n  )\n}\n\nexport default function CardsPage() {\n  return (\n    <ProtectedRoute>\n      <ClientOnly fallback={\n        <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n            <p className=\"mt-4 text-gray-600\">加载中...</p>\n          </div>\n        </div>\n      }>\n        <CardsPageContent />\n      </ClientOnly>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAgCA,SAAS;IACP,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACjE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sBAAsB;QACtB,IAAI,CAAC,eAAe,iBAAiB;YACnC;QACF,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB;YAC3C,WAAW;QACb;IACF,GAAG;QAAC;QAAa;KAAgB;IAEjC,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,QAAQ;YAC/B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS,KAAK,IAAI;YACpB,OAAO;gBACL,QAAQ,KAAK,CAAC,0BAA0B,KAAK,KAAK;YACpD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,yBAAyB;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,kBAAkB;IACpB;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,YAAY,MAAM,QAAQ;YAC9B,OAAO;YACP,SAAS;YACT,aAAa;YACb,YAAY;YACZ,MAAM;QACR;QAEA,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,CAAC,WAAW,EAAE,QAAQ,EAAE;gBACrD,QAAQ;YACV;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,eAAe;gBACf,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBACjD,YAAY,QAAQ;YACtB,OAAO;gBACL,UAAU,QAAQ,KAAK,KAAK,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,UAAU,QAAQ;QACpB;IACF;IAEA,MAAM,qBAAqB,OAAO,QAAgB;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,CAAC,WAAW,EAAE,QAAQ,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,SAAS;gCAAE,GAAG,IAAI;gCAAE;4BAAS,IAAI;oBAE/C,YAAY,UAAU,CAAC,GAAG,EAAE,WAAW,OAAO,MAAM;gBACtD;YACF,OAAO;gBACL,UAAU,UAAU;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,UAAU,UAAU;QACtB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;4CACP,eAAe;4CACf,kBAAkB;wCACpB;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQT,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAwC,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAKvE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAChF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAMnD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,CAAC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;0CAMrE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,CAAC,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQtE,8OAAC,uIAAA,CAAA,WAAQ;wBACP,OAAO;wBACP,QAAQ;wBACR,UAAU;;;;;;;;;;;;YAKb,gCACC,8OAAC,uIAAA,CAAA,WAAQ;gBACP,MAAM;gBACN,WAAW;oBACT,kBAAkB;oBAClB,eAAe;oBACf,aAAa,WAAW;;gBAC1B;gBACA,UAAU;oBACR,kBAAkB;oBAClB,eAAe;gBACjB;;;;;;;;;;;;AAKV;AAEe,SAAS;IACtB,qBACE,8OAAC,4IAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC,gIAAA,CAAA,aAAU;YAAC,wBACV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;sBAItC,cAAA,8OAAC;;;;;;;;;;;;;;;AAIT", "debugId": null}}]}