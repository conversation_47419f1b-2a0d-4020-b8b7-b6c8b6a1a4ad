{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { useRouter } from 'next/navigation'\n\ninterface User {\n  id: string\n  email: string\n  name: string\n  avatar?: string\n  familyId?: string\n  familyRole?: string\n  family?: {\n    id: string\n    name: string\n    role: string\n  }\n}\n\ninterface AuthState {\n  user: User | null\n  token: string | null\n  loading: boolean\n  isAuthenticated: boolean\n}\n\nexport function useAuth() {\n  const router = useRouter()\n  const [authState, setAuthState] = useState<AuthState>({\n    user: null,\n    token: null,\n    loading: true,\n    isAuthenticated: false,\n  })\n\n  useEffect(() => {\n    // 确保只在客户端执行\n    if (typeof window === 'undefined') {\n      setAuthState(prev => ({ ...prev, loading: false }))\n      return\n    }\n\n    // 从localStorage获取认证信息\n    const token = localStorage.getItem('token')\n    const userStr = localStorage.getItem('user')\n\n    if (token && userStr) {\n      try {\n        const user = JSON.parse(userStr)\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n      } catch (error) {\n        console.error('Failed to parse user data:', error)\n        logout()\n      }\n    } else {\n      setAuthState(prev => ({ ...prev, loading: false }))\n    }\n  }, [])\n\n  const login = async (email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        const { user, token } = data.data\n\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', token)\n          localStorage.setItem('user', JSON.stringify(user))\n        }\n\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n\n        return { success: true }\n      } else {\n        return { success: false, error: data.error }\n      }\n    } catch (error) {\n      console.error('Login error:', error)\n      return { success: false, error: '网络错误，请重试' }\n    }\n  }\n\n  const register = async (name: string, email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ name, email, password }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        const { user, token } = data.data\n\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', token)\n          localStorage.setItem('user', JSON.stringify(user))\n        }\n\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n\n        return { success: true }\n      } else {\n        return { success: false, error: data.error }\n      }\n    } catch (error) {\n      console.error('Register error:', error)\n      return { success: false, error: '网络错误，请重试' }\n    }\n  }\n\n  const logout = useCallback(() => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('token')\n      localStorage.removeItem('user')\n    }\n\n    setAuthState({\n      user: null,\n      token: null,\n      loading: false,\n      isAuthenticated: false,\n    })\n\n    router.push('/auth/login')\n  }, [router])\n\n  const updateUser = (updatedUser: Partial<User>) => {\n    if (authState.user) {\n      const newUser = { ...authState.user, ...updatedUser }\n      if (typeof window !== 'undefined') {\n        localStorage.setItem('user', JSON.stringify(newUser))\n      }\n      setAuthState(prev => ({\n        ...prev,\n        user: newUser,\n      }))\n    }\n  }\n\n  const getAuthHeaders = useCallback(() => {\n    if (authState.token) {\n      return {\n        'Authorization': `Bearer ${authState.token}`,\n        'Content-Type': 'application/json',\n      }\n    }\n    return {\n      'Content-Type': 'application/json',\n    }\n  }, [authState.token])\n\n  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {\n    const headers = {\n      ...getAuthHeaders(),\n      ...options.headers,\n    }\n\n    const response = await fetch(url, {\n      ...options,\n      headers,\n    })\n\n    if (response.status === 401) {\n      // Token过期或无效，自动登出\n      logout()\n      throw new Error('认证失败，请重新登录')\n    }\n\n    return response\n  }, [getAuthHeaders, logout])\n\n  return {\n    ...authState,\n    login,\n    register,\n    logout,\n    updateUser,\n    getAuthHeaders,\n    apiCall,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AA0BO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM;QACN,OAAO;QACP,SAAS;QACT,iBAAiB;IACnB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;QACZ,wCAAmC;YACjC,aAAa,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;gBAAM,CAAC;YACjD;QACF;;QAEA,sBAAsB;QACtB,MAAM;QACN,MAAM;IAkBR,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI;gBAEjC,uCAAmC;;gBAGnC;gBAEA,aAAa;oBACX;oBACA;oBACA,SAAS;oBACT,iBAAiB;gBACnB;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK;gBAAC;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;IAEA,MAAM,WAAW,OAAO,MAAc,OAAe;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;oBAAO;gBAAS;YAC/C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI;gBAEjC,uCAAmC;;gBAGnC;gBAEA,aAAa;oBACX;oBACA;oBACA,SAAS;oBACT,iBAAiB;gBACnB;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK;gBAAC;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;IAEA,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzB,uCAAmC;;QAGnC;QAEA,aAAa;YACX,MAAM;YACN,OAAO;YACP,SAAS;YACT,iBAAiB;QACnB;QAEA,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU,IAAI,EAAE;YAClB,MAAM,UAAU;gBAAE,GAAG,UAAU,IAAI;gBAAE,GAAG,WAAW;YAAC;YACpD,uCAAmC;;YAEnC;YACA,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,MAAM;gBACR,CAAC;QACH;IACF;IAEA,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,UAAU,KAAK,EAAE;YACnB,OAAO;gBACL,iBAAiB,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE;gBAC5C,gBAAgB;YAClB;QACF;QACA,OAAO;YACL,gBAAgB;QAClB;IACF,GAAG;QAAC,UAAU,KAAK;KAAC;IAEpB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,KAAa,UAAuB,CAAC,CAAC;QACvE,MAAM,UAAU;YACd,GAAG,gBAAgB;YACnB,GAAG,QAAQ,OAAO;QACpB;QAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV;QACF;QAEA,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,kBAAkB;YAClB;YACA,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,GAAG;QAAC;QAAgB;KAAO;IAE3B,OAAO;QACL,GAAG,SAAS;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency = 'CNY'): string {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n  }).format(amount)\n}\n\nexport function formatDate(date: Date | string, format: 'short' | 'long' | 'time' = 'short'): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  \n  switch (format) {\n    case 'short':\n      return d.toLocaleDateString('zh-CN')\n    case 'long':\n      return d.toLocaleDateString('zh-CN', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        weekday: 'long',\n      })\n    case 'time':\n      return d.toLocaleString('zh-CN')\n    default:\n      return d.toLocaleDateString('zh-CN')\n  }\n}\n\nexport function formatRelativeTime(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) {\n    return '刚刚'\n  } else if (diffInSeconds < 3600) {\n    return `${Math.floor(diffInSeconds / 60)}分钟前`\n  } else if (diffInSeconds < 86400) {\n    return `${Math.floor(diffInSeconds / 3600)}小时前`\n  } else if (diffInSeconds < 2592000) {\n    return `${Math.floor(diffInSeconds / 86400)}天前`\n  } else {\n    return formatDate(d)\n  }\n}\n\nexport function getTransactionTypeLabel(type: string): string {\n  const labels: Record<string, string> = {\n    INCOME: '收入',\n    EXPENSE: '支出',\n    TRANSFER: '转账',\n  }\n  return labels[type] || type\n}\n\nexport function getCardTypeLabel(type: string): string {\n  const labels: Record<string, string> = {\n    BANK_CARD: '银行卡',\n    CREDIT_CARD: '信用卡',\n    WECHAT: '微信',\n    ALIPAY: '支付宝',\n    CASH: '现金',\n    OTHER: '其他',\n  }\n  return labels[type] || type\n}\n\nexport function getTransactionStatusLabel(status: string): string {\n  const labels: Record<string, string> = {\n    PENDING: '待确认',\n    CONFIRMED: '已确认',\n    CANCELLED: '已取消',\n    LINKED: '已关联',\n  }\n  return labels[status] || status\n}\n\nexport function getStatusColor(status: string): string {\n  const colors: Record<string, string> = {\n    PENDING: 'bg-yellow-100 text-yellow-800',\n    CONFIRMED: 'bg-green-100 text-green-800',\n    CANCELLED: 'bg-red-100 text-red-800',\n    LINKED: 'bg-blue-100 text-blue-800',\n  }\n  return colors[status] || 'bg-gray-100 text-gray-800'\n}\n\nexport function getTypeColor(type: string): string {\n  const colors: Record<string, string> = {\n    INCOME: 'text-green-600',\n    EXPENSE: 'text-red-600',\n    TRANSFER: 'text-blue-600',\n  }\n  return colors[type] || 'text-gray-600'\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) {\n      clearTimeout(timeout)\n    }\n    \n    timeout = setTimeout(() => {\n      func(...args)\n    }, wait)\n  }\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB,EAAE,SAAoC,OAAO;IACzF,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAEtD,OAAQ;QACN,KAAK;YACH,OAAO,EAAE,kBAAkB,CAAC;QAC9B,KAAK;YACH,OAAO,EAAE,kBAAkB,CAAC,SAAS;gBACnC,MAAM;gBACN,OAAO;gBACP,KAAK;gBACL,SAAS;YACX;QACF,KAAK;YACH,OAAO,EAAE,cAAc,CAAC;QAC1B;YACE,OAAO,EAAE,kBAAkB,CAAC;IAChC;AACF;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,GAAG,CAAC;IAC/C,OAAO,IAAI,gBAAgB,OAAO;QAChC,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,GAAG,CAAC;IACjD,OAAO,IAAI,gBAAgB,SAAS;QAClC,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,EAAE,CAAC;IACjD,OAAO;QACL,OAAO,WAAW;IACpB;AACF;AAEO,SAAS,wBAAwB,IAAY;IAClD,MAAM,SAAiC;QACrC,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,SAAiC;QACrC,WAAW;QACX,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,OAAO;IACT;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,0BAA0B,MAAc;IACtD,MAAM,SAAiC;QACrC,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,SAAiC;QACrC,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAEO,SAAS,aAAa,IAAY;IACvC,MAAM,SAAiC;QACrC,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS;YACX,aAAa;QACf;QAEA,UAAU,WAAW;YACnB,QAAQ;QACV,GAAG;IACL;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n  children: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading = false, disabled, children, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    }\n    \n    const sizes = {\n      sm: 'h-8 px-3 text-sm',\n      md: 'h-10 px-4 text-sm',\n      lg: 'h-12 px-6 text-base',\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC/F,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/cards/CardList.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { getCardTypeLabel, getStatusColor, formatDate } from '@/lib/utils'\n\ninterface Card {\n  id: string\n  name: string\n  type: string\n  accountNo?: string\n  bankName?: string\n  isActive: boolean\n  createdAt: string\n  user: {\n    id: string\n    name: string\n  }\n  _count: {\n    transactions: number\n    billImports: number\n  }\n}\n\ninterface CardListProps {\n  cards: Card[]\n  onEdit?: (card: Card) => void\n  onDelete?: (cardId: string) => void\n  onToggleStatus?: (cardId: string, isActive: boolean) => void\n}\n\nexport function CardList({ cards, onEdit, onDelete, onToggleStatus }: CardListProps) {\n  const [selectedCards, setSelectedCards] = useState<string[]>([])\n\n  const handleSelectAll = (checked: boolean) => {\n    if (checked) {\n      setSelectedCards(cards.map(card => card.id))\n    } else {\n      setSelectedCards([])\n    }\n  }\n\n  const handleSelectCard = (cardId: string, checked: boolean) => {\n    if (checked) {\n      setSelectedCards(prev => [...prev, cardId])\n    } else {\n      setSelectedCards(prev => prev.filter(id => id !== cardId))\n    }\n  }\n\n  const isAllSelected = cards.length > 0 && selectedCards.length === cards.length\n  const isPartiallySelected = selectedCards.length > 0 && selectedCards.length < cards.length\n\n  return (\n    <div className=\"bg-white shadow rounded-lg\">\n      <div className=\"px-6 py-4 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-medium text-gray-900\">卡片列表</h3>\n          <div className=\"flex space-x-2\">\n            {selectedCards.length > 0 && (\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => {\n                  // 批量操作\n                  console.log('批量操作:', selectedCards)\n                }}\n              >\n                批量操作 ({selectedCards.length})\n              </Button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left\">\n                <input\n                  type=\"checkbox\"\n                  checked={isAllSelected}\n                  ref={input => {\n                    if (input) input.indeterminate = isPartiallySelected\n                  }}\n                  onChange={(e) => handleSelectAll(e.target.checked)}\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                卡片信息\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                类型\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                所属用户\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                统计\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                状态\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                创建时间\n              </th>\n              <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                操作\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {cards.map((card) => (\n              <tr key={card.id} className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4\">\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedCards.includes(card.id)}\n                    onChange={(e) => handleSelectCard(card.id, e.target.checked)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                </td>\n                <td className=\"px-6 py-4\">\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-900\">{card.name}</div>\n                    {card.bankName && (\n                      <div className=\"text-sm text-gray-500\">{card.bankName}</div>\n                    )}\n                    {card.accountNo && (\n                      <div className=\"text-sm text-gray-500\">{card.accountNo}</div>\n                    )}\n                  </div>\n                </td>\n                <td className=\"px-6 py-4\">\n                  <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\">\n                    {getCardTypeLabel(card.type)}\n                  </span>\n                </td>\n                <td className=\"px-6 py-4 text-sm text-gray-900\">\n                  {card.user.name}\n                </td>\n                <td className=\"px-6 py-4\">\n                  <div className=\"text-sm text-gray-900\">\n                    <div>交易: {card._count.transactions}</div>\n                    <div>导入: {card._count.billImports}</div>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4\">\n                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                    card.isActive \n                      ? 'bg-green-100 text-green-800' \n                      : 'bg-red-100 text-red-800'\n                  }`}>\n                    {card.isActive ? '活跃' : '禁用'}\n                  </span>\n                </td>\n                <td className=\"px-6 py-4 text-sm text-gray-500\">\n                  {formatDate(card.createdAt)}\n                </td>\n                <td className=\"px-6 py-4 text-right text-sm font-medium space-x-2\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => onEdit?.(card)}\n                  >\n                    编辑\n                  </Button>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => onToggleStatus?.(card.id, !card.isActive)}\n                  >\n                    {card.isActive ? '禁用' : '启用'}\n                  </Button>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => onDelete?.(card.id)}\n                    className=\"text-red-600 hover:text-red-700\"\n                  >\n                    删除\n                  </Button>\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {cards.length === 0 && (\n        <div className=\"text-center py-12\">\n          <svg\n            className=\"mx-auto h-12 w-12 text-gray-400\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n            stroke=\"currentColor\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\"\n            />\n          </svg>\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无卡片</h3>\n          <p className=\"mt-1 text-sm text-gray-500\">开始添加您的第一张卡片</p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AA+BO,SAAS,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAiB;IACjF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS;YACX,iBAAiB,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;QAC5C,OAAO;YACL,iBAAiB,EAAE;QACrB;IACF;IAEA,MAAM,mBAAmB,CAAC,QAAgB;QACxC,IAAI,SAAS;YACX,iBAAiB,CAAA,OAAQ;uBAAI;oBAAM;iBAAO;QAC5C,OAAO;YACL,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO;QACpD;IACF;IAEA,MAAM,gBAAgB,MAAM,MAAM,GAAG,KAAK,cAAc,MAAM,KAAK,MAAM,MAAM;IAC/E,MAAM,sBAAsB,cAAc,MAAM,GAAG,KAAK,cAAc,MAAM,GAAG,MAAM,MAAM;IAE3F,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,8OAAC;4BAAI,WAAU;sCACZ,cAAc,MAAM,GAAG,mBACtB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;oCACP,OAAO;oCACP,QAAQ,GAAG,CAAC,SAAS;gCACvB;;oCACD;oCACQ,cAAc,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACZ,cAAA,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,KAAK,CAAA;gDACH,IAAI,OAAO,MAAM,aAAa,GAAG;4CACnC;4CACA,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;4CACjD,WAAU;;;;;;;;;;;kDAGd,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAkF;;;;;;;;;;;;;;;;;sCAKpG,8OAAC;4BAAM,WAAU;sCACd,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;oCAAiB,WAAU;;sDAC1B,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDACC,MAAK;gDACL,SAAS,cAAc,QAAQ,CAAC,KAAK,EAAE;gDACvC,UAAU,CAAC,IAAM,iBAAiB,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,OAAO;gDAC3D,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAqC,KAAK,IAAI;;;;;;oDAC5D,KAAK,QAAQ,kBACZ,8OAAC;wDAAI,WAAU;kEAAyB,KAAK,QAAQ;;;;;;oDAEtD,KAAK,SAAS,kBACb,8OAAC;wDAAI,WAAU;kEAAyB,KAAK,SAAS;;;;;;;;;;;;;;;;;sDAI5D,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,IAAI;;;;;;;;;;;sDAG/B,8OAAC;4CAAG,WAAU;sDACX,KAAK,IAAI,CAAC,IAAI;;;;;;sDAEjB,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAI;4DAAK,KAAK,MAAM,CAAC,YAAY;;;;;;;kEAClC,8OAAC;;4DAAI;4DAAK,KAAK,MAAM,CAAC,WAAW;;;;;;;;;;;;;;;;;;sDAGrC,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAK,WAAW,CAAC,yDAAyD,EACzE,KAAK,QAAQ,GACT,gCACA,2BACJ;0DACC,KAAK,QAAQ,GAAG,OAAO;;;;;;;;;;;sDAG5B,8OAAC;4CAAG,WAAU;sDACX,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;;;;;;sDAE5B,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,SAAS;8DACzB;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,CAAC,KAAK,QAAQ;8DAEtD,KAAK,QAAQ,GAAG,OAAO;;;;;;8DAE1B,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,WAAW,KAAK,EAAE;oDACjC,WAAU;8DACX;;;;;;;;;;;;;mCAlEI,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;YA4EvB,MAAM,MAAM,KAAK,mBAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,SAAQ;wBACR,QAAO;kCAEP,cAAA,8OAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,aAAa;4BACb,GAAE;;;;;;;;;;;kCAGN,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;;AAKpD", "debugId": null}}, {"offset": {"line": 876, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/cards/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute'\nimport { ClientOnly } from '@/components/ClientOnly'\nimport { Button } from '@/components/ui/Button'\nimport { CardList } from '@/components/cards/CardList'\n\ninterface Card {\n  id: string\n  name: string\n  type: string\n  accountNo?: string\n  bankName?: string\n  balance: number\n  isActive: boolean\n  createdAt: string\n  user: {\n    id: string\n    name: string\n  }\n  _count: {\n    transactions: number\n    billImports: number\n  }\n}\n\nfunction CardsPageContent() {\n  const { apiCall } = useAuth()\n  const [cards, setCards] = useState<Card[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showCreateForm, setShowCreateForm] = useState(false)\n  const [editingCard, setEditingCard] = useState<Card | null>(null)\n\n  useEffect(() => {\n    fetchCards()\n  }, [])\n\n  const fetchData = async () => {\n    try {\n      const [cardsRes, usersRes] = await Promise.all([\n        fetch('/api/cards'),\n        fetch('/api/users')\n      ])\n      \n      const cardsData = await cardsRes.json()\n      const usersData = await usersRes.json()\n      \n      if (cardsData.success) setCards(cardsData.data)\n      if (usersData.success) setUsers(usersData.data)\n    } catch (error) {\n      console.error('Failed to fetch data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleEdit = (card: Card) => {\n    setEditingCard(card)\n    setShowCreateForm(true)\n  }\n\n  const handleDelete = async (cardId: string) => {\n    if (!confirm('确定要删除这张卡片吗？')) return\n\n    try {\n      const response = await fetch(`/api/cards/${cardId}`, {\n        method: 'DELETE',\n      })\n\n      if (response.ok) {\n        setCards(prev => prev.filter(card => card.id !== cardId))\n      } else {\n        alert('删除失败')\n      }\n    } catch (error) {\n      console.error('Delete failed:', error)\n      alert('删除失败')\n    }\n  }\n\n  const handleToggleStatus = async (cardId: string, isActive: boolean) => {\n    try {\n      const response = await fetch(`/api/cards/${cardId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ isActive }),\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        if (data.success) {\n          setCards(prev => prev.map(card => \n            card.id === cardId ? { ...card, isActive } : card\n          ))\n        }\n      } else {\n        alert('状态更新失败')\n      }\n    } catch (error) {\n      console.error('Toggle status failed:', error)\n      alert('状态更新失败')\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"bg-white shadow\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">卡片管理</h1>\n            <div className=\"flex space-x-4\">\n              <Link\n                href=\"/dashboard\"\n                className=\"text-blue-600 hover:text-blue-800 transition-colors\"\n              >\n                返回仪表板\n              </Link>\n              <Button\n                onClick={() => {\n                  setEditingCard(null)\n                  setShowCreateForm(true)\n                }}\n              >\n                添加卡片\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* 统计卡片 */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总卡片数</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">{cards.length}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">活跃卡片</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {cards.filter(card => card.isActive).length}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总交易数</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {cards.reduce((sum, card) => sum + card._count.transactions, 0)}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-yellow-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总导入数</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {cards.reduce((sum, card) => sum + card._count.billImports, 0)}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 卡片列表 */}\n        <CardList\n          cards={cards}\n          onEdit={handleEdit}\n          onDelete={handleDelete}\n          onToggleStatus={handleToggleStatus}\n        />\n      </div>\n\n      {/* 创建/编辑表单模态框 */}\n      {showCreateForm && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"mt-3\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n                {editingCard ? '编辑卡片' : '添加卡片'}\n              </h3>\n              <p className=\"text-sm text-gray-500 mb-4\">\n                表单功能正在开发中...\n              </p>\n              <div className=\"flex justify-end space-x-2\">\n                <Button\n                  variant=\"outline\"\n                  onClick={() => {\n                    setShowCreateForm(false)\n                    setEditingCard(null)\n                  }}\n                >\n                  取消\n                </Button>\n                <Button onClick={() => setShowCreateForm(false)}>\n                  保存\n                </Button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;AAEA;AACA;AACA;AAGA;AACA;AARA;;;;;;;AA6BA,SAAS;IACP,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,CAAC,UAAU,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC7C,MAAM;gBACN,MAAM;aACP;YAED,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,YAAY,MAAM,SAAS,IAAI;YAErC,IAAI,UAAU,OAAO,EAAE,SAAS,UAAU,IAAI;YAC9C,IAAI,UAAU,OAAO,EAAE,SAAS,UAAU,IAAI;QAChD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,kBAAkB;IACpB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,gBAAgB;QAE7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ,EAAE;gBACnD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACnD,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB,OAAO,QAAgB;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ,EAAE;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,SAAS;gCAAE,GAAG,IAAI;gCAAE;4BAAS,IAAI;gBAEjD;YACF,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;4CACP,eAAe;4CACf,kBAAkB;wCACpB;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQT,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAwC,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAKvE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAChF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAMnD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,CAAC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;0CAMrE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,CAAC,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQtE,8OAAC,uIAAA,CAAA,WAAQ;wBACP,OAAO;wBACP,QAAQ;wBACR,UAAU;wBACV,gBAAgB;;;;;;;;;;;;YAKnB,gCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,cAAc,SAAS;;;;;;0CAE1B,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;4CACP,kBAAkB;4CAClB,eAAe;wCACjB;kDACD;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,kBAAkB;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjE", "debugId": null}}]}