{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { useRouter } from 'next/navigation'\n\ninterface User {\n  id: string\n  email: string\n  name: string\n  avatar?: string\n  familyId?: string\n  familyRole?: string\n  family?: {\n    id: string\n    name: string\n    role: string\n  }\n}\n\ninterface AuthState {\n  user: User | null\n  token: string | null\n  loading: boolean\n  isAuthenticated: boolean\n}\n\nexport function useAuth() {\n  const router = useRouter()\n  const [authState, setAuthState] = useState<AuthState>({\n    user: null,\n    token: null,\n    loading: true,\n    isAuthenticated: false,\n  })\n\n  // 定义logout函数（需要在useEffect之前定义以避免循环依赖）\n  const logout = useCallback(() => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('token')\n      localStorage.removeItem('user')\n    }\n\n    setAuthState({\n      user: null,\n      token: null,\n      loading: false,\n      isAuthenticated: false,\n    })\n\n    router.push('/auth/login')\n  }, [router])\n\n  useEffect(() => {\n    // 确保只在客户端执行\n    if (typeof window === 'undefined') {\n      setAuthState(prev => ({ ...prev, loading: false }))\n      return\n    }\n\n    // 从localStorage获取认证信息\n    const token = localStorage.getItem('token')\n    const userStr = localStorage.getItem('user')\n\n    if (token && userStr) {\n      try {\n        const user = JSON.parse(userStr)\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n      } catch (error) {\n        console.error('Failed to parse user data:', error)\n        logout()\n      }\n    } else {\n      setAuthState(prev => ({ ...prev, loading: false }))\n    }\n  }, [logout])\n\n  const login = async (email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        const { user, token } = data.data\n\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', token)\n          localStorage.setItem('user', JSON.stringify(user))\n        }\n\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n\n        return { success: true }\n      } else {\n        return { success: false, error: data.error }\n      }\n    } catch (error) {\n      console.error('Login error:', error)\n      return { success: false, error: '网络错误，请重试' }\n    }\n  }\n\n  const register = async (name: string, email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ name, email, password }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        const { user, token } = data.data\n\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', token)\n          localStorage.setItem('user', JSON.stringify(user))\n        }\n\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n\n        return { success: true }\n      } else {\n        return { success: false, error: data.error }\n      }\n    } catch (error) {\n      console.error('Register error:', error)\n      return { success: false, error: '网络错误，请重试' }\n    }\n  }\n\n\n\n  const updateUser = (updatedUser: Partial<User>) => {\n    if (authState.user) {\n      const newUser = { ...authState.user, ...updatedUser }\n      if (typeof window !== 'undefined') {\n        localStorage.setItem('user', JSON.stringify(newUser))\n      }\n      setAuthState(prev => ({\n        ...prev,\n        user: newUser,\n      }))\n    }\n  }\n\n  const getAuthHeaders = useCallback(() => {\n    if (authState.token) {\n      return {\n        'Authorization': `Bearer ${authState.token}`,\n        'Content-Type': 'application/json',\n      }\n    }\n    return {\n      'Content-Type': 'application/json',\n    }\n  }, [authState.token])\n\n  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {\n    const headers = {\n      ...getAuthHeaders(),\n      ...options.headers,\n    }\n\n    const response = await fetch(url, {\n      ...options,\n      headers,\n    })\n\n    if (response.status === 401) {\n      // Token过期或无效，自动登出\n      logout()\n      throw new Error('认证失败，请重新登录')\n    }\n\n    return response\n  }, [getAuthHeaders, logout])\n\n  return {\n    ...authState,\n    login,\n    register,\n    logout,\n    updateUser,\n    getAuthHeaders,\n    apiCall,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AA0BO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM;QACN,OAAO;QACP,SAAS;QACT,iBAAiB;IACnB;IAEA,sCAAsC;IACtC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzB,uCAAmC;;QAGnC;QAEA,aAAa;YACX,MAAM;YACN,OAAO;YACP,SAAS;YACT,iBAAiB;QACnB;QAEA,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;QACZ,wCAAmC;YACjC,aAAa,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;gBAAM,CAAC;YACjD;QACF;;QAEA,sBAAsB;QACtB,MAAM;QACN,MAAM;IAkBR,GAAG;QAAC;KAAO;IAEX,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI;gBAEjC,uCAAmC;;gBAGnC;gBAEA,aAAa;oBACX;oBACA;oBACA,SAAS;oBACT,iBAAiB;gBACnB;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK;gBAAC;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;IAEA,MAAM,WAAW,OAAO,MAAc,OAAe;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;oBAAO;gBAAS;YAC/C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI;gBAEjC,uCAAmC;;gBAGnC;gBAEA,aAAa;oBACX;oBACA;oBACA,SAAS;oBACT,iBAAiB;gBACnB;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK;gBAAC;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;IAIA,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU,IAAI,EAAE;YAClB,MAAM,UAAU;gBAAE,GAAG,UAAU,IAAI;gBAAE,GAAG,WAAW;YAAC;YACpD,uCAAmC;;YAEnC;YACA,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,MAAM;gBACR,CAAC;QACH;IACF;IAEA,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,UAAU,KAAK,EAAE;YACnB,OAAO;gBACL,iBAAiB,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE;gBAC5C,gBAAgB;YAClB;QACF;QACA,OAAO;YACL,gBAAgB;QAClB;IACF,GAAG;QAAC,UAAU,KAAK;KAAC;IAEpB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,KAAa,UAAuB,CAAC,CAAC;QACvE,MAAM,UAAU;YACd,GAAG,gBAAgB;YACnB,GAAG,QAAQ,OAAO;QACpB;QAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV;QACF;QAEA,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,kBAAkB;YAClB;YACA,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,GAAG;QAAC;QAAgB;KAAO;IAE3B,OAAO;QACL,GAAG,SAAS;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface ClientOnlyProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n}\n\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  const [hasMounted, setHasMounted] = useState(false)\n\n  useEffect(() => {\n    setHasMounted(true)\n  }, [])\n\n  if (!hasMounted) {\n    return <>{fallback}</>\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/hooks/useAuth'\nimport { ClientOnly } from '@/components/ClientOnly'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requireFamily?: boolean // 是否需要用户已加入家庭\n  requireFamilyAdmin?: boolean // 是否需要家庭管理员权限\n}\n\nconst LoadingSpinner = () => (\n  <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n    <div className=\"text-center\">\n      <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      <p className=\"mt-4 text-gray-600\">加载中...</p>\n    </div>\n  </div>\n)\n\nfunction ProtectedRouteContent({\n  children,\n  requireFamily = false,\n  requireFamilyAdmin = false\n}: ProtectedRouteProps) {\n  const router = useRouter()\n  const { user, loading, isAuthenticated } = useAuth()\n\n  useEffect(() => {\n    if (!loading) {\n      if (!isAuthenticated) {\n        router.push('/auth/login')\n        return\n      }\n\n      if (requireFamily && !user?.familyId) {\n        router.push('/family')\n        return\n      }\n\n      if (requireFamilyAdmin && user?.familyRole !== 'OWNER' && user?.familyRole !== 'ADMIN') {\n        router.push('/dashboard')\n        return\n      }\n    }\n  }, [loading, isAuthenticated, user, router, requireFamily, requireFamilyAdmin])\n\n  if (loading) {\n    return <LoadingSpinner />\n  }\n\n  if (!isAuthenticated) {\n    return null // 将重定向到登录页面\n  }\n\n  if (requireFamily && !user?.familyId) {\n    return null // 将重定向到家庭页面\n  }\n\n  if (requireFamilyAdmin && user?.familyRole !== 'OWNER' && user?.familyRole !== 'ADMIN') {\n    return null // 将重定向到仪表板\n  }\n\n  return <>{children}</>\n}\n\nexport function ProtectedRoute(props: ProtectedRouteProps) {\n  return (\n    <ClientOnly fallback={<LoadingSpinner />}>\n      <ProtectedRouteContent {...props} />\n    </ClientOnly>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAaA,MAAM,iBAAiB,kBACrB,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;;;;;;;;;;;;AAKxC,SAAS,sBAAsB,EAC7B,QAAQ,EACR,gBAAgB,KAAK,EACrB,qBAAqB,KAAK,EACN;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;YACZ,IAAI,CAAC,iBAAiB;gBACpB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,iBAAiB,CAAC,MAAM,UAAU;gBACpC,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,sBAAsB,MAAM,eAAe,WAAW,MAAM,eAAe,SAAS;gBACtF,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;IACF,GAAG;QAAC;QAAS;QAAiB;QAAM;QAAQ;QAAe;KAAmB;IAE9E,IAAI,SAAS;QACX,qBAAO,8OAAC;;;;;IACV;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,KAAK,YAAY;;IAC1B;IAEA,IAAI,iBAAiB,CAAC,MAAM,UAAU;QACpC,OAAO,KAAK,YAAY;;IAC1B;IAEA,IAAI,sBAAsB,MAAM,eAAe,WAAW,MAAM,eAAe,SAAS;QACtF,OAAO,KAAK,WAAW;;IACzB;IAEA,qBAAO;kBAAG;;AACZ;AAEO,SAAS,eAAe,KAA0B;IACvD,qBACE,8OAAC,gIAAA,CAAA,aAAU;QAAC,wBAAU,8OAAC;;;;;kBACrB,cAAA,8OAAC;YAAuB,GAAG,KAAK;;;;;;;;;;;AAGtC", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/import/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { useSearchParams } from 'next/navigation'\nimport { useAuth } from '@/hooks/useAuth'\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute'\nimport { ClientOnly } from '@/components/ClientOnly'\nimport Link from 'next/link'\n\ninterface Card {\n  id: string\n  name: string\n  type: string\n  accountNo?: string\n  bankName?: string\n  balance: number\n  isActive: boolean\n}\n\ninterface ImportResult {\n  billImportId: string\n  success: boolean\n  totalCount: number\n  successCount: number\n  errorCount: number\n  duplicateCount: number\n  errors: string[]\n}\n\nfunction ImportPageContent() {\n  const { apiCall, isAuthenticated, loading: authLoading } = useAuth()\n  const searchParams = useSearchParams()\n  const cardId = searchParams.get('cardId')\n\n  const [card, setCard] = useState<Card | null>(null)\n  const [selectedFile, setSelectedFile] = useState<File | null>(null)\n  const [importing, setImporting] = useState(false)\n  const [result, setResult] = useState<ImportResult | null>(null)\n  const [supportedTypes, setSupportedTypes] = useState<string[]>([])\n  const [loading, setLoading] = useState(true)\n\n  const fetchCard = useCallback(async (id: string) => {\n    try {\n      const response = await apiCall(`/api/cards/${id}`)\n      const data = await response.json()\n\n      if (data.success) {\n        setCard(data.data)\n      } else {\n        console.error('Failed to fetch card:', data.error)\n      }\n    } catch (error) {\n      console.error('Failed to fetch card:', error)\n    } finally {\n      setLoading(false)\n    }\n  }, [apiCall])\n\n  const fetchSupportedTypes = useCallback(async () => {\n    try {\n      const response = await apiCall('/api/bills/import')\n      const data = await response.json()\n\n      if (data.success) {\n        setSupportedTypes(data.data.supportedFileTypes)\n      }\n    } catch (error) {\n      console.error('获取支持的文件类型失败:', error)\n    }\n  }, [apiCall])\n\n  useEffect(() => {\n    // 只有在认证完成且用户已登录时才获取数据\n    if (!authLoading && isAuthenticated) {\n      fetchSupportedTypes()\n      if (cardId) {\n        fetchCard(cardId)\n      } else {\n        setLoading(false)\n      }\n    } else if (!authLoading && !isAuthenticated) {\n      setLoading(false)\n    }\n  }, [cardId, fetchCard, fetchSupportedTypes, authLoading, isAuthenticated])\n\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (file) {\n      // 验证文件类型\n      const extension = file.name.toLowerCase().split('.').pop()\n      if (extension && supportedTypes.includes(extension)) {\n        setSelectedFile(file)\n        setResult(null)\n      } else {\n        alert(`不支持的文件类型。支持的类型: ${supportedTypes.join(', ')}`)\n        event.target.value = ''\n      }\n    }\n  }\n\n  const handleImport = async () => {\n    if (!selectedFile || !cardId) {\n      alert('请选择文件')\n      return\n    }\n\n    try {\n      setImporting(true)\n      setResult(null)\n\n      const formData = new FormData()\n      formData.append('file', selectedFile)\n      formData.append('cardId', cardId)\n\n      const response = await apiCall('/api/bills/import', {\n        method: 'POST',\n        body: formData,\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        setResult(data.data)\n        setSelectedFile(null)\n        // 重置文件输入\n        const fileInput = document.getElementById('file-input') as HTMLInputElement\n        if (fileInput) fileInput.value = ''\n      } else {\n        alert(`导入失败: ${data.error}`)\n      }\n    } catch (error) {\n      console.error('导入失败:', error)\n      alert('导入失败，请重试')\n    } finally {\n      setImporting(false)\n    }\n  }\n\n  const getCardTypeLabel = (type: string) => {\n    const labels: Record<string, string> = {\n      DEBIT_CARD: '借记卡',\n      CREDIT_CARD: '信用卡',\n      WECHAT: '微信',\n      ALIPAY: '支付宝',\n      CASH: '现金',\n      OTHER: '其他',\n    }\n    return labels[type] || type\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!cardId) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 py-8\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">账单导入</h1>\n            <p className=\"text-gray-600 mb-8\">请从卡片管理页面选择要导入账单的卡片</p>\n            <Link\n              href=\"/cards\"\n              className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\"\n            >\n              前往卡片管理\n            </Link>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!card) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 py-8\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">卡片不存在</h1>\n            <p className=\"text-gray-600 mb-8\">指定的卡片不存在或您没有权限访问</p>\n            <Link\n              href=\"/cards\"\n              className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\"\n            >\n              返回卡片管理\n            </Link>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">账单导入</h1>\n            <Link\n              href=\"/cards\"\n              className=\"text-blue-600 hover:text-blue-800 transition-colors\"\n            >\n              返回卡片管理\n            </Link>\n          </div>\n          <p className=\"mt-2 text-gray-600\">\n            为 {card.name} ({getCardTypeLabel(card.type)}) 导入账单\n          </p>\n        </div>\n\n        <div className=\"bg-white shadow rounded-lg p-6 mb-8\">\n          <h2 className=\"text-lg font-medium text-gray-900 mb-4\">选择账单文件</h2>\n\n          <div className=\"space-y-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                选择账单文件\n              </label>\n              <input\n                id=\"file-input\"\n                type=\"file\"\n                accept={supportedTypes.map(type => `.${type}`).join(',')}\n                onChange={handleFileSelect}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              />\n              <p className=\"mt-1 text-sm text-gray-500\">\n                支持的文件类型: {supportedTypes.join(', ')}\n              </p>\n            </div>\n\n            <div>\n              <button\n                onClick={handleImport}\n                disabled={!selectedFile || importing}\n                className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\"\n              >\n                {importing ? '导入中...' : '开始导入'}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* 导入结果 */}\n        {result && (\n          <div className=\"bg-white shadow rounded-lg p-6\">\n            <h2 className=\"text-lg font-medium text-gray-900 mb-4\">导入结果</h2>\n\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-blue-600\">{result.totalCount}</div>\n                <div className=\"text-sm text-gray-500\">总记录数</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-green-600\">{result.successCount}</div>\n                <div className=\"text-sm text-gray-500\">成功导入</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-yellow-600\">{result.duplicateCount}</div>\n                <div className=\"text-sm text-gray-500\">重复跳过</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-red-600\">{result.errorCount}</div>\n                <div className=\"text-sm text-gray-500\">导入失败</div>\n              </div>\n            </div>\n\n            {result.errors.length > 0 && (\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 mb-2\">错误信息</h4>\n                <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n                  <ul className=\"text-sm text-red-700 space-y-1\">\n                    {result.errors.map((error, index) => (\n                      <li key={index}>• {error}</li>\n                    ))}\n                  </ul>\n                </div>\n              </div>\n            )}\n\n            <div className=\"mt-6 flex justify-center\">\n              <Link\n                href=\"/dashboard\"\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\"\n              >\n                查看交易记录\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\nexport default function ImportPage() {\n  return (\n    <ProtectedRoute>\n      <ClientOnly fallback={\n        <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n            <p className=\"mt-4 text-gray-600\">加载中...</p>\n          </div>\n        </div>\n      }>\n        <ImportPageContent />\n      </ClientOnly>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AA6BA,SAAS;IACP,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACjE,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,aAAa,GAAG,CAAC;IAEhC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC1D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACnC,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,CAAC,WAAW,EAAE,IAAI;YACjD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,QAAQ,KAAK,IAAI;YACnB,OAAO;gBACL,QAAQ,KAAK,CAAC,yBAAyB,KAAK,KAAK;YACnD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ;YAC/B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,kBAAkB,KAAK,IAAI,CAAC,kBAAkB;YAChD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;QAChC;IACF,GAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sBAAsB;QACtB,IAAI,CAAC,eAAe,iBAAiB;YACnC;YACA,IAAI,QAAQ;gBACV,UAAU;YACZ,OAAO;gBACL,WAAW;YACb;QACF,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB;YAC3C,WAAW;QACb;IACF,GAAG;QAAC;QAAQ;QAAW;QAAqB;QAAa;KAAgB;IAEzE,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,SAAS;YACT,MAAM,YAAY,KAAK,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG;YACxD,IAAI,aAAa,eAAe,QAAQ,CAAC,YAAY;gBACnD,gBAAgB;gBAChB,UAAU;YACZ,OAAO;gBACL,MAAM,CAAC,gBAAgB,EAAE,eAAe,IAAI,CAAC,OAAO;gBACpD,MAAM,MAAM,CAAC,KAAK,GAAG;YACvB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,gBAAgB,CAAC,QAAQ;YAC5B,MAAM;YACN;QACF;QAEA,IAAI;YACF,aAAa;YACb,UAAU;YAEV,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,UAAU;YAE1B,MAAM,WAAW,MAAM,QAAQ,qBAAqB;gBAClD,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,UAAU,KAAK,IAAI;gBACnB,gBAAgB;gBAChB,SAAS;gBACT,MAAM,YAAY,SAAS,cAAc,CAAC;gBAC1C,IAAI,WAAW,UAAU,KAAK,GAAG;YACnC,OAAO;gBACL,MAAM,CAAC,MAAM,EAAE,KAAK,KAAK,EAAE;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAiC;YACrC,YAAY;YACZ,aAAa;YACb,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,OAAO;QACT;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAIH,8OAAC;4BAAE,WAAU;;gCAAqB;gCAC7B,KAAK,IAAI;gCAAC;gCAAG,iBAAiB,KAAK,IAAI;gCAAE;;;;;;;;;;;;;8BAIhD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAEvD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,QAAQ,eAAe,GAAG,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC;4CACpD,UAAU;4CACV,WAAU;;;;;;sDAEZ,8OAAC;4CAAE,WAAU;;gDAA6B;gDAC9B,eAAe,IAAI,CAAC;;;;;;;;;;;;;8CAIlC,8OAAC;8CACC,cAAA,8OAAC;wCACC,SAAS;wCACT,UAAU,CAAC,gBAAgB;wCAC3B,WAAU;kDAET,YAAY,WAAW;;;;;;;;;;;;;;;;;;;;;;;gBAO/B,wBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAEvD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAoC,OAAO,UAAU;;;;;;sDACpE,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqC,OAAO,YAAY;;;;;;sDACvE,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAsC,OAAO,cAAc;;;;;;sDAC1E,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAmC,OAAO,UAAU;;;;;;sDACnE,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;wBAI1C,OAAO,MAAM,CAAC,MAAM,GAAG,mBACtB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDACX,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC;;oDAAe;oDAAG;;+CAAV;;;;;;;;;;;;;;;;;;;;;sCAOnB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;AAEe,SAAS;IACtB,qBACE,8OAAC,4IAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC,gIAAA,CAAA,aAAU;YAAC,wBACV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;sBAItC,cAAA,8OAAC;;;;;;;;;;;;;;;AAIT", "debugId": null}}]}