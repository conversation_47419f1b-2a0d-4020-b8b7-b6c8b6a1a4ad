{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/import/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\n\ninterface Card {\n  id: string\n  name: string\n  type: string\n  bankName?: string\n  user: {\n    id: string\n    name: string\n  }\n}\n\ninterface User {\n  id: string\n  name: string\n}\n\ninterface ImportResult {\n  billImportId: string\n  success: boolean\n  totalCount: number\n  successCount: number\n  errorCount: number\n  duplicateCount: number\n  errors: string[]\n}\n\nexport default function ImportPage() {\n  const [users, setUsers] = useState<User[]>([])\n  const [cards, setCards] = useState<Card[]>([])\n  const [selectedUserId, setSelectedUserId] = useState('')\n  const [selectedCardId, setSelectedCardId] = useState('')\n  const [file, setFile] = useState<File | null>(null)\n  const [importing, setImporting] = useState(false)\n  const [result, setResult] = useState<ImportResult | null>(null)\n\n  useEffect(() => {\n    fetchUsers()\n    fetchCards()\n  }, [])\n\n  const fetchUsers = async () => {\n    try {\n      const response = await fetch('/api/users')\n      const data = await response.json()\n      if (data.success) {\n        setUsers(data.data)\n      }\n    } catch (error) {\n      console.error('Failed to fetch users:', error)\n    }\n  }\n\n  const fetchCards = async () => {\n    try {\n      const response = await fetch('/api/cards')\n      const data = await response.json()\n      if (data.success) {\n        setCards(data.data)\n      }\n    } catch (error) {\n      console.error('Failed to fetch cards:', error)\n    }\n  }\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFile = e.target.files?.[0]\n    if (selectedFile) {\n      setFile(selectedFile)\n    }\n  }\n\n  const handleImport = async () => {\n    if (!file || !selectedCardId || !selectedUserId) {\n      alert('请选择文件、用户和卡片')\n      return\n    }\n\n    setImporting(true)\n    setResult(null)\n\n    try {\n      const formData = new FormData()\n      formData.append('file', file)\n      formData.append('cardId', selectedCardId)\n      formData.append('userId', selectedUserId)\n\n      const response = await fetch('/api/bills/import', {\n        method: 'POST',\n        body: formData,\n      })\n\n      const data = await response.json()\n      \n      if (data.success) {\n        setResult(data.data)\n      } else {\n        alert(`导入失败: ${data.error}`)\n      }\n    } catch (error) {\n      console.error('Import failed:', error)\n      alert('导入失败，请重试')\n    } finally {\n      setImporting(false)\n    }\n  }\n\n  const filteredCards = selectedUserId \n    ? cards.filter(card => card.user.id === selectedUserId)\n    : cards\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"bg-white shadow\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">账单导入</h1>\n            <Link\n              href=\"/dashboard\"\n              className=\"text-blue-600 hover:text-blue-800 transition-colors\"\n            >\n              返回仪表板\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"max-w-2xl mx-auto\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-lg font-medium text-gray-900 mb-6\">导入账单文件</h2>\n            \n            <div className=\"space-y-6\">\n              {/* 用户选择 */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  选择用户\n                </label>\n                <select\n                  value={selectedUserId}\n                  onChange={(e) => {\n                    setSelectedUserId(e.target.value)\n                    setSelectedCardId('') // 重置卡片选择\n                  }}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"\">请选择用户</option>\n                  {users.map((user) => (\n                    <option key={user.id} value={user.id}>\n                      {user.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* 卡片选择 */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  选择卡片\n                </label>\n                <select\n                  value={selectedCardId}\n                  onChange={(e) => setSelectedCardId(e.target.value)}\n                  disabled={!selectedUserId}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100\"\n                >\n                  <option value=\"\">请选择卡片</option>\n                  {filteredCards.map((card) => (\n                    <option key={card.id} value={card.id}>\n                      {card.name} ({card.type}) {card.bankName && `- ${card.bankName}`}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* 文件选择 */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  选择账单文件\n                </label>\n                <input\n                  type=\"file\"\n                  accept=\".csv,.xlsx,.xls\"\n                  onChange={handleFileChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n                <p className=\"mt-1 text-sm text-gray-500\">\n                  支持 CSV、Excel 格式，文件大小不超过 10MB\n                </p>\n              </div>\n\n              {/* 导入按钮 */}\n              <div>\n                <button\n                  onClick={handleImport}\n                  disabled={!file || !selectedCardId || !selectedUserId || importing}\n                  className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\"\n                >\n                  {importing ? '导入中...' : '开始导入'}\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* 导入结果 */}\n          {result && (\n            <div className=\"mt-6 bg-white rounded-lg shadow p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">导入结果</h3>\n              \n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-600\">{result.totalCount}</div>\n                  <div className=\"text-sm text-gray-500\">总记录数</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-600\">{result.successCount}</div>\n                  <div className=\"text-sm text-gray-500\">成功导入</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-yellow-600\">{result.duplicateCount}</div>\n                  <div className=\"text-sm text-gray-500\">重复跳过</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-red-600\">{result.errorCount}</div>\n                  <div className=\"text-sm text-gray-500\">导入失败</div>\n                </div>\n              </div>\n\n              {result.errors.length > 0 && (\n                <div>\n                  <h4 className=\"text-md font-medium text-gray-900 mb-2\">错误信息</h4>\n                  <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n                    <ul className=\"text-sm text-red-700 space-y-1\">\n                      {result.errors.map((error, index) => (\n                        <li key={index}>• {error}</li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              )}\n\n              <div className=\"mt-4 flex space-x-4\">\n                <Link\n                  href=\"/dashboard\"\n                  className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\"\n                >\n                  查看交易记录\n                </Link>\n                <button\n                  onClick={() => {\n                    setResult(null)\n                    setFile(null)\n                    const fileInput = document.querySelector('input[type=\"file\"]') as HTMLInputElement\n                    if (fileInput) fileInput.value = ''\n                  }}\n                  className=\"bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors\"\n                >\n                  继续导入\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AA+Be,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAE1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS,KAAK,IAAI;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS,KAAK,IAAI;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACxC,IAAI,cAAc;YAChB,QAAQ;QACV;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,gBAAgB;YAC/C,MAAM;YACN;QACF;QAEA,aAAa;QACb,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,UAAU;YAC1B,SAAS,MAAM,CAAC,UAAU;YAE1B,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,UAAU,KAAK,IAAI;YACrB,OAAO;gBACL,MAAM,CAAC,MAAM,EAAE,KAAK,KAAK,EAAE;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,iBAClB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,EAAE,KAAK,kBACtC;IAEJ,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAOP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAEvD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC;wDACT,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDAChC,kBAAkB,IAAI,SAAS;;oDACjC;oDACA,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;gEAAqB,OAAO,KAAK,EAAE;0EACjC,KAAK,IAAI;+DADC,KAAK,EAAE;;;;;;;;;;;;;;;;;sDAQ1B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,UAAU,CAAC;oDACX,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;gEAAqB,OAAO,KAAK,EAAE;;oEACjC,KAAK,IAAI;oEAAC;oEAAG,KAAK,IAAI;oEAAC;oEAAG,KAAK,QAAQ,IAAI,CAAC,EAAE,EAAE,KAAK,QAAQ,EAAE;;+DADrD,KAAK,EAAE;;;;;;;;;;;;;;;;;sDAQ1B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,QAAO;oDACP,UAAU;oDACV,WAAU;;;;;;8DAEZ,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAM5C,8OAAC;sDACC,cAAA,8OAAC;gDACC,SAAS;gDACT,UAAU,CAAC,QAAQ,CAAC,kBAAkB,CAAC,kBAAkB;gDACzD,WAAU;0DAET,YAAY,WAAW;;;;;;;;;;;;;;;;;;;;;;;wBAO/B,wBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAEvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoC,OAAO,UAAU;;;;;;8DACpE,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAqC,OAAO,YAAY;;;;;;8DACvE,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAsC,OAAO,cAAc;;;;;;8DAC1E,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAmC,OAAO,UAAU;;;;;;8DACnE,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;gCAI1C,OAAO,MAAM,CAAC,MAAM,GAAG,mBACtB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DACX,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC;;4DAAe;4DAAG;;uDAAV;;;;;;;;;;;;;;;;;;;;;8CAOnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;gDACP,UAAU;gDACV,QAAQ;gDACR,MAAM,YAAY,SAAS,aAAa,CAAC;gDACzC,IAAI,WAAW,UAAU,KAAK,GAAG;4CACnC;4CACA,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}