{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { useRouter } from 'next/navigation'\n\ninterface User {\n  id: string\n  email: string\n  name: string\n  avatar?: string\n  familyId?: string\n  familyRole?: string\n  family?: {\n    id: string\n    name: string\n    role: string\n  }\n}\n\ninterface AuthState {\n  user: User | null\n  token: string | null\n  loading: boolean\n  isAuthenticated: boolean\n}\n\nexport function useAuth() {\n  const router = useRouter()\n  const [authState, setAuthState] = useState<AuthState>({\n    user: null,\n    token: null,\n    loading: true,\n    isAuthenticated: false,\n  })\n\n  // 定义logout函数（需要在useEffect之前定义以避免循环依赖）\n  const logout = useCallback(() => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('token')\n      localStorage.removeItem('user')\n    }\n\n    setAuthState({\n      user: null,\n      token: null,\n      loading: false,\n      isAuthenticated: false,\n    })\n\n    router.push('/auth/login')\n  }, [router])\n\n  useEffect(() => {\n    // 确保只在客户端执行\n    if (typeof window === 'undefined') {\n      setAuthState(prev => ({ ...prev, loading: false }))\n      return\n    }\n\n    // 从localStorage获取认证信息\n    const token = localStorage.getItem('token')\n    const userStr = localStorage.getItem('user')\n\n    if (token && userStr) {\n      try {\n        const user = JSON.parse(userStr)\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n      } catch (error) {\n        console.error('Failed to parse user data:', error)\n        logout()\n      }\n    } else {\n      setAuthState(prev => ({ ...prev, loading: false }))\n    }\n  }, [logout])\n\n  const login = async (email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        const { user, token } = data.data\n\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', token)\n          localStorage.setItem('user', JSON.stringify(user))\n        }\n\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n\n        return { success: true }\n      } else {\n        return { success: false, error: data.error }\n      }\n    } catch (error) {\n      console.error('Login error:', error)\n      return { success: false, error: '网络错误，请重试' }\n    }\n  }\n\n  const register = async (name: string, email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ name, email, password }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        const { user, token } = data.data\n\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', token)\n          localStorage.setItem('user', JSON.stringify(user))\n        }\n\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n\n        return { success: true }\n      } else {\n        return { success: false, error: data.error }\n      }\n    } catch (error) {\n      console.error('Register error:', error)\n      return { success: false, error: '网络错误，请重试' }\n    }\n  }\n\n\n\n  const updateUser = (updatedUser: Partial<User>) => {\n    if (authState.user) {\n      const newUser = { ...authState.user, ...updatedUser }\n      if (typeof window !== 'undefined') {\n        localStorage.setItem('user', JSON.stringify(newUser))\n      }\n      setAuthState(prev => ({\n        ...prev,\n        user: newUser,\n      }))\n    }\n  }\n\n  const getAuthHeaders = useCallback(() => {\n    if (authState.token) {\n      return {\n        'Authorization': `Bearer ${authState.token}`,\n        'Content-Type': 'application/json',\n      }\n    }\n    return {\n      'Content-Type': 'application/json',\n    }\n  }, [authState.token])\n\n  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {\n    const headers = {\n      ...getAuthHeaders(),\n      ...options.headers,\n    }\n\n    const response = await fetch(url, {\n      ...options,\n      headers,\n    })\n\n    if (response.status === 401) {\n      // Token过期或无效，自动登出\n      logout()\n      throw new Error('认证失败，请重新登录')\n    }\n\n    return response\n  }, [getAuthHeaders, logout])\n\n  return {\n    ...authState,\n    login,\n    register,\n    logout,\n    updateUser,\n    getAuthHeaders,\n    apiCall,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AA0BO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM;QACN,OAAO;QACP,SAAS;QACT,iBAAiB;IACnB;IAEA,sCAAsC;IACtC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzB,uCAAmC;;QAGnC;QAEA,aAAa;YACX,MAAM;YACN,OAAO;YACP,SAAS;YACT,iBAAiB;QACnB;QAEA,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;QACZ,wCAAmC;YACjC,aAAa,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;gBAAM,CAAC;YACjD;QACF;;QAEA,sBAAsB;QACtB,MAAM;QACN,MAAM;IAkBR,GAAG;QAAC;KAAO;IAEX,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI;gBAEjC,uCAAmC;;gBAGnC;gBAEA,aAAa;oBACX;oBACA;oBACA,SAAS;oBACT,iBAAiB;gBACnB;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK;gBAAC;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;IAEA,MAAM,WAAW,OAAO,MAAc,OAAe;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;oBAAO;gBAAS;YAC/C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI;gBAEjC,uCAAmC;;gBAGnC;gBAEA,aAAa;oBACX;oBACA;oBACA,SAAS;oBACT,iBAAiB;gBACnB;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK;gBAAC;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;IAIA,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU,IAAI,EAAE;YAClB,MAAM,UAAU;gBAAE,GAAG,UAAU,IAAI;gBAAE,GAAG,WAAW;YAAC;YACpD,uCAAmC;;YAEnC;YACA,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,MAAM;gBACR,CAAC;QACH;IACF;IAEA,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,UAAU,KAAK,EAAE;YACnB,OAAO;gBACL,iBAAiB,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE;gBAC5C,gBAAgB;YAClB;QACF;QACA,OAAO;YACL,gBAAgB;QAClB;IACF,GAAG;QAAC,UAAU,KAAK;KAAC;IAEpB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,KAAa,UAAuB,CAAC,CAAC;QACvE,MAAM,UAAU;YACd,GAAG,gBAAgB;YACnB,GAAG,QAAQ,OAAO;QACpB;QAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV;QACF;QAEA,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,kBAAkB;YAClB;YACA,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,GAAG;QAAC;QAAgB;KAAO;IAE3B,OAAO;QACL,GAAG,SAAS;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface ClientOnlyProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n}\n\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  const [hasMounted, setHasMounted] = useState(false)\n\n  useEffect(() => {\n    setHasMounted(true)\n  }, [])\n\n  if (!hasMounted) {\n    return <>{fallback}</>\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/hooks/useAuth'\nimport { ClientOnly } from '@/components/ClientOnly'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requireFamily?: boolean // 是否需要用户已加入家庭\n  requireFamilyAdmin?: boolean // 是否需要家庭管理员权限\n}\n\nconst LoadingSpinner = () => (\n  <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n    <div className=\"text-center\">\n      <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      <p className=\"mt-4 text-gray-600\">加载中...</p>\n    </div>\n  </div>\n)\n\nfunction ProtectedRouteContent({\n  children,\n  requireFamily = false,\n  requireFamilyAdmin = false\n}: ProtectedRouteProps) {\n  const router = useRouter()\n  const { user, loading, isAuthenticated } = useAuth()\n\n  useEffect(() => {\n    if (!loading) {\n      if (!isAuthenticated) {\n        router.push('/auth/login')\n        return\n      }\n\n      if (requireFamily && !user?.familyId) {\n        router.push('/family')\n        return\n      }\n\n      if (requireFamilyAdmin && user?.familyRole !== 'OWNER' && user?.familyRole !== 'ADMIN') {\n        router.push('/dashboard')\n        return\n      }\n    }\n  }, [loading, isAuthenticated, user, router, requireFamily, requireFamilyAdmin])\n\n  if (loading) {\n    return <LoadingSpinner />\n  }\n\n  if (!isAuthenticated) {\n    return null // 将重定向到登录页面\n  }\n\n  if (requireFamily && !user?.familyId) {\n    return null // 将重定向到家庭页面\n  }\n\n  if (requireFamilyAdmin && user?.familyRole !== 'OWNER' && user?.familyRole !== 'ADMIN') {\n    return null // 将重定向到仪表板\n  }\n\n  return <>{children}</>\n}\n\nexport function ProtectedRoute(props: ProtectedRouteProps) {\n  return (\n    <ClientOnly fallback={<LoadingSpinner />}>\n      <ProtectedRouteContent {...props} />\n    </ClientOnly>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAaA,MAAM,iBAAiB,kBACrB,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;;;;;;;;;;;;AAKxC,SAAS,sBAAsB,EAC7B,QAAQ,EACR,gBAAgB,KAAK,EACrB,qBAAqB,KAAK,EACN;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;YACZ,IAAI,CAAC,iBAAiB;gBACpB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,iBAAiB,CAAC,MAAM,UAAU;gBACpC,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,sBAAsB,MAAM,eAAe,WAAW,MAAM,eAAe,SAAS;gBACtF,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;IACF,GAAG;QAAC;QAAS;QAAiB;QAAM;QAAQ;QAAe;KAAmB;IAE9E,IAAI,SAAS;QACX,qBAAO,8OAAC;;;;;IACV;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,KAAK,YAAY;;IAC1B;IAEA,IAAI,iBAAiB,CAAC,MAAM,UAAU;QACpC,OAAO,KAAK,YAAY;;IAC1B;IAEA,IAAI,sBAAsB,MAAM,eAAe,WAAW,MAAM,eAAe,SAAS;QACtF,OAAO,KAAK,WAAW;;IACzB;IAEA,qBAAO;kBAAG;;AACZ;AAEO,SAAS,eAAe,KAA0B;IACvD,qBACE,8OAAC,gIAAA,CAAA,aAAU;QAAC,wBAAU,8OAAC;;;;;kBACrB,cAAA,8OAAC;YAAuB,GAAG,KAAK;;;;;;;;;;;AAGtC", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency = 'CNY'): string {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n  }).format(amount)\n}\n\nexport function formatDate(date: Date | string, format: 'short' | 'long' | 'time' = 'short'): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  \n  switch (format) {\n    case 'short':\n      return d.toLocaleDateString('zh-CN')\n    case 'long':\n      return d.toLocaleDateString('zh-CN', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        weekday: 'long',\n      })\n    case 'time':\n      return d.toLocaleString('zh-CN')\n    default:\n      return d.toLocaleDateString('zh-CN')\n  }\n}\n\nexport function formatRelativeTime(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) {\n    return '刚刚'\n  } else if (diffInSeconds < 3600) {\n    return `${Math.floor(diffInSeconds / 60)}分钟前`\n  } else if (diffInSeconds < 86400) {\n    return `${Math.floor(diffInSeconds / 3600)}小时前`\n  } else if (diffInSeconds < 2592000) {\n    return `${Math.floor(diffInSeconds / 86400)}天前`\n  } else {\n    return formatDate(d)\n  }\n}\n\nexport function getTransactionTypeLabel(type: string): string {\n  const labels: Record<string, string> = {\n    INCOME: '收入',\n    EXPENSE: '支出',\n    TRANSFER: '转账',\n  }\n  return labels[type] || type\n}\n\nexport function getCardTypeLabel(type: string): string {\n  const labels: Record<string, string> = {\n    BANK_CARD: '银行卡',\n    CREDIT_CARD: '信用卡',\n    WECHAT: '微信',\n    ALIPAY: '支付宝',\n    CASH: '现金',\n    OTHER: '其他',\n  }\n  return labels[type] || type\n}\n\nexport function getTransactionStatusLabel(status: string): string {\n  const labels: Record<string, string> = {\n    PENDING: '待确认',\n    CONFIRMED: '已确认',\n    CANCELLED: '已取消',\n    LINKED: '已关联',\n  }\n  return labels[status] || status\n}\n\nexport function getStatusColor(status: string): string {\n  const colors: Record<string, string> = {\n    PENDING: 'bg-yellow-100 text-yellow-800',\n    CONFIRMED: 'bg-green-100 text-green-800',\n    CANCELLED: 'bg-red-100 text-red-800',\n    LINKED: 'bg-blue-100 text-blue-800',\n  }\n  return colors[status] || 'bg-gray-100 text-gray-800'\n}\n\nexport function getTypeColor(type: string): string {\n  const colors: Record<string, string> = {\n    INCOME: 'text-green-600',\n    EXPENSE: 'text-red-600',\n    TRANSFER: 'text-blue-600',\n  }\n  return colors[type] || 'text-gray-600'\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) {\n      clearTimeout(timeout)\n    }\n    \n    timeout = setTimeout(() => {\n      func(...args)\n    }, wait)\n  }\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB,EAAE,SAAoC,OAAO;IACzF,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAEtD,OAAQ;QACN,KAAK;YACH,OAAO,EAAE,kBAAkB,CAAC;QAC9B,KAAK;YACH,OAAO,EAAE,kBAAkB,CAAC,SAAS;gBACnC,MAAM;gBACN,OAAO;gBACP,KAAK;gBACL,SAAS;YACX;QACF,KAAK;YACH,OAAO,EAAE,cAAc,CAAC;QAC1B;YACE,OAAO,EAAE,kBAAkB,CAAC;IAChC;AACF;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,GAAG,CAAC;IAC/C,OAAO,IAAI,gBAAgB,OAAO;QAChC,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,GAAG,CAAC;IACjD,OAAO,IAAI,gBAAgB,SAAS;QAClC,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,EAAE,CAAC;IACjD,OAAO;QACL,OAAO,WAAW;IACpB;AACF;AAEO,SAAS,wBAAwB,IAAY;IAClD,MAAM,SAAiC;QACrC,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,SAAiC;QACrC,WAAW;QACX,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,OAAO;IACT;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,0BAA0B,MAAc;IACtD,MAAM,SAAiC;QACrC,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,SAAiC;QACrC,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAEO,SAAS,aAAa,IAAY;IACvC,MAAM,SAAiC;QACrC,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS;YACX,aAAa;QACf;QAEA,UAAU,WAAW;YACnB,QAAQ;QACV,GAAG;IACL;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n  children: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading = false, disabled, children, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    }\n    \n    const sizes = {\n      sm: 'h-8 px-3 text-sm',\n      md: 'h-10 px-4 text-sm',\n      lg: 'h-12 px-6 text-base',\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC/F,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/cards/CardForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/Button'\n\ninterface Card {\n  id: string\n  name: string\n  type: string\n  accountNo?: string\n  bankName?: string\n  balance: number\n  description?: string\n  isActive: boolean\n}\n\ninterface CardFormProps {\n  card?: Card | null\n  onSuccess: () => void\n  onCancel: () => void\n}\n\nconst CARD_TYPES = [\n  { value: 'DEBIT_CARD', label: '借记卡' },\n  { value: 'CREDIT_CARD', label: '信用卡' },\n  { value: 'WECHAT', label: '微信' },\n  { value: 'ALIPAY', label: '支付宝' },\n  { value: 'CASH', label: '现金' },\n  { value: 'OTHER', label: '其他' },\n]\n\nexport function CardForm({ card, onSuccess, onCancel }: CardFormProps) {\n  const { apiCall } = useAuth()\n  const [loading, setLoading] = useState(false)\n  const [formData, setFormData] = useState({\n    name: '',\n    type: 'DEBIT_CARD',\n    accountNo: '',\n    bankName: '',\n    balance: 0,\n    description: '',\n    isActive: true,\n  })\n\n  useEffect(() => {\n    if (card) {\n      setFormData({\n        name: card.name,\n        type: card.type,\n        accountNo: card.accountNo || '',\n        bankName: card.bankName || '',\n        balance: card.balance,\n        description: card.description || '',\n        isActive: card.isActive,\n      })\n    }\n  }, [card])\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n\n    try {\n      const url = card ? `/api/cards/${card.id}` : '/api/cards'\n      const method = card ? 'PUT' : 'POST'\n\n      const response = await apiCall(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      })\n\n      const data = await response.json()\n      if (data.success) {\n        alert(card ? '卡片更新成功' : '卡片创建成功')\n        onSuccess()\n      } else {\n        alert(data.error || '操作失败')\n      }\n    } catch (error) {\n      console.error('Form submission failed:', error)\n      alert('操作失败，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n    const { name, value, type } = e.target\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'number' ? parseFloat(value) || 0 : \n              type === 'checkbox' ? (e.target as HTMLInputElement).checked : value\n    }))\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\">\n        <h2 className=\"text-xl font-bold mb-4\">\n          {card ? '编辑卡片' : '添加卡片'}\n        </h2>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* 卡片名称 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              卡片名称 *\n            </label>\n            <input\n              type=\"text\"\n              name=\"name\"\n              value={formData.name}\n              onChange={handleChange}\n              required\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              placeholder=\"例如：招商银行储蓄卡\"\n            />\n          </div>\n\n          {/* 卡片类型 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              卡片类型 *\n            </label>\n            <select\n              name=\"type\"\n              value={formData.type}\n              onChange={handleChange}\n              required\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n            >\n              {CARD_TYPES.map(type => (\n                <option key={type.value} value={type.value}>\n                  {type.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* 账号 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              账号/卡号\n            </label>\n            <input\n              type=\"text\"\n              name=\"accountNo\"\n              value={formData.accountNo}\n              onChange={handleChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              placeholder=\"例如：****1234\"\n            />\n          </div>\n\n          {/* 银行名称 */}\n          {(formData.type === 'DEBIT_CARD' || formData.type === 'CREDIT_CARD') && (\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                银行名称\n              </label>\n              <input\n                type=\"text\"\n                name=\"bankName\"\n                value={formData.bankName}\n                onChange={handleChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n                placeholder=\"例如：招商银行\"\n              />\n            </div>\n          )}\n\n          {/* 余额 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              当前余额\n            </label>\n            <input\n              type=\"number\"\n              name=\"balance\"\n              value={formData.balance}\n              onChange={handleChange}\n              step=\"0.01\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              placeholder=\"0.00\"\n            />\n          </div>\n\n          {/* 描述 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              描述\n            </label>\n            <textarea\n              name=\"description\"\n              value={formData.description}\n              onChange={handleChange}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              placeholder=\"可选的描述信息\"\n            />\n          </div>\n\n          {/* 状态 */}\n          <div className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              name=\"isActive\"\n              checked={formData.isActive}\n              onChange={handleChange}\n              className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n            />\n            <label className=\"ml-2 block text-sm text-gray-700\">\n              启用此卡片\n            </label>\n          </div>\n\n          {/* 按钮 */}\n          <div className=\"flex space-x-3 pt-4\">\n            <Button\n              type=\"submit\"\n              disabled={loading}\n              className=\"flex-1\"\n            >\n              {loading ? '保存中...' : (card ? '更新' : '创建')}\n            </Button>\n            <Button\n              type=\"button\"\n              variant=\"secondary\"\n              onClick={onCancel}\n              disabled={loading}\n              className=\"flex-1\"\n            >\n              取消\n            </Button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAuBA,MAAM,aAAa;IACjB;QAAE,OAAO;QAAc,OAAO;IAAM;IACpC;QAAE,OAAO;QAAe,OAAO;IAAM;IACrC;QAAE,OAAO;QAAU,OAAO;IAAK;IAC/B;QAAE,OAAO;QAAU,OAAO;IAAM;IAChC;QAAE,OAAO;QAAQ,OAAO;IAAK;IAC7B;QAAE,OAAO;QAAS,OAAO;IAAK;CAC/B;AAEM,SAAS,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAiB;IACnE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,MAAM;QACN,WAAW;QACX,UAAU;QACV,SAAS;QACT,aAAa;QACb,UAAU;IACZ;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,YAAY;gBACV,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,WAAW,KAAK,SAAS,IAAI;gBAC7B,UAAU,KAAK,QAAQ,IAAI;gBAC3B,SAAS,KAAK,OAAO;gBACrB,aAAa,KAAK,WAAW,IAAI;gBACjC,UAAU,KAAK,QAAQ;YACzB;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,MAAM,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,GAAG;YAC7C,MAAM,SAAS,OAAO,QAAQ;YAE9B,MAAM,WAAW,MAAM,QAAQ,KAAK;gBAClC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,OAAO,WAAW;gBACxB;YACF,OAAO;gBACL,MAAM,KAAK,KAAK,IAAI;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,WAAW,WAAW,UAAU,IACzC,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;YACzE,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BACX,OAAO,SAAS;;;;;;8BAGnB,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU;oCACV,QAAQ;oCACR,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU;oCACV,QAAQ;oCACR,WAAU;8CAET,WAAW,GAAG,CAAC,CAAA,qBACd,8OAAC;4CAAwB,OAAO,KAAK,KAAK;sDACvC,KAAK,KAAK;2CADA,KAAK,KAAK;;;;;;;;;;;;;;;;sCAQ7B,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,SAAS;oCACzB,UAAU;oCACV,WAAU;oCACV,aAAY;;;;;;;;;;;;wBAKf,CAAC,SAAS,IAAI,KAAK,gBAAgB,SAAS,IAAI,KAAK,aAAa,mBACjE,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAMlB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,OAAO;oCACvB,UAAU;oCACV,MAAK;oCACL,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,WAAW;oCAC3B,UAAU;oCACV,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,SAAS,SAAS,QAAQ;oCAC1B,UAAU;oCACV,WAAU;;;;;;8CAEZ,8OAAC;oCAAM,WAAU;8CAAmC;;;;;;;;;;;;sCAMtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,WAAY,OAAO,OAAO;;;;;;8CAEvC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 960, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/cards/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute'\nimport { ClientOnly } from '@/components/ClientOnly'\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { CardGrid } from '@/components/cards/CardGrid'\nimport { CardForm } from '@/components/cards/CardForm'\n\ninterface Card {\n  id: string\n  name: string\n  type: string\n  accountNo?: string\n  bankName?: string\n  balance: number\n  isActive: boolean\n  createdAt: string\n  user: {\n    id: string\n    name: string\n  }\n  _count: {\n    transactions: number\n    billImports: number\n  }\n}\n\nfunction CardsPageContent() {\n  const { apiCall, isAuthenticated, loading: authLoading } = useAuth()\n  const [cards, setCards] = useState<Card[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showCreateForm, setShowCreateForm] = useState(false)\n  const [editingCard, setEditingCard] = useState<Card | null>(null)\n\n  useEffect(() => {\n    // 只有在认证完成且用户已登录时才获取卡片\n    if (!authLoading && isAuthenticated) {\n      fetchCards()\n    } else if (!authLoading && !isAuthenticated) {\n      setLoading(false)\n    }\n  }, [authLoading, isAuthenticated])\n\n  const fetchCards = async () => {\n    try {\n      setLoading(true)\n      const response = await apiCall('/api/cards')\n      const data = await response.json()\n\n      if (data.success) {\n        setCards(data.data)\n      } else {\n        console.error('Failed to fetch cards:', data.error)\n      }\n    } catch (error) {\n      console.error('Failed to fetch cards:', error)\n      // 如果是认证错误，用户会被自动重定向到登录页面\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleEdit = (card: Card) => {\n    setEditingCard(card)\n    setShowCreateForm(true)\n  }\n\n  const handleDelete = async (cardId: string) => {\n    if (!confirm('确定要删除这张卡片吗？')) return\n\n    try {\n      const response = await apiCall(`/api/cards/${cardId}`, {\n        method: 'DELETE',\n      })\n\n      const data = await response.json()\n      if (data.success) {\n        // 软删除：更新卡片状态为不活跃\n        setCards(prev => prev.map(card =>\n          card.id === cardId ? { ...card, isActive: false } : card\n        ))\n        alert('卡片删除成功')\n      } else {\n        alert(data.error || '删除失败')\n      }\n    } catch (error) {\n      console.error('Delete failed:', error)\n      alert('删除失败')\n    }\n  }\n\n  const handleToggleStatus = async (cardId: string, isActive: boolean) => {\n    try {\n      const response = await apiCall(`/api/cards/${cardId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ isActive }),\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        if (data.success) {\n          setCards(prev => prev.map(card => \n            card.id === cardId ? { ...card, isActive } : card\n          ))\n        }\n      } else {\n        alert('状态更新失败')\n      }\n    } catch (error) {\n      console.error('Toggle status failed:', error)\n      alert('状态更新失败')\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"bg-white shadow\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">卡片管理</h1>\n            <div className=\"flex space-x-4\">\n              <Link\n                href=\"/dashboard\"\n                className=\"text-blue-600 hover:text-blue-800 transition-colors\"\n              >\n                返回仪表板\n              </Link>\n              <Button\n                onClick={() => {\n                  setEditingCard(null)\n                  setShowCreateForm(true)\n                }}\n              >\n                添加卡片\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* 统计卡片 */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总卡片数</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">{cards.length}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">活跃卡片</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {cards.filter(card => card.isActive).length}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总交易数</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {cards.reduce((sum, card) => sum + card._count.transactions, 0)}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-yellow-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总导入数</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {cards.reduce((sum, card) => sum + card._count.billImports, 0)}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 卡片列表 */}\n        <CardList\n          cards={cards}\n          onEdit={handleEdit}\n          onDelete={handleDelete}\n          onToggleStatus={handleToggleStatus}\n        />\n      </div>\n\n      {/* 创建/编辑表单模态框 */}\n      {showCreateForm && (\n        <CardForm\n          card={editingCard}\n          onSuccess={() => {\n            setShowCreateForm(false)\n            setEditingCard(null)\n            fetchCards() // 重新获取卡片列表\n          }}\n          onCancel={() => {\n            setShowCreateForm(false)\n            setEditingCard(null)\n          }}\n        />\n      )}\n    </div>\n  )\n}\n\nexport default function CardsPage() {\n  return (\n    <ProtectedRoute>\n      <ClientOnly fallback={\n        <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n            <p className=\"mt-4 text-gray-600\">加载中...</p>\n          </div>\n        </div>\n      }>\n        <CardsPageContent />\n      </ClientOnly>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AATA;;;;;;;;;AA8BA,SAAS;IACP,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sBAAsB;QACtB,IAAI,CAAC,eAAe,iBAAiB;YACnC;QACF,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB;YAC3C,WAAW;QACb;IACF,GAAG;QAAC;QAAa;KAAgB;IAEjC,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,QAAQ;YAC/B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS,KAAK,IAAI;YACpB,OAAO;gBACL,QAAQ,KAAK,CAAC,0BAA0B,KAAK,KAAK;YACpD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,yBAAyB;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,kBAAkB;IACpB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,gBAAgB;QAE7B,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,CAAC,WAAW,EAAE,QAAQ,EAAE;gBACrD,QAAQ;YACV;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,iBAAiB;gBACjB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,SAAS;4BAAE,GAAG,IAAI;4BAAE,UAAU;wBAAM,IAAI;gBAEtD,MAAM;YACR,OAAO;gBACL,MAAM,KAAK,KAAK,IAAI;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB,OAAO,QAAgB;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,CAAC,WAAW,EAAE,QAAQ,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,SAAS;gCAAE,GAAG,IAAI;gCAAE;4BAAS,IAAI;gBAEjD;YACF,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;4CACP,eAAe;4CACf,kBAAkB;wCACpB;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQT,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAwC,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAKvE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAChF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAMnD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,CAAC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;0CAMrE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,CAAC,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQtE,8OAAC;wBACC,OAAO;wBACP,QAAQ;wBACR,UAAU;wBACV,gBAAgB;;;;;;;;;;;;YAKnB,gCACC,8OAAC,uIAAA,CAAA,WAAQ;gBACP,MAAM;gBACN,WAAW;oBACT,kBAAkB;oBAClB,eAAe;oBACf,aAAa,WAAW;;gBAC1B;gBACA,UAAU;oBACR,kBAAkB;oBAClB,eAAe;gBACjB;;;;;;;;;;;;AAKV;AAEe,SAAS;IACtB,qBACE,8OAAC,4IAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC,gIAAA,CAAA,aAAU;YAAC,wBACV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;sBAItC,cAAA,8OAAC;;;;;;;;;;;;;;;AAIT", "debugId": null}}]}