{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/hooks/useToast.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ToastProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/hooks/useToast.tsx <module evaluation>\",\n    \"ToastProvider\",\n);\nexport const useToast = registerClientReference(\n    function() { throw new Error(\"Attempted to call useToast() from the server but useToast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/hooks/useToast.tsx <module evaluation>\",\n    \"useToast\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,wDACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,wDACA", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/hooks/useToast.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ToastProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/hooks/useToast.tsx\",\n    \"ToastProvider\",\n);\nexport const useToast = registerClientReference(\n    function() { throw new Error(\"Attempted to call useToast() from the server but useToast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/hooks/useToast.tsx\",\n    \"useToast\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,oCACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,oCACA", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/hooks/useConfirm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ConfirmProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ConfirmProvider() from the server but ConfirmProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/hooks/useConfirm.tsx <module evaluation>\",\n    \"ConfirmProvider\",\n);\nexport const useConfirm = registerClientReference(\n    function() { throw new Error(\"Attempted to call useConfirm() from the server but useConfirm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/hooks/useConfirm.tsx <module evaluation>\",\n    \"useConfirm\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/hooks/useConfirm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ConfirmProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ConfirmProvider() from the server but ConfirmProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/hooks/useConfirm.tsx\",\n    \"ConfirmProvider\",\n);\nexport const useConfirm = registerClientReference(\n    function() { throw new Error(\"Attempted to call useConfirm() from the server but useConfirm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/hooks/useConfirm.tsx\",\n    \"useConfirm\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,sCACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,sCACA", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport { ToastProvider } from \"@/hooks/useToast\";\nimport { ConfirmProvider } from \"@/hooks/useConfirm\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"Create Next App\",\n  description: \"Generated by create next app\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased`}\n      >\n        <ToastProvider>\n          <ConfirmProvider>\n            {children}\n          </ConfirmProvider>\n        </ToastProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;;;;;;;AAYO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;sBAEpE,cAAA,8OAAC,yHAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC,2HAAA,CAAA,kBAAe;8BACb;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}