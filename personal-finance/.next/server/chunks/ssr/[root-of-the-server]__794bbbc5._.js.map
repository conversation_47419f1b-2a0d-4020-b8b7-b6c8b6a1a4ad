{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ui/Toast.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { CheckCircleIcon, ExclamationCircleIcon, XCircleIcon, InformationCircleIcon, XMarkIcon } from '@heroicons/react/24/outline'\n\nexport type ToastType = 'success' | 'error' | 'warning' | 'info'\n\nexport interface ToastMessage {\n  id: string\n  type: ToastType\n  title: string\n  message?: string\n  duration?: number\n}\n\ninterface ToastProps {\n  toast: ToastMessage\n  onClose: (id: string) => void\n}\n\nfunction Toast({ toast, onClose }: ToastProps) {\n  const { id, type, title, message, duration = 5000 } = toast\n\n  useEffect(() => {\n    if (duration > 0) {\n      const timer = setTimeout(() => {\n        onClose(id)\n      }, duration)\n\n      return () => clearTimeout(timer)\n    }\n  }, [id, duration, onClose])\n\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return <CheckCircleIcon className=\"h-6 w-6 text-green-400\" />\n      case 'error':\n        return <XCircleIcon className=\"h-6 w-6 text-red-400\" />\n      case 'warning':\n        return <ExclamationCircleIcon className=\"h-6 w-6 text-yellow-400\" />\n      case 'info':\n        return <InformationCircleIcon className=\"h-6 w-6 text-blue-400\" />\n    }\n  }\n\n  const getBackgroundColor = () => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200'\n      case 'error':\n        return 'bg-red-50 border-red-200'\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200'\n      case 'info':\n        return 'bg-blue-50 border-blue-200'\n    }\n  }\n\n  const getTextColor = () => {\n    switch (type) {\n      case 'success':\n        return 'text-green-800'\n      case 'error':\n        return 'text-red-800'\n      case 'warning':\n        return 'text-yellow-800'\n      case 'info':\n        return 'text-blue-800'\n    }\n  }\n\n  return (\n    <div className={`max-w-sm w-full ${getBackgroundColor()} border rounded-lg shadow-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden`}>\n      <div className=\"p-4\">\n        <div className=\"flex items-start\">\n          <div className=\"flex-shrink-0\">\n            {getIcon()}\n          </div>\n          <div className=\"ml-3 w-0 flex-1 pt-0.5\">\n            <p className={`text-sm font-medium ${getTextColor()}`}>\n              {title}\n            </p>\n            {message && (\n              <p className={`mt-1 text-sm ${getTextColor()} opacity-75`}>\n                {message}\n              </p>\n            )}\n          </div>\n          <div className=\"ml-4 flex-shrink-0 flex\">\n            <button\n              className={`rounded-md inline-flex ${getTextColor()} hover:opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500`}\n              onClick={() => onClose(id)}\n            >\n              <span className=\"sr-only\">关闭</span>\n              <XMarkIcon className=\"h-5 w-5\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\ninterface ToastContainerProps {\n  toasts: ToastMessage[]\n  onClose: (id: string) => void\n}\n\nexport function ToastContainer({ toasts, onClose }: ToastContainerProps) {\n  return (\n    <div\n      aria-live=\"assertive\"\n      className=\"fixed inset-0 flex items-end justify-center px-4 py-6 pointer-events-none sm:p-6 sm:items-start sm:justify-end z-50\"\n    >\n      <div className=\"w-full flex flex-col items-center space-y-4 sm:items-end\">\n        {toasts.map((toast) => (\n          <Toast key={toast.id} toast={toast} onClose={onClose} />\n        ))}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAoBA,SAAS,MAAM,EAAE,KAAK,EAAE,OAAO,EAAc;IAC3C,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,IAAI,EAAE,GAAG;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,GAAG;YAChB,MAAM,QAAQ,WAAW;gBACvB,QAAQ;YACV,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAI;QAAU;KAAQ;IAE1B,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,6NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,qNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,yOAAA,CAAA,wBAAqB;oBAAC,WAAU;;;;;;YAC1C,KAAK;gBACH,qBAAO,8OAAC,yOAAA,CAAA,wBAAqB;oBAAC,WAAU;;;;;;QAC5C;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,gBAAgB,EAAE,qBAAqB,iGAAiG,CAAC;kBACxJ,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAEH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAW,CAAC,oBAAoB,EAAE,gBAAgB;0CAClD;;;;;;4BAEF,yBACC,8OAAC;gCAAE,WAAW,CAAC,aAAa,EAAE,eAAe,WAAW,CAAC;0CACtD;;;;;;;;;;;;kCAIP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAW,CAAC,uBAAuB,EAAE,eAAe,2FAA2F,CAAC;4BAChJ,SAAS,IAAM,QAAQ;;8CAEvB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnC;AAOO,SAAS,eAAe,EAAE,MAAM,EAAE,OAAO,EAAuB;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;sBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;oBAAqB,OAAO;oBAAO,SAAS;mBAAjC,MAAM,EAAE;;;;;;;;;;;;;;;AAK9B", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/hooks/useToast.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useCallback, ReactNode } from 'react'\nimport { ToastContainer, ToastMessage, ToastType } from '@/components/ui/Toast'\n\ninterface ToastContextType {\n  showToast: (type: ToastType, title: string, message?: string, duration?: number) => void\n  showSuccess: (title: string, message?: string) => void\n  showError: (title: string, message?: string) => void\n  showWarning: (title: string, message?: string) => void\n  showInfo: (title: string, message?: string) => void\n  removeToast: (id: string) => void\n}\n\nconst ToastContext = createContext<ToastContextType | undefined>(undefined)\n\nexport function useToast() {\n  const context = useContext(ToastContext)\n  if (context === undefined) {\n    throw new Error('useToast must be used within a ToastProvider')\n  }\n  return context\n}\n\ninterface ToastProviderProps {\n  children: ReactNode\n}\n\nexport function ToastProvider({ children }: ToastProviderProps) {\n  const [toasts, setToasts] = useState<ToastMessage[]>([])\n\n  const removeToast = useCallback((id: string) => {\n    setToasts((prev) => prev.filter((toast) => toast.id !== id))\n  }, [])\n\n  const showToast = useCallback((\n    type: ToastType,\n    title: string,\n    message?: string,\n    duration?: number\n  ) => {\n    const id = Math.random().toString(36).substr(2, 9)\n    const newToast: ToastMessage = {\n      id,\n      type,\n      title,\n      message,\n      duration,\n    }\n\n    setToasts((prev) => [...prev, newToast])\n  }, [])\n\n  const showSuccess = useCallback((title: string, message?: string) => {\n    showToast('success', title, message)\n  }, [showToast])\n\n  const showError = useCallback((title: string, message?: string) => {\n    showToast('error', title, message)\n  }, [showToast])\n\n  const showWarning = useCallback((title: string, message?: string) => {\n    showToast('warning', title, message)\n  }, [showToast])\n\n  const showInfo = useCallback((title: string, message?: string) => {\n    showToast('info', title, message)\n  }, [showToast])\n\n  const value = {\n    showToast,\n    showSuccess,\n    showError,\n    showWarning,\n    showInfo,\n    removeToast,\n  }\n\n  return (\n    <ToastContext.Provider value={value}>\n      {children}\n      <ToastContainer toasts={toasts} onClose={removeToast} />\n    </ToastContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAcA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAEvD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,UAAU,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK;IAC1D,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAC5B,MACA,OACA,SACA;QAEA,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,WAAyB;YAC7B;YACA;YACA;YACA;YACA;QACF;QAEA,UAAU,CAAC,OAAS;mBAAI;gBAAM;aAAS;IACzC,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC9C,UAAU,WAAW,OAAO;IAC9B,GAAG;QAAC;KAAU;IAEd,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC5C,UAAU,SAAS,OAAO;IAC5B,GAAG;QAAC;KAAU;IAEd,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC9C,UAAU,WAAW,OAAO;IAC9B,GAAG;QAAC;KAAU;IAEd,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC3C,UAAU,QAAQ,OAAO;IAC3B,GAAG;QAAC;KAAU;IAEd,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;;YAC3B;0BACD,8OAAC,iIAAA,CAAA,iBAAc;gBAAC,QAAQ;gBAAQ,SAAS;;;;;;;;;;;;AAG/C", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency = 'CNY'): string {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n  }).format(amount)\n}\n\nexport function formatDate(date: Date | string, format: 'short' | 'long' | 'time' = 'short'): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  \n  switch (format) {\n    case 'short':\n      return d.toLocaleDateString('zh-CN')\n    case 'long':\n      return d.toLocaleDateString('zh-CN', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        weekday: 'long',\n      })\n    case 'time':\n      return d.toLocaleString('zh-CN')\n    default:\n      return d.toLocaleDateString('zh-CN')\n  }\n}\n\nexport function formatRelativeTime(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) {\n    return '刚刚'\n  } else if (diffInSeconds < 3600) {\n    return `${Math.floor(diffInSeconds / 60)}分钟前`\n  } else if (diffInSeconds < 86400) {\n    return `${Math.floor(diffInSeconds / 3600)}小时前`\n  } else if (diffInSeconds < 2592000) {\n    return `${Math.floor(diffInSeconds / 86400)}天前`\n  } else {\n    return formatDate(d)\n  }\n}\n\nexport function getTransactionTypeLabel(type: string): string {\n  const labels: Record<string, string> = {\n    INCOME: '收入',\n    EXPENSE: '支出',\n    TRANSFER: '转账',\n  }\n  return labels[type] || type\n}\n\nexport function getCardTypeLabel(type: string): string {\n  const labels: Record<string, string> = {\n    BANK_CARD: '银行卡',\n    CREDIT_CARD: '信用卡',\n    WECHAT: '微信',\n    ALIPAY: '支付宝',\n    CASH: '现金',\n    OTHER: '其他',\n  }\n  return labels[type] || type\n}\n\nexport function getTransactionStatusLabel(status: string): string {\n  const labels: Record<string, string> = {\n    PENDING: '待确认',\n    CONFIRMED: '已确认',\n    CANCELLED: '已取消',\n    LINKED: '已关联',\n  }\n  return labels[status] || status\n}\n\nexport function getStatusColor(status: string): string {\n  const colors: Record<string, string> = {\n    PENDING: 'bg-yellow-100 text-yellow-800',\n    CONFIRMED: 'bg-green-100 text-green-800',\n    CANCELLED: 'bg-red-100 text-red-800',\n    LINKED: 'bg-blue-100 text-blue-800',\n  }\n  return colors[status] || 'bg-gray-100 text-gray-800'\n}\n\nexport function getTypeColor(type: string): string {\n  const colors: Record<string, string> = {\n    INCOME: 'text-green-600',\n    EXPENSE: 'text-red-600',\n    TRANSFER: 'text-blue-600',\n  }\n  return colors[type] || 'text-gray-600'\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) {\n      clearTimeout(timeout)\n    }\n    \n    timeout = setTimeout(() => {\n      func(...args)\n    }, wait)\n  }\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB,EAAE,SAAoC,OAAO;IACzF,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAEtD,OAAQ;QACN,KAAK;YACH,OAAO,EAAE,kBAAkB,CAAC;QAC9B,KAAK;YACH,OAAO,EAAE,kBAAkB,CAAC,SAAS;gBACnC,MAAM;gBACN,OAAO;gBACP,KAAK;gBACL,SAAS;YACX;QACF,KAAK;YACH,OAAO,EAAE,cAAc,CAAC;QAC1B;YACE,OAAO,EAAE,kBAAkB,CAAC;IAChC;AACF;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,GAAG,CAAC;IAC/C,OAAO,IAAI,gBAAgB,OAAO;QAChC,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,GAAG,CAAC;IACjD,OAAO,IAAI,gBAAgB,SAAS;QAClC,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,EAAE,CAAC;IACjD,OAAO;QACL,OAAO,WAAW;IACpB;AACF;AAEO,SAAS,wBAAwB,IAAY;IAClD,MAAM,SAAiC;QACrC,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,SAAiC;QACrC,WAAW;QACX,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,OAAO;IACT;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,0BAA0B,MAAc;IACtD,MAAM,SAAiC;QACrC,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,SAAiC;QACrC,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAEO,SAAS,aAAa,IAAY;IACvC,MAAM,SAAiC;QACrC,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS;YACX,aAAa;QACf;QAEA,UAAU,WAAW;YACnB,QAAQ;QACV,GAAG;IACL;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n  children: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading = false, disabled, children, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    }\n    \n    const sizes = {\n      sm: 'h-8 px-3 text-sm',\n      md: 'h-10 px-4 text-sm',\n      lg: 'h-12 px-6 text-base',\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC/F,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ui/ConfirmDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport { ExclamationTriangleIcon } from '@heroicons/react/24/outline'\nimport { Button } from './Button'\n\ninterface ConfirmDialogProps {\n  isOpen: boolean\n  onClose: () => void\n  onConfirm: () => void\n  title: string\n  message: string\n  confirmText?: string\n  cancelText?: string\n  type?: 'danger' | 'warning' | 'info'\n}\n\nexport function ConfirmDialog({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  message,\n  confirmText = '确认',\n  cancelText = '取消',\n  type = 'danger'\n}: ConfirmDialogProps) {\n  const handleConfirm = () => {\n    onConfirm()\n    onClose()\n  }\n\n  const getIconColor = () => {\n    switch (type) {\n      case 'danger':\n        return 'text-red-600'\n      case 'warning':\n        return 'text-yellow-600'\n      case 'info':\n        return 'text-blue-600'\n      default:\n        return 'text-red-600'\n    }\n  }\n\n  const getConfirmButtonVariant = () => {\n    switch (type) {\n      case 'danger':\n        return 'destructive'\n      case 'warning':\n        return 'default'\n      case 'info':\n        return 'default'\n      default:\n        return 'destructive'\n    }\n  }\n\n  return (\n    <Transition appear show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={onClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-black bg-opacity-25\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 overflow-y-auto\">\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 scale-95\"\n              enterTo=\"opacity-100 scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 scale-100\"\n              leaveTo=\"opacity-0 scale-95\"\n            >\n              <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\n                <div className=\"flex items-center\">\n                  <div className={`mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10`}>\n                    <ExclamationTriangleIcon\n                      className={`h-6 w-6 ${getIconColor()}`}\n                      aria-hidden=\"true\"\n                    />\n                  </div>\n                  <div className=\"ml-4\">\n                    <Dialog.Title\n                      as=\"h3\"\n                      className=\"text-lg font-medium leading-6 text-gray-900\"\n                    >\n                      {title}\n                    </Dialog.Title>\n                  </div>\n                </div>\n\n                <div className=\"mt-4\">\n                  <p className=\"text-sm text-gray-500\">\n                    {message}\n                  </p>\n                </div>\n\n                <div className=\"mt-6 flex space-x-3 justify-end\">\n                  <Button\n                    variant=\"outline\"\n                    onClick={onClose}\n                  >\n                    {cancelText}\n                  </Button>\n                  <Button\n                    variant={getConfirmButtonVariant() as any}\n                    onClick={handleConfirm}\n                  >\n                    {confirmText}\n                  </Button>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAkBO,SAAS,cAAc,EAC5B,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,cAAc,IAAI,EAClB,aAAa,IAAI,EACjB,OAAO,QAAQ,EACI;IACnB,MAAM,gBAAgB;QACpB;QACA;IACF;IAEA,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,0BAA0B;QAC9B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU;QAAC,MAAM;QAAC,MAAM;QAAQ,IAAI,qMAAA,CAAA,WAAQ;kBAC3C,cAAA,8OAAC,+KAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,qMAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAU;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,gHAAgH,CAAC;0DAChI,cAAA,8OAAC,6OAAA,CAAA,0BAAuB;oDACtB,WAAW,CAAC,QAAQ,EAAE,gBAAgB;oDACtC,eAAY;;;;;;;;;;;0DAGhB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;oDACX,IAAG;oDACH,WAAU;8DAET;;;;;;;;;;;;;;;;;kDAKP,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDACV;;;;;;;;;;;kDAIL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS;0DAER;;;;;;0DAEH,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,SAAS;0DAER;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrB", "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/hooks/useConfirm.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useCallback, ReactNode } from 'react'\nimport { ConfirmDialog } from '@/components/ui/ConfirmDialog'\n\ninterface ConfirmOptions {\n  title: string\n  message: string\n  confirmText?: string\n  cancelText?: string\n  type?: 'danger' | 'warning' | 'info'\n}\n\ninterface ConfirmContextType {\n  confirm: (options: ConfirmOptions) => Promise<boolean>\n}\n\nconst ConfirmContext = createContext<ConfirmContextType | undefined>(undefined)\n\nexport function useConfirm() {\n  const context = useContext(ConfirmContext)\n  if (context === undefined) {\n    throw new Error('useConfirm must be used within a ConfirmProvider')\n  }\n  return context\n}\n\ninterface ConfirmProviderProps {\n  children: ReactNode\n}\n\ninterface ConfirmState extends ConfirmOptions {\n  isOpen: boolean\n  resolve?: (value: boolean) => void\n}\n\nexport function ConfirmProvider({ children }: ConfirmProviderProps) {\n  const [confirmState, setConfirmState] = useState<ConfirmState>({\n    isOpen: false,\n    title: '',\n    message: '',\n  })\n\n  const confirm = useCallback((options: ConfirmOptions): Promise<boolean> => {\n    return new Promise((resolve) => {\n      setConfirmState({\n        ...options,\n        isOpen: true,\n        resolve,\n      })\n    })\n  }, [])\n\n  const handleClose = useCallback(() => {\n    setConfirmState((prev) => ({\n      ...prev,\n      isOpen: false,\n    }))\n    if (confirmState.resolve) {\n      confirmState.resolve(false)\n    }\n  }, [confirmState.resolve])\n\n  const handleConfirm = useCallback(() => {\n    if (confirmState.resolve) {\n      confirmState.resolve(true)\n    }\n  }, [confirmState.resolve])\n\n  const value = {\n    confirm,\n  }\n\n  return (\n    <ConfirmContext.Provider value={value}>\n      {children}\n      <ConfirmDialog\n        isOpen={confirmState.isOpen}\n        onClose={handleClose}\n        onConfirm={handleConfirm}\n        title={confirmState.title}\n        message={confirmState.message}\n        confirmText={confirmState.confirmText}\n        cancelText={confirmState.cancelText}\n        type={confirmState.type}\n      />\n    </ConfirmContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAiBA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAkC;AAE9D,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAWO,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,QAAQ;QACR,OAAO;QACP,SAAS;IACX;IAEA,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3B,OAAO,IAAI,QAAQ,CAAC;YAClB,gBAAgB;gBACd,GAAG,OAAO;gBACV,QAAQ;gBACR;YACF;QACF;IACF,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,gBAAgB,CAAC,OAAS,CAAC;gBACzB,GAAG,IAAI;gBACP,QAAQ;YACV,CAAC;QACD,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC;QACvB;IACF,GAAG;QAAC,aAAa,OAAO;KAAC;IAEzB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC;QACvB;IACF,GAAG;QAAC,aAAa,OAAO;KAAC;IAEzB,MAAM,QAAQ;QACZ;IACF;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;;YAC7B;0BACD,8OAAC,yIAAA,CAAA,gBAAa;gBACZ,QAAQ,aAAa,MAAM;gBAC3B,SAAS;gBACT,WAAW;gBACX,OAAO,aAAa,KAAK;gBACzB,SAAS,aAAa,OAAO;gBAC7B,aAAa,aAAa,WAAW;gBACrC,YAAY,aAAa,UAAU;gBACnC,MAAM,aAAa,IAAI;;;;;;;;;;;;AAI/B", "debugId": null}}]}