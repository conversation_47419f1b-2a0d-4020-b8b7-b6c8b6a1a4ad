{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/api-response.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport interface ApiResponse<T = any> {\n  success: boolean\n  data?: T\n  error?: string\n  message?: string\n  pagination?: {\n    page: number\n    limit: number\n    total: number\n    totalPages: number\n  }\n}\n\nexport class ApiError extends Error {\n  constructor(\n    message: string,\n    public statusCode: number = 500,\n    public code?: string\n  ) {\n    super(message)\n    this.name = 'ApiError'\n  }\n}\n\nexport function createSuccessResponse<T>(\n  data: T,\n  message?: string,\n  pagination?: ApiResponse<T>['pagination']\n): NextResponse<ApiResponse<T>> {\n  return NextResponse.json({\n    success: true,\n    data,\n    message,\n    pagination,\n  })\n}\n\nexport function createErrorResponse(\n  error: string | Error,\n  statusCode: number = 500\n): NextResponse<ApiResponse> {\n  const message = error instanceof Error ? error.message : error\n  \n  return NextResponse.json(\n    {\n      success: false,\n      error: message,\n    },\n    { status: statusCode }\n  )\n}\n\nexport function handleApiError(error: unknown): NextResponse<ApiResponse> {\n  console.error('API Error:', error)\n  \n  if (error instanceof ApiError) {\n    return createErrorResponse(error.message, error.statusCode)\n  }\n  \n  if (error instanceof Error) {\n    return createErrorResponse(error.message, 500)\n  }\n  \n  return createErrorResponse('Internal server error', 500)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAeO,MAAM,iBAAiB;;;IAC5B,YACE,OAAe,EACf,AAAO,aAAqB,GAAG,EAC/B,AAAO,IAAa,CACpB;QACA,KAAK,CAAC,eAHC,aAAA,iBACA,OAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,SAAS,sBACd,IAAO,EACP,OAAgB,EAChB,UAAyC;IAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT;QACA;QACA;IACF;AACF;AAEO,SAAS,oBACd,KAAqB,EACrB,aAAqB,GAAG;IAExB,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAEzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT,OAAO;IACT,GACA;QAAE,QAAQ;IAAW;AAEzB;AAEO,SAAS,eAAe,KAAc;IAC3C,QAAQ,KAAK,CAAC,cAAc;IAE5B,IAAI,iBAAiB,UAAU;QAC7B,OAAO,oBAAoB,MAAM,OAAO,EAAE,MAAM,UAAU;IAC5D;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,oBAAoB,MAAM,OAAO,EAAE;IAC5C;IAEA,OAAO,oBAAoB,yBAAyB;AACtD", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { NextRequest } from 'next/server'\nimport { prisma } from './prisma'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'\nconst BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12')\n\nexport interface JWTPayload {\n  userId: string\n  email: string\n  familyId?: string\n  familyRole?: string\n}\n\nexport interface AuthUser {\n  id: string\n  email: string\n  name: string\n  avatar?: string\n  familyId?: string\n  familyRole?: string\n  family?: {\n    id: string\n    name: string\n    role: string\n  }\n}\n\n// 密码加密\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, BCRYPT_ROUNDS)\n}\n\n// 密码验证\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\n// 生成JWT令牌\nexport function generateToken(payload: JWTPayload): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })\n}\n\n// 验证JWT令牌\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload\n  } catch (error) {\n    return null\n  }\n}\n\n// 从请求中获取用户信息\nexport async function getUserFromRequest(request: NextRequest): Promise<AuthUser | null> {\n  try {\n    const authHeader = request.headers.get('authorization')\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return null\n    }\n\n    const token = authHeader.substring(7)\n    const payload = verifyToken(token)\n    if (!payload) {\n      return null\n    }\n\n    const user = await prisma.user.findUnique({\n      where: { id: payload.userId },\n      include: {\n        family: {\n          select: {\n            id: true,\n            name: true,\n          },\n        },\n      },\n    })\n\n    if (!user || !user.isActive) {\n      return null\n    }\n\n    return {\n      id: user.id,\n      email: user.email,\n      name: user.name,\n      avatar: user.avatar || undefined,\n      familyId: user.familyId || undefined,\n      familyRole: user.familyRole,\n      family: user.family ? {\n        id: user.family.id,\n        name: user.family.name,\n        role: user.familyRole,\n      } : undefined,\n    }\n  } catch (error) {\n    console.error('Error getting user from request:', error)\n    return null\n  }\n}\n\n// 中间件：验证用户认证\nexport function requireAuth(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(\n        JSON.stringify({ success: false, error: '未授权访问' }),\n        { status: 401, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n    return handler(request, user)\n  }\n}\n\n// 中间件：验证家庭成员权限\nexport function requireFamilyMember(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(\n        JSON.stringify({ success: false, error: '未授权访问' }),\n        { status: 401, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    if (!user.familyId) {\n      return new Response(\n        JSON.stringify({ success: false, error: '您还没有加入任何家庭' }),\n        { status: 403, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n\n// 中间件：验证家庭管理员权限\nexport function requireFamilyAdmin(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(\n        JSON.stringify({ success: false, error: '未授权访问' }),\n        { status: 401, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    if (!user.familyId) {\n      return new Response(\n        JSON.stringify({ success: false, error: '您还没有加入任何家庭' }),\n        { status: 403, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    if (user.familyRole !== 'OWNER' && user.familyRole !== 'ADMIN') {\n      return new Response(\n        JSON.stringify({ success: false, error: '需要管理员权限' }),\n        { status: 403, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n\n// 验证邮箱格式\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// 验证密码强度\nexport function isValidPassword(password: string): { valid: boolean; message?: string } {\n  if (password.length < 8) {\n    return { valid: false, message: '密码长度至少8位' }\n  }\n  \n  if (!/(?=.*[a-z])/.test(password)) {\n    return { valid: false, message: '密码必须包含小写字母' }\n  }\n  \n  if (!/(?=.*[A-Z])/.test(password)) {\n    return { valid: false, message: '密码必须包含大写字母' }\n  }\n  \n  if (!/(?=.*\\d)/.test(password)) {\n    return { valid: false, message: '密码必须包含数字' }\n  }\n  \n  return { valid: true }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AAEA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AACrD,MAAM,gBAAgB,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;AAwBrD,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,cAAc,OAAmB;IAC/C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAe;AACnE;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe,mBAAmB,OAAoB;IAC3D,IAAI;QACF,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;QACnC,MAAM,UAAU,YAAY;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;YACF;QACF;QAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC3B,OAAO;QACT;QAEA,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,QAAQ,KAAK,MAAM,IAAI;YACvB,UAAU,KAAK,QAAQ,IAAI;YAC3B,YAAY,KAAK,UAAU;YAC3B,QAAQ,KAAK,MAAM,GAAG;gBACpB,IAAI,KAAK,MAAM,CAAC,EAAE;gBAClB,MAAM,KAAK,MAAM,CAAC,IAAI;gBACtB,MAAM,KAAK,UAAU;YACvB,IAAI;QACN;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAGO,SAAS,YAAY,OAAoE;IAC9F,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,mBAAmB;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAChD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QACA,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,SAAS,oBAAoB,OAAoE;IACtG,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,mBAAmB;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAChD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAa,IACrD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,SAAS,mBAAmB,OAAoE;IACrG,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,mBAAmB;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAChD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAa,IACrD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,KAAK,SAAS;YAC9D,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAU,IAClD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,gBAAgB,QAAgB;IAC9C,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO;YAAE,OAAO;YAAO,SAAS;QAAW;IAC7C;IAEA,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW;QACjC,OAAO;YAAE,OAAO;YAAO,SAAS;QAAa;IAC/C;IAEA,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW;QACjC,OAAO;YAAE,OAAO;YAAO,SAAS;QAAa;IAC/C;IAEA,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW;QAC9B,OAAO;YAAE,OAAO;YAAO,SAAS;QAAW;IAC7C;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB", "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/api/transactions/calendar/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { createSuccessResponse, handleApiError } from '@/lib/api-response'\nimport { getUserFromRequest } from '@/lib/auth'\n\ninterface DailyStats {\n  date: string\n  totalTransactions: number\n  totalIncome: number\n  totalExpense: number\n  netAmount: number\n  cardStats: {\n    cardId: string\n    cardName: string\n    cardType: string\n    transactionCount: number\n    income: number\n    expense: number\n    netAmount: number\n  }[]\n}\n\ninterface CalendarStats {\n  [date: string]: DailyStats\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    // 验证用户认证\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(JSON.stringify({ success: false, error: '未授权访问' }), {\n        status: 401,\n        headers: { 'Content-Type': 'application/json' }\n      })\n    }\n\n    const { searchParams } = new URL(request.url)\n\n    // 获取查询参数\n    const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString())\n    const month = parseInt(searchParams.get('month') || (new Date().getMonth() + 1).toString())\n    const cardId = searchParams.get('cardId') // 可选：按特定卡片筛选\n\n    // 验证参数\n    if (year < 2000 || year > 3000) {\n      return new Response(JSON.stringify({ success: false, error: '年份参数无效' }), {\n        status: 400,\n        headers: { 'Content-Type': 'application/json' }\n      })\n    }\n    if (month < 1 || month > 12) {\n      return new Response(JSON.stringify({ success: false, error: '月份参数无效' }), {\n        status: 400,\n        headers: { 'Content-Type': 'application/json' }\n      })\n    }\n    \n    // 计算月份的开始和结束日期\n    const startDate = new Date(year, month - 1, 1)\n    const endDate = new Date(year, month, 0, 23, 59, 59, 999)\n    \n    // 构建查询条件\n    const whereCondition: any = {\n      userId: user.id,\n      transactionDate: {\n        gte: startDate,\n        lte: endDate,\n      },\n    }\n    \n    if (cardId) {\n      whereCondition.cardId = cardId\n    }\n    \n    // 查询交易记录\n    const transactions = await prisma.transaction.findMany({\n      where: whereCondition,\n      include: {\n        card: {\n          select: {\n            id: true,\n            name: true,\n            type: true,\n          },\n        },\n      },\n      orderBy: {\n        transactionDate: 'asc',\n      },\n    })\n    \n    // 按日期分组统计\n    const calendarStats: CalendarStats = {}\n    \n    transactions.forEach(transaction => {\n      const dateKey = transaction.transactionDate.toISOString().split('T')[0]\n      \n      if (!calendarStats[dateKey]) {\n        calendarStats[dateKey] = {\n          date: dateKey,\n          totalTransactions: 0,\n          totalIncome: 0,\n          totalExpense: 0,\n          netAmount: 0,\n          cardStats: [],\n        }\n      }\n      \n      const dayStats = calendarStats[dateKey]\n      dayStats.totalTransactions++\n      \n      if (transaction.type === 'INCOME') {\n        dayStats.totalIncome += transaction.amount\n      } else {\n        dayStats.totalExpense += transaction.amount\n      }\n      \n      dayStats.netAmount = dayStats.totalIncome - dayStats.totalExpense\n      \n      // 按卡片统计\n      let cardStat = dayStats.cardStats.find(cs => cs.cardId === transaction.cardId)\n      if (!cardStat) {\n        cardStat = {\n          cardId: transaction.cardId || '',\n          cardName: transaction.card?.name || '未知卡片',\n          cardType: transaction.card?.type || 'UNKNOWN',\n          transactionCount: 0,\n          income: 0,\n          expense: 0,\n          netAmount: 0,\n        }\n        dayStats.cardStats.push(cardStat)\n      }\n      \n      cardStat.transactionCount++\n      if (transaction.type === 'INCOME') {\n        cardStat.income += transaction.amount\n      } else {\n        cardStat.expense += transaction.amount\n      }\n      cardStat.netAmount = cardStat.income - cardStat.expense\n    })\n    \n    // 获取用户的所有卡片（用于筛选器）\n    const userCards = await prisma.card.findMany({\n      where: {\n        userId: user.id,\n        isActive: true,\n      },\n      select: {\n        id: true,\n        name: true,\n        type: true,\n      },\n      orderBy: {\n        name: 'asc',\n      },\n    })\n    \n    // 计算月度汇总\n    const monthlyTotal = {\n      totalTransactions: transactions.length,\n      totalIncome: transactions\n        .filter(t => t.type === 'INCOME')\n        .reduce((sum, t) => sum + t.amount, 0),\n      totalExpense: transactions\n        .filter(t => t.type === 'EXPENSE')\n        .reduce((sum, t) => sum + t.amount, 0),\n    }\n    monthlyTotal.netAmount = monthlyTotal.totalIncome - monthlyTotal.totalExpense\n    \n    return createSuccessResponse({\n      year,\n      month,\n      calendarStats,\n      monthlyTotal,\n      userCards,\n    })\n    \n  } catch (error) {\n    console.error('Calendar stats API error:', error)\n    return handleApiError(error)\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAuBO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,SAAS;QACT,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAAI;gBACtE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,SAAS;QACT,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW,IAAI,OAAO,WAAW,GAAG,QAAQ;QACnF,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY,CAAC,IAAI,OAAO,QAAQ,KAAK,CAAC,EAAE,QAAQ;QACxF,MAAM,SAAS,aAAa,GAAG,CAAC,UAAU,aAAa;;QAEvD,OAAO;QACP,IAAI,OAAO,QAAQ,OAAO,MAAM;YAC9B,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAS,IAAI;gBACvE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QACA,IAAI,QAAQ,KAAK,QAAQ,IAAI;YAC3B,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAS,IAAI;gBACvE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,eAAe;QACf,MAAM,YAAY,IAAI,KAAK,MAAM,QAAQ,GAAG;QAC5C,MAAM,UAAU,IAAI,KAAK,MAAM,OAAO,GAAG,IAAI,IAAI,IAAI;QAErD,SAAS;QACT,MAAM,iBAAsB;YAC1B,QAAQ,KAAK,EAAE;YACf,iBAAiB;gBACf,KAAK;gBACL,KAAK;YACP;QACF;QAEA,IAAI,QAAQ;YACV,eAAe,MAAM,GAAG;QAC1B;QAEA,SAAS;QACT,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;YACrD,OAAO;YACP,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,MAAM;oBACR;gBACF;YACF;YACA,SAAS;gBACP,iBAAiB;YACnB;QACF;QAEA,UAAU;QACV,MAAM,gBAA+B,CAAC;QAEtC,aAAa,OAAO,CAAC,CAAA;YACnB,MAAM,UAAU,YAAY,eAAe,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAEvE,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;gBAC3B,aAAa,CAAC,QAAQ,GAAG;oBACvB,MAAM;oBACN,mBAAmB;oBACnB,aAAa;oBACb,cAAc;oBACd,WAAW;oBACX,WAAW,EAAE;gBACf;YACF;YAEA,MAAM,WAAW,aAAa,CAAC,QAAQ;YACvC,SAAS,iBAAiB;YAE1B,IAAI,YAAY,IAAI,KAAK,UAAU;gBACjC,SAAS,WAAW,IAAI,YAAY,MAAM;YAC5C,OAAO;gBACL,SAAS,YAAY,IAAI,YAAY,MAAM;YAC7C;YAEA,SAAS,SAAS,GAAG,SAAS,WAAW,GAAG,SAAS,YAAY;YAEjE,QAAQ;YACR,IAAI,WAAW,SAAS,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,GAAG,MAAM,KAAK,YAAY,MAAM;YAC7E,IAAI,CAAC,UAAU;gBACb,WAAW;oBACT,QAAQ,YAAY,MAAM,IAAI;oBAC9B,UAAU,YAAY,IAAI,EAAE,QAAQ;oBACpC,UAAU,YAAY,IAAI,EAAE,QAAQ;oBACpC,kBAAkB;oBAClB,QAAQ;oBACR,SAAS;oBACT,WAAW;gBACb;gBACA,SAAS,SAAS,CAAC,IAAI,CAAC;YAC1B;YAEA,SAAS,gBAAgB;YACzB,IAAI,YAAY,IAAI,KAAK,UAAU;gBACjC,SAAS,MAAM,IAAI,YAAY,MAAM;YACvC,OAAO;gBACL,SAAS,OAAO,IAAI,YAAY,MAAM;YACxC;YACA,SAAS,SAAS,GAAG,SAAS,MAAM,GAAG,SAAS,OAAO;QACzD;QAEA,mBAAmB;QACnB,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC3C,OAAO;gBACL,QAAQ,KAAK,EAAE;gBACf,UAAU;YACZ;YACA,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,MAAM;YACR;YACA,SAAS;gBACP,MAAM;YACR;QACF;QAEA,SAAS;QACT,MAAM,eAAe;YACnB,mBAAmB,aAAa,MAAM;YACtC,aAAa,aACV,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UACvB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;YACtC,cAAc,aACX,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WACvB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QACxC;QACA,aAAa,SAAS,GAAG,aAAa,WAAW,GAAG,aAAa,YAAY;QAE7E,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE;YAC3B;YACA;YACA;YACA;YACA;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;IACxB;AACF", "debugId": null}}]}