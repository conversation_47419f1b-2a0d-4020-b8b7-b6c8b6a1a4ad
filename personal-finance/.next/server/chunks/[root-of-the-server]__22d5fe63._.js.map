{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/api-response.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport interface ApiResponse<T = any> {\n  success: boolean\n  data?: T\n  error?: string\n  message?: string\n  pagination?: {\n    page: number\n    limit: number\n    total: number\n    totalPages: number\n  }\n}\n\nexport class ApiError extends Error {\n  constructor(\n    message: string,\n    public statusCode: number = 500,\n    public code?: string\n  ) {\n    super(message)\n    this.name = 'ApiError'\n  }\n}\n\nexport function createSuccessResponse<T>(\n  data: T,\n  message?: string,\n  pagination?: ApiResponse<T>['pagination']\n): NextResponse<ApiResponse<T>> {\n  return NextResponse.json({\n    success: true,\n    data,\n    message,\n    pagination,\n  })\n}\n\nexport function createErrorResponse(\n  error: string | Error,\n  statusCode: number = 500\n): NextResponse<ApiResponse> {\n  const message = error instanceof Error ? error.message : error\n  \n  return NextResponse.json(\n    {\n      success: false,\n      error: message,\n    },\n    { status: statusCode }\n  )\n}\n\nexport function handleApiError(error: unknown): NextResponse<ApiResponse> {\n  console.error('API Error:', error)\n  \n  if (error instanceof ApiError) {\n    return createErrorResponse(error.message, error.statusCode)\n  }\n  \n  if (error instanceof Error) {\n    return createErrorResponse(error.message, 500)\n  }\n  \n  return createErrorResponse('Internal server error', 500)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAeO,MAAM,iBAAiB;;;IAC5B,YACE,OAAe,EACf,AAAO,aAAqB,GAAG,EAC/B,AAAO,IAAa,CACpB;QACA,KAAK,CAAC,eAHC,aAAA,iBACA,OAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,SAAS,sBACd,IAAO,EACP,OAAgB,EAChB,UAAyC;IAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT;QACA;QACA;IACF;AACF;AAEO,SAAS,oBACd,KAAqB,EACrB,aAAqB,GAAG;IAExB,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAEzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT,OAAO;IACT,GACA;QAAE,QAAQ;IAAW;AAEzB;AAEO,SAAS,eAAe,KAAc;IAC3C,QAAQ,KAAK,CAAC,cAAc;IAE5B,IAAI,iBAAiB,UAAU;QAC7B,OAAO,oBAAoB,MAAM,OAAO,EAAE,MAAM,UAAU;IAC5D;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,oBAAoB,MAAM,OAAO,EAAE;IAC5C;IAEA,OAAO,oBAAoB,yBAAyB;AACtD", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/validations/card.ts"], "sourcesContent": ["import { z } from 'zod'\nimport { CardType } from '@prisma/client'\n\nexport const createCardSchema = z.object({\n  name: z.string().min(1, '卡片名称不能为空').max(100, '卡片名称不能超过100个字符'),\n  type: z.nativeEnum(CardType),\n  accountNo: z.string().max(50, '账号不能超过50个字符').optional(),\n  bankName: z.string().max(100, '银行名称不能超过100个字符').optional(),\n  description: z.string().max(500, '描述不能超过500个字符').optional(),\n  userId: z.string().cuid('用户ID格式不正确'),\n})\n\nexport const updateCardSchema = z.object({\n  name: z.string().min(1, '卡片名称不能为空').max(100, '卡片名称不能超过100个字符').optional(),\n  type: z.nativeEnum(CardType).optional(),\n  accountNo: z.string().max(50, '账号不能超过50个字符').optional(),\n  bankName: z.string().max(100, '银行名称不能超过100个字符').optional(),\n  description: z.string().max(500, '描述不能超过500个字符').optional(),\n  isActive: z.boolean().optional(),\n})\n\nexport type CreateCardInput = z.infer<typeof createCardSchema>\nexport type UpdateCardInput = z.infer<typeof updateCardSchema>\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,mBAAmB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,KAAK;IAC7C,MAAM,oKAAA,CAAA,IAAC,CAAC,UAAU,CAAC,6HAAA,CAAA,WAAQ;IAC3B,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,eAAe,QAAQ;IACrD,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,kBAAkB,QAAQ;IACxD,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,gBAAgB,QAAQ;IACzD,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;AAC1B;AAEO,MAAM,mBAAmB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,KAAK,kBAAkB,QAAQ;IACvE,MAAM,oKAAA,CAAA,IAAC,CAAC,UAAU,CAAC,6HAAA,CAAA,WAAQ,EAAE,QAAQ;IACrC,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,eAAe,QAAQ;IACrD,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,kBAAkB,QAAQ;IACxD,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,gBAAgB,QAAQ;IACzD,UAAU,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;AAChC", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { NextRequest } from 'next/server'\nimport { prisma } from './prisma'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'\nconst BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12')\n\nexport interface JWTPayload {\n  userId: string\n  email: string\n  familyId?: string\n  familyRole?: string\n}\n\nexport interface AuthUser {\n  id: string\n  email: string\n  name: string\n  avatar?: string\n  familyId?: string\n  familyRole?: string\n  family?: {\n    id: string\n    name: string\n    role: string\n  }\n}\n\n// 密码加密\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, BCRYPT_ROUNDS)\n}\n\n// 密码验证\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\n// 生成JWT令牌\nexport function generateToken(payload: JWTPayload): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })\n}\n\n// 验证JWT令牌\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload\n  } catch (error) {\n    return null\n  }\n}\n\n// 从请求中获取用户信息\nexport async function getUserFromRequest(request: NextRequest): Promise<AuthUser | null> {\n  try {\n    const authHeader = request.headers.get('authorization')\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return null\n    }\n\n    const token = authHeader.substring(7)\n    const payload = verifyToken(token)\n    if (!payload) {\n      return null\n    }\n\n    const user = await prisma.user.findUnique({\n      where: { id: payload.userId },\n      include: {\n        family: {\n          select: {\n            id: true,\n            name: true,\n          },\n        },\n      },\n    })\n\n    if (!user || !user.isActive) {\n      return null\n    }\n\n    return {\n      id: user.id,\n      email: user.email,\n      name: user.name,\n      avatar: user.avatar || undefined,\n      familyId: user.familyId || undefined,\n      familyRole: user.familyRole,\n      family: user.family ? {\n        id: user.family.id,\n        name: user.family.name,\n        role: user.familyRole,\n      } : undefined,\n    }\n  } catch (error) {\n    console.error('Error getting user from request:', error)\n    return null\n  }\n}\n\n// 中间件：验证用户认证\nexport function requireAuth(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(\n        JSON.stringify({ success: false, error: '未授权访问' }),\n        { status: 401, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n    return handler(request, user)\n  }\n}\n\n// 中间件：验证家庭成员权限\nexport function requireFamilyMember(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(\n        JSON.stringify({ success: false, error: '未授权访问' }),\n        { status: 401, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    if (!user.familyId) {\n      return new Response(\n        JSON.stringify({ success: false, error: '您还没有加入任何家庭' }),\n        { status: 403, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n\n// 中间件：验证家庭管理员权限\nexport function requireFamilyAdmin(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(\n        JSON.stringify({ success: false, error: '未授权访问' }),\n        { status: 401, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    if (!user.familyId) {\n      return new Response(\n        JSON.stringify({ success: false, error: '您还没有加入任何家庭' }),\n        { status: 403, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    if (user.familyRole !== 'OWNER' && user.familyRole !== 'ADMIN') {\n      return new Response(\n        JSON.stringify({ success: false, error: '需要管理员权限' }),\n        { status: 403, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n\n// 验证邮箱格式\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// 验证密码强度\nexport function isValidPassword(password: string): { valid: boolean; message?: string } {\n  if (password.length < 8) {\n    return { valid: false, message: '密码长度至少8位' }\n  }\n  \n  if (!/(?=.*[a-z])/.test(password)) {\n    return { valid: false, message: '密码必须包含小写字母' }\n  }\n  \n  if (!/(?=.*[A-Z])/.test(password)) {\n    return { valid: false, message: '密码必须包含大写字母' }\n  }\n  \n  if (!/(?=.*\\d)/.test(password)) {\n    return { valid: false, message: '密码必须包含数字' }\n  }\n  \n  return { valid: true }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AAEA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AACrD,MAAM,gBAAgB,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;AAwBrD,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,cAAc,OAAmB;IAC/C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAe;AACnE;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe,mBAAmB,OAAoB;IAC3D,IAAI;QACF,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;QACnC,MAAM,UAAU,YAAY;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;YACF;QACF;QAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC3B,OAAO;QACT;QAEA,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,QAAQ,KAAK,MAAM,IAAI;YACvB,UAAU,KAAK,QAAQ,IAAI;YAC3B,YAAY,KAAK,UAAU;YAC3B,QAAQ,KAAK,MAAM,GAAG;gBACpB,IAAI,KAAK,MAAM,CAAC,EAAE;gBAClB,MAAM,KAAK,MAAM,CAAC,IAAI;gBACtB,MAAM,KAAK,UAAU;YACvB,IAAI;QACN;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAGO,SAAS,YAAY,OAAoE;IAC9F,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,mBAAmB;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAChD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QACA,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,SAAS,oBAAoB,OAAoE;IACtG,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,mBAAmB;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAChD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAa,IACrD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,SAAS,mBAAmB,OAAoE;IACrG,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,mBAAmB;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAChD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAa,IACrD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,KAAK,SAAS;YAC9D,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAU,IAClD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,gBAAgB,QAAgB;IAC9C,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO;YAAE,OAAO;YAAO,SAAS;QAAW;IAC7C;IAEA,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW;QACjC,OAAO;YAAE,OAAO;YAAO,SAAS;QAAa;IAC/C;IAEA,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW;QACjC,OAAO;YAAE,OAAO;YAAO,SAAS;QAAa;IAC/C;IAEA,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW;QAC9B,OAAO;YAAE,OAAO;YAAO,SAAS;QAAW;IAC7C;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/api/cards/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { createSuccessResponse, handleApiError, ApiError } from '@/lib/api-response'\nimport { updateCardSchema } from '@/lib/validations/card'\nimport { getUserFromRequest } from '@/lib/auth'\n\n// GET /api/cards/[id] - 获取单个卡片\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    // 验证用户认证\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(JSON.stringify({ success: false, error: '未授权访问' }), {\n        status: 401,\n        headers: { 'Content-Type': 'application/json' }\n      })\n    }\n\n    const { id } = await params\n    const card = await prisma.card.findFirst({\n      where: {\n        id,\n        userId: user.id, // 确保只能访问自己的卡片\n      },\n      include: {\n        user: {\n          select: {\n            id: true,\n            name: true,\n          },\n        },\n        transactions: {\n          take: 10,\n          orderBy: { transactionDate: 'desc' },\n          select: {\n            id: true,\n            type: true,\n            amount: true,\n            description: true,\n            transactionDate: true,\n            status: true,\n          },\n        },\n        _count: {\n          select: {\n            transactions: true,\n            billImports: true,\n          },\n        },\n      },\n    })\n\n    if (!card) {\n      throw new ApiError('卡片不存在', 404)\n    }\n\n    return createSuccessResponse(card)\n  } catch (error) {\n    return handleApiError(error)\n  }\n}\n\n// PUT /api/cards/[id] - 更新卡片\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    // 验证用户认证\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(JSON.stringify({ success: false, error: '未授权访问' }), {\n        status: 401,\n        headers: { 'Content-Type': 'application/json' }\n      })\n    }\n\n    const { id } = await params\n    // 检查卡片是否存在且属于当前用户\n    const existingCard = await prisma.card.findFirst({\n      where: {\n        id,\n        userId: user.id,\n      },\n    })\n\n    if (!existingCard) {\n      throw new ApiError('卡片不存在', 404)\n    }\n\n    const body = await request.json()\n    const validatedData = updateCardSchema.parse(body)\n\n    const card = await prisma.card.update({\n      where: { id },\n      data: validatedData,\n      include: {\n        user: {\n          select: {\n            id: true,\n            name: true,\n          },\n        },\n        _count: {\n          select: {\n            transactions: true,\n            billImports: true,\n          },\n        },\n      },\n    })\n\n    return createSuccessResponse(card, '卡片更新成功')\n  } catch (error) {\n    return handleApiError(error)\n  }\n}\n\n// DELETE /api/cards/[id] - 删除卡片\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    // 验证用户认证\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(JSON.stringify({ success: false, error: '未授权访问' }), {\n        status: 401,\n        headers: { 'Content-Type': 'application/json' }\n      })\n    }\n\n    const { id } = await params\n    // 检查卡片是否存在且属于当前用户\n    const existingCard = await prisma.card.findFirst({\n      where: {\n        id,\n        userId: user.id,\n      },\n      include: {\n        _count: {\n          select: {\n            transactions: true,\n            billImports: true,\n          },\n        },\n      },\n    })\n\n    if (!existingCard) {\n      throw new ApiError('卡片不存在', 404)\n    }\n\n    // 检查是否有关联的交易记录\n    if (existingCard._count.transactions > 0) {\n      throw new ApiError('无法删除有交易记录的卡片，请先删除相关交易记录', 400)\n    }\n\n    // 软删除：将卡片标记为不活跃\n    const card = await prisma.card.update({\n      where: { id },\n      data: { isActive: false },\n      include: {\n        user: {\n          select: {\n            id: true,\n            name: true,\n          },\n        },\n        _count: {\n          select: {\n            transactions: true,\n            billImports: true,\n          },\n        },\n      },\n    })\n\n    return createSuccessResponse(card, '卡片删除成功')\n  } catch (error) {\n    return handleApiError(error)\n  }\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;;;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,SAAS;QACT,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAAI;gBACtE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACvC,OAAO;gBACL;gBACA,QAAQ,KAAK,EAAE;YACjB;YACA,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;gBACA,cAAc;oBACZ,MAAM;oBACN,SAAS;wBAAE,iBAAiB;oBAAO;oBACnC,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,QAAQ;wBACR,aAAa;wBACb,iBAAiB;wBACjB,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,cAAc;wBACd,aAAa;oBACf;gBACF;YACF;QACF;QAEA,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,+HAAA,CAAA,WAAQ,CAAC,SAAS;QAC9B;QAEA,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE;IAC/B,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;IACxB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,SAAS;QACT,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAAI;gBACtE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,kBAAkB;QAClB,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YAC/C,OAAO;gBACL;gBACA,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,+HAAA,CAAA,WAAQ,CAAC,SAAS;QAC9B;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,mIAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC;QAE7C,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,OAAO;gBAAE;YAAG;YACZ,MAAM;YACN,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,cAAc;wBACd,aAAa;oBACf;gBACF;YACF;QACF;QAEA,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM;IACrC,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;IACxB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,SAAS;QACT,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAAI;gBACtE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,kBAAkB;QAClB,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YAC/C,OAAO;gBACL;gBACA,QAAQ,KAAK,EAAE;YACjB;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,cAAc;wBACd,aAAa;oBACf;gBACF;YACF;QACF;QAEA,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,+HAAA,CAAA,WAAQ,CAAC,SAAS;QAC9B;QAEA,eAAe;QACf,IAAI,aAAa,MAAM,CAAC,YAAY,GAAG,GAAG;YACxC,MAAM,IAAI,+HAAA,CAAA,WAAQ,CAAC,2BAA2B;QAChD;QAEA,gBAAgB;QAChB,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,OAAO;gBAAE;YAAG;YACZ,MAAM;gBAAE,UAAU;YAAM;YACxB,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,cAAc;wBACd,aAAa;oBACf;gBACF;YACF;QACF;QAEA,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM;IACrC,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;IACxB;AACF", "debugId": null}}]}