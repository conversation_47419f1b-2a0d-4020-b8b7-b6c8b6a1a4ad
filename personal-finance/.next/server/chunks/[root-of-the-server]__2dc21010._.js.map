{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/api-response.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport interface ApiResponse<T = any> {\n  success: boolean\n  data?: T\n  error?: string\n  message?: string\n  pagination?: {\n    page: number\n    limit: number\n    total: number\n    totalPages: number\n  }\n}\n\nexport class ApiError extends Error {\n  constructor(\n    message: string,\n    public statusCode: number = 500,\n    public code?: string\n  ) {\n    super(message)\n    this.name = 'ApiError'\n  }\n}\n\nexport function createSuccessResponse<T>(\n  data: T,\n  message?: string,\n  pagination?: ApiResponse<T>['pagination']\n): NextResponse<ApiResponse<T>> {\n  return NextResponse.json({\n    success: true,\n    data,\n    message,\n    pagination,\n  })\n}\n\nexport function createErrorResponse(\n  error: string | Error,\n  statusCode: number = 500\n): NextResponse<ApiResponse> {\n  const message = error instanceof Error ? error.message : error\n  \n  return NextResponse.json(\n    {\n      success: false,\n      error: message,\n    },\n    { status: statusCode }\n  )\n}\n\nexport function handleApiError(error: unknown): NextResponse<ApiResponse> {\n  console.error('API Error:', error)\n  \n  if (error instanceof ApiError) {\n    return createErrorResponse(error.message, error.statusCode)\n  }\n  \n  if (error instanceof Error) {\n    return createErrorResponse(error.message, 500)\n  }\n  \n  return createErrorResponse('Internal server error', 500)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAeO,MAAM,iBAAiB;;;IAC5B,YACE,OAAe,EACf,AAAO,aAAqB,GAAG,EAC/B,AAAO,IAAa,CACpB;QACA,KAAK,CAAC,eAHC,aAAA,iBACA,OAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,SAAS,sBACd,IAAO,EACP,OAAgB,EAChB,UAAyC;IAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT;QACA;QACA;IACF;AACF;AAEO,SAAS,oBACd,KAAqB,EACrB,aAAqB,GAAG;IAExB,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAEzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT,OAAO;IACT,GACA;QAAE,QAAQ;IAAW;AAEzB;AAEO,SAAS,eAAe,KAAc;IAC3C,QAAQ,KAAK,CAAC,cAAc;IAE5B,IAAI,iBAAiB,UAAU;QAC7B,OAAO,oBAAoB,MAAM,OAAO,EAAE,MAAM,UAAU;IAC5D;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,oBAAoB,MAAM,OAAO,EAAE;IAC5C;IAEA,OAAO,oBAAoB,yBAAyB;AACtD", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/validations/transaction.ts"], "sourcesContent": ["import { z } from 'zod'\nimport { TransactionType, TransactionStatus } from '@prisma/client'\n\nexport const createTransactionSchema = z.object({\n  type: z.nativeEnum(TransactionType),\n  amount: z.number().positive('金额必须大于0'),\n  description: z.string().min(1, '交易描述不能为空').max(200, '交易描述不能超过200个字符'),\n  category: z.string().max(50, '分类不能超过50个字符').optional(),\n  subcategory: z.string().max(50, '子分类不能超过50个字符').optional(),\n  transactionDate: z.string().datetime('交易时间格式不正确'),\n  location: z.string().max(100, '交易地点不能超过100个字符').optional(),\n  notes: z.string().max(500, '备注不能超过500个字符').optional(),\n  userId: z.string().cuid('用户ID格式不正确'),\n  cardId: z.string().cuid('卡片ID格式不正确').optional(),\n  externalId: z.string().max(100, '外部ID不能超过100个字符').optional(),\n})\n\nexport const updateTransactionSchema = z.object({\n  type: z.nativeEnum(TransactionType).optional(),\n  status: z.nativeEnum(TransactionStatus).optional(),\n  amount: z.number().positive('金额必须大于0').optional(),\n  description: z.string().min(1, '交易描述不能为空').max(200, '交易描述不能超过200个字符').optional(),\n  category: z.string().max(50, '分类不能超过50个字符').optional(),\n  subcategory: z.string().max(50, '子分类不能超过50个字符').optional(),\n  transactionDate: z.string().datetime('交易时间格式不正确').optional(),\n  location: z.string().max(100, '交易地点不能超过100个字符').optional(),\n  notes: z.string().max(500, '备注不能超过500个字符').optional(),\n  cardId: z.string().cuid('卡片ID格式不正确').optional(),\n})\n\nexport const transactionQuerySchema = z.object({\n  page: z.string().transform(Number).pipe(z.number().min(1)).default('1'),\n  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).default('20'),\n  type: z.nativeEnum(TransactionType).optional(),\n  status: z.nativeEnum(TransactionStatus).optional(),\n  cardId: z.string().cuid().optional(),\n  category: z.string().optional(),\n  startDate: z.string().datetime().optional(),\n  endDate: z.string().datetime().optional(),\n  search: z.string().optional(),\n})\n\nexport type CreateTransactionInput = z.infer<typeof createTransactionSchema>\nexport type UpdateTransactionInput = z.infer<typeof updateTransactionSchema>\nexport type TransactionQuery = z.infer<typeof transactionQuerySchema>\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,MAAM,0BAA0B,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,MAAM,oKAAA,CAAA,IAAC,CAAC,UAAU,CAAC,6HAAA,CAAA,kBAAe;IAClC,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5B,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,KAAK;IACpD,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,eAAe,QAAQ;IACpD,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,gBAAgB,QAAQ;IACxD,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACrC,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,kBAAkB,QAAQ;IACxD,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,gBAAgB,QAAQ;IACnD,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IACxB,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,QAAQ;IAC7C,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,kBAAkB,QAAQ;AAC5D;AAEO,MAAM,0BAA0B,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,MAAM,oKAAA,CAAA,IAAC,CAAC,UAAU,CAAC,6HAAA,CAAA,kBAAe,EAAE,QAAQ;IAC5C,QAAQ,oKAAA,CAAA,IAAC,CAAC,UAAU,CAAC,6HAAA,CAAA,oBAAiB,EAAE,QAAQ;IAChD,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,WAAW,QAAQ;IAC/C,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,KAAK,kBAAkB,QAAQ;IAC9E,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,eAAe,QAAQ;IACpD,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,gBAAgB,QAAQ;IACxD,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,QAAQ;IAC1D,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,kBAAkB,QAAQ;IACxD,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,gBAAgB,QAAQ;IACnD,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,QAAQ;AAC/C;AAEO,MAAM,yBAAyB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7C,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,IAAI,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;IACnE,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,IAAI,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,OAAO,CAAC;IAC7E,MAAM,oKAAA,CAAA,IAAC,CAAC,UAAU,CAAC,6HAAA,CAAA,kBAAe,EAAE,QAAQ;IAC5C,QAAQ,oKAAA,CAAA,IAAC,CAAC,UAAU,CAAC,6HAAA,CAAA,oBAAiB,EAAE,QAAQ;IAChD,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAClC,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACzC,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACvC,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC7B", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { NextRequest } from 'next/server'\nimport { prisma } from './prisma'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'\nconst BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12')\n\nexport interface JWTPayload {\n  userId: string\n  email: string\n  familyId?: string\n  familyRole?: string\n}\n\nexport interface AuthUser {\n  id: string\n  email: string\n  name: string\n  avatar?: string\n  familyId?: string\n  familyRole?: string\n  family?: {\n    id: string\n    name: string\n    role: string\n  }\n}\n\n// 密码加密\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, BCRYPT_ROUNDS)\n}\n\n// 密码验证\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\n// 生成JWT令牌\nexport function generateToken(payload: JWTPayload): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })\n}\n\n// 验证JWT令牌\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload\n  } catch (error) {\n    return null\n  }\n}\n\n// 从请求中获取用户信息\nexport async function getUserFromRequest(request: NextRequest): Promise<AuthUser | null> {\n  try {\n    const authHeader = request.headers.get('authorization')\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return null\n    }\n\n    const token = authHeader.substring(7)\n    const payload = verifyToken(token)\n    if (!payload) {\n      return null\n    }\n\n    const user = await prisma.user.findUnique({\n      where: { id: payload.userId },\n      include: {\n        family: {\n          select: {\n            id: true,\n            name: true,\n          },\n        },\n      },\n    })\n\n    if (!user || !user.isActive) {\n      return null\n    }\n\n    return {\n      id: user.id,\n      email: user.email,\n      name: user.name,\n      avatar: user.avatar || undefined,\n      familyId: user.familyId || undefined,\n      familyRole: user.familyRole,\n      family: user.family ? {\n        id: user.family.id,\n        name: user.family.name,\n        role: user.familyRole,\n      } : undefined,\n    }\n  } catch (error) {\n    console.error('Error getting user from request:', error)\n    return null\n  }\n}\n\n// 中间件：验证用户认证\nexport function requireAuth(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(\n        JSON.stringify({ success: false, error: '未授权访问' }),\n        { status: 401, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n    return handler(request, user)\n  }\n}\n\n// 中间件：验证家庭成员权限\nexport function requireFamilyMember(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(\n        JSON.stringify({ success: false, error: '未授权访问' }),\n        { status: 401, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    if (!user.familyId) {\n      return new Response(\n        JSON.stringify({ success: false, error: '您还没有加入任何家庭' }),\n        { status: 403, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n\n// 中间件：验证家庭管理员权限\nexport function requireFamilyAdmin(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(\n        JSON.stringify({ success: false, error: '未授权访问' }),\n        { status: 401, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    if (!user.familyId) {\n      return new Response(\n        JSON.stringify({ success: false, error: '您还没有加入任何家庭' }),\n        { status: 403, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    if (user.familyRole !== 'OWNER' && user.familyRole !== 'ADMIN') {\n      return new Response(\n        JSON.stringify({ success: false, error: '需要管理员权限' }),\n        { status: 403, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n\n// 验证邮箱格式\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// 验证密码强度\nexport function isValidPassword(password: string): { valid: boolean; message?: string } {\n  if (password.length < 8) {\n    return { valid: false, message: '密码长度至少8位' }\n  }\n  \n  if (!/(?=.*[a-z])/.test(password)) {\n    return { valid: false, message: '密码必须包含小写字母' }\n  }\n  \n  if (!/(?=.*[A-Z])/.test(password)) {\n    return { valid: false, message: '密码必须包含大写字母' }\n  }\n  \n  if (!/(?=.*\\d)/.test(password)) {\n    return { valid: false, message: '密码必须包含数字' }\n  }\n  \n  return { valid: true }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AAEA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AACrD,MAAM,gBAAgB,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;AAwBrD,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,cAAc,OAAmB;IAC/C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAe;AACnE;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe,mBAAmB,OAAoB;IAC3D,IAAI;QACF,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;QACnC,MAAM,UAAU,YAAY;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;YACF;QACF;QAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC3B,OAAO;QACT;QAEA,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,QAAQ,KAAK,MAAM,IAAI;YACvB,UAAU,KAAK,QAAQ,IAAI;YAC3B,YAAY,KAAK,UAAU;YAC3B,QAAQ,KAAK,MAAM,GAAG;gBACpB,IAAI,KAAK,MAAM,CAAC,EAAE;gBAClB,MAAM,KAAK,MAAM,CAAC,IAAI;gBACtB,MAAM,KAAK,UAAU;YACvB,IAAI;QACN;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAGO,SAAS,YAAY,OAAoE;IAC9F,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,mBAAmB;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAChD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QACA,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,SAAS,oBAAoB,OAAoE;IACtG,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,mBAAmB;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAChD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAa,IACrD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,SAAS,mBAAmB,OAAoE;IACrG,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,mBAAmB;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAChD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAa,IACrD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,KAAK,SAAS;YAC9D,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAU,IAClD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,gBAAgB,QAAgB;IAC9C,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO;YAAE,OAAO;YAAO,SAAS;QAAW;IAC7C;IAEA,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW;QACjC,OAAO;YAAE,OAAO;YAAO,SAAS;QAAa;IAC/C;IAEA,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW;QACjC,OAAO;YAAE,OAAO;YAAO,SAAS;QAAa;IAC/C;IAEA,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW;QAC9B,OAAO;YAAE,OAAO;YAAO,SAAS;QAAW;IAC7C;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB", "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/api/transactions/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { createSuccessResponse, handleApiError } from '@/lib/api-response'\nimport { createTransactionSchema, transactionQuerySchema } from '@/lib/validations/transaction'\nimport { getUserFromRequest } from '@/lib/auth'\n\n// GET /api/transactions - 获取交易记录列表\nexport async function GET(request: NextRequest) {\n  try {\n    // 验证用户认证\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(JSON.stringify({ success: false, error: '未授权访问' }), {\n        status: 401,\n        headers: { 'Content-Type': 'application/json' }\n      })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const query = transactionQuerySchema.parse(Object.fromEntries(searchParams))\n\n    const where: any = {\n      userId: user.id, // 只查询当前用户的交易记录\n    }\n    if (query.type) where.type = query.type\n    if (query.status) where.status = query.status\n    if (query.cardId) where.cardId = query.cardId\n    if (query.category) where.category = query.category\n    if (query.startDate || query.endDate) {\n      where.transactionDate = {}\n      if (query.startDate) where.transactionDate.gte = new Date(query.startDate)\n      if (query.endDate) where.transactionDate.lte = new Date(query.endDate)\n    }\n    if (query.search) {\n      where.OR = [\n        { description: { contains: query.search } },\n        { notes: { contains: query.search } },\n        { location: { contains: query.search } },\n      ]\n    }\n\n    const [transactions, total] = await Promise.all([\n      prisma.transaction.findMany({\n        where,\n        include: {\n          user: {\n            select: {\n              id: true,\n              name: true,\n            },\n          },\n          card: {\n            select: {\n              id: true,\n              name: true,\n              type: true,\n            },\n          },\n          linkedTransaction: {\n            select: {\n              id: true,\n              description: true,\n              amount: true,\n            },\n          },\n        },\n        orderBy: { transactionDate: 'desc' },\n        skip: (query.page - 1) * query.limit,\n        take: query.limit,\n      }),\n      prisma.transaction.count({ where }),\n    ])\n\n    const pagination = {\n      page: query.page,\n      limit: query.limit,\n      total,\n      totalPages: Math.ceil(total / query.limit),\n    }\n\n    return createSuccessResponse(transactions, undefined, pagination)\n  } catch (error) {\n    return handleApiError(error)\n  }\n}\n\n// POST /api/transactions - 创建新交易记录\nexport async function POST(request: NextRequest) {\n  try {\n    // 验证用户认证\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(JSON.stringify({ success: false, error: '未授权访问' }), {\n        status: 401,\n        headers: { 'Content-Type': 'application/json' }\n      })\n    }\n\n    const body = await request.json()\n    const validatedData = createTransactionSchema.parse({\n      ...body,\n      userId: user.id, // 确保交易属于当前用户\n    })\n\n    // 转换日期字符串为Date对象\n    const transactionData = {\n      ...validatedData,\n      transactionDate: new Date(validatedData.transactionDate),\n    }\n\n    const transaction = await prisma.transaction.create({\n      data: transactionData,\n      include: {\n        user: {\n          select: {\n            id: true,\n            name: true,\n          },\n        },\n        card: {\n          select: {\n            id: true,\n            name: true,\n            type: true,\n          },\n        },\n      },\n    })\n\n    return createSuccessResponse(transaction, '交易记录创建成功')\n  } catch (error) {\n    return handleApiError(error)\n  }\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,SAAS;QACT,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAAI;gBACtE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,0IAAA,CAAA,yBAAsB,CAAC,KAAK,CAAC,OAAO,WAAW,CAAC;QAE9D,MAAM,QAAa;YACjB,QAAQ,KAAK,EAAE;QACjB;QACA,IAAI,MAAM,IAAI,EAAE,MAAM,IAAI,GAAG,MAAM,IAAI;QACvC,IAAI,MAAM,MAAM,EAAE,MAAM,MAAM,GAAG,MAAM,MAAM;QAC7C,IAAI,MAAM,MAAM,EAAE,MAAM,MAAM,GAAG,MAAM,MAAM;QAC7C,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ,GAAG,MAAM,QAAQ;QACnD,IAAI,MAAM,SAAS,IAAI,MAAM,OAAO,EAAE;YACpC,MAAM,eAAe,GAAG,CAAC;YACzB,IAAI,MAAM,SAAS,EAAE,MAAM,eAAe,CAAC,GAAG,GAAG,IAAI,KAAK,MAAM,SAAS;YACzE,IAAI,MAAM,OAAO,EAAE,MAAM,eAAe,CAAC,GAAG,GAAG,IAAI,KAAK,MAAM,OAAO;QACvE;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,EAAE,GAAG;gBACT;oBAAE,aAAa;wBAAE,UAAU,MAAM,MAAM;oBAAC;gBAAE;gBAC1C;oBAAE,OAAO;wBAAE,UAAU,MAAM,MAAM;oBAAC;gBAAE;gBACpC;oBAAE,UAAU;wBAAE,UAAU,MAAM,MAAM;oBAAC;gBAAE;aACxC;QACH;QAEA,MAAM,CAAC,cAAc,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC9C,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;gBAC1B;gBACA,SAAS;oBACP,MAAM;wBACJ,QAAQ;4BACN,IAAI;4BACJ,MAAM;wBACR;oBACF;oBACA,MAAM;wBACJ,QAAQ;4BACN,IAAI;4BACJ,MAAM;4BACN,MAAM;wBACR;oBACF;oBACA,mBAAmB;wBACjB,QAAQ;4BACN,IAAI;4BACJ,aAAa;4BACb,QAAQ;wBACV;oBACF;gBACF;gBACA,SAAS;oBAAE,iBAAiB;gBAAO;gBACnC,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,MAAM,KAAK;gBACpC,MAAM,MAAM,KAAK;YACnB;YACA,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,KAAK,CAAC;gBAAE;YAAM;SAClC;QAED,MAAM,aAAa;YACjB,MAAM,MAAM,IAAI;YAChB,OAAO,MAAM,KAAK;YAClB;YACA,YAAY,KAAK,IAAI,CAAC,QAAQ,MAAM,KAAK;QAC3C;QAEA,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc,WAAW;IACxD,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;IACxB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,SAAS;QACT,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAAI;gBACtE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,0IAAA,CAAA,0BAAuB,CAAC,KAAK,CAAC;YAClD,GAAG,IAAI;YACP,QAAQ,KAAK,EAAE;QACjB;QAEA,iBAAiB;QACjB,MAAM,kBAAkB;YACtB,GAAG,aAAa;YAChB,iBAAiB,IAAI,KAAK,cAAc,eAAe;QACzD;QAEA,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YAClD,MAAM;YACN,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;gBACA,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,MAAM;oBACR;gBACF;YACF;QACF;QAEA,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa;IAC5C,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;IACxB;AACF", "debugId": null}}]}