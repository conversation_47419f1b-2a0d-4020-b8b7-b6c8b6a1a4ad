{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/api-response.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport interface ApiResponse<T = any> {\n  success: boolean\n  data?: T\n  error?: string\n  message?: string\n  pagination?: {\n    page: number\n    limit: number\n    total: number\n    totalPages: number\n  }\n}\n\nexport class ApiError extends Error {\n  constructor(\n    message: string,\n    public statusCode: number = 500,\n    public code?: string\n  ) {\n    super(message)\n    this.name = 'ApiError'\n  }\n}\n\nexport function createSuccessResponse<T>(\n  data: T,\n  message?: string,\n  pagination?: ApiResponse<T>['pagination']\n): NextResponse<ApiResponse<T>> {\n  return NextResponse.json({\n    success: true,\n    data,\n    message,\n    pagination,\n  })\n}\n\nexport function createErrorResponse(\n  error: string | Error,\n  statusCode: number = 500\n): NextResponse<ApiResponse> {\n  const message = error instanceof Error ? error.message : error\n  \n  return NextResponse.json(\n    {\n      success: false,\n      error: message,\n    },\n    { status: statusCode }\n  )\n}\n\nexport function handleApiError(error: unknown): NextResponse<ApiResponse> {\n  console.error('API Error:', error)\n  \n  if (error instanceof ApiError) {\n    return createErrorResponse(error.message, error.statusCode)\n  }\n  \n  if (error instanceof Error) {\n    return createErrorResponse(error.message, 500)\n  }\n  \n  return createErrorResponse('Internal server error', 500)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAeO,MAAM,iBAAiB;;;IAC5B,YACE,OAAe,EACf,AAAO,aAAqB,GAAG,EAC/B,AAAO,IAAa,CACpB;QACA,KAAK,CAAC,eAHC,aAAA,iBACA,OAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,SAAS,sBACd,IAAO,EACP,OAAgB,EAChB,UAAyC;IAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT;QACA;QACA;IACF;AACF;AAEO,SAAS,oBACd,KAAqB,EACrB,aAAqB,GAAG;IAExB,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAEzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT,OAAO;IACT,GACA;QAAE,QAAQ;IAAW;AAEzB;AAEO,SAAS,eAAe,KAAc;IAC3C,QAAQ,KAAK,CAAC,cAAc;IAE5B,IAAI,iBAAiB,UAAU;QAC7B,OAAO,oBAAoB,MAAM,OAAO,EAAE,MAAM,UAAU;IAC5D;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,oBAAoB,MAAM,OAAO,EAAE;IAC5C;IAEA,OAAO,oBAAoB,yBAAyB;AACtD", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/validations/user.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// 用户注册\nexport const registerSchema = z.object({\n  email: z.string().email('邮箱格式不正确'),\n  password: z.string().min(8, '密码长度至少8位'),\n  name: z.string().min(1, '姓名不能为空').max(50, '姓名不能超过50个字符'),\n})\n\n// 用户登录\nexport const loginSchema = z.object({\n  email: z.string().email('邮箱格式不正确'),\n  password: z.string().min(1, '密码不能为空'),\n})\n\n// 更新用户信息\nexport const updateUserSchema = z.object({\n  name: z.string().min(1, '姓名不能为空').max(50, '姓名不能超过50个字符').optional(),\n  avatar: z.string().url('头像URL格式不正确').optional(),\n})\n\n// 修改密码\nexport const changePasswordSchema = z.object({\n  currentPassword: z.string().min(1, '当前密码不能为空'),\n  newPassword: z.string().min(8, '新密码长度至少8位'),\n  confirmPassword: z.string().min(1, '确认密码不能为空'),\n}).refine((data) => data.newPassword === data.confirmPassword, {\n  message: '两次输入的密码不一致',\n  path: ['confirmPassword'],\n})\n\n// 创建家庭\nexport const createFamilySchema = z.object({\n  name: z.string().min(1, '家庭名称不能为空').max(100, '家庭名称不能超过100个字符'),\n  description: z.string().max(500, '家庭描述不能超过500个字符').optional(),\n  avatar: z.string().url('头像URL格式不正确').optional(),\n})\n\n// 邀请家庭成员\nexport const inviteFamilyMemberSchema = z.object({\n  email: z.string().email('邮箱格式不正确'),\n  role: z.enum(['OWNER', 'ADMIN', 'MEMBER']).default('MEMBER'),\n  message: z.string().max(500, '邀请消息不能超过500个字符').optional(),\n})\n\n// 响应邀请\nexport const respondInvitationSchema = z.object({\n  action: z.enum(['accept', 'decline']),\n})\n\nexport type RegisterInput = z.infer<typeof registerSchema>\nexport type LoginInput = z.infer<typeof loginSchema>\nexport type UpdateUserInput = z.infer<typeof updateUserSchema>\nexport type ChangePasswordInput = z.infer<typeof changePasswordSchema>\nexport type CreateFamilyInput = z.infer<typeof createFamilySchema>\nexport type InviteFamilyMemberInput = z.infer<typeof inviteFamilyMemberSchema>\nexport type RespondInvitationInput = z.infer<typeof respondInvitationSchema>\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAGO,MAAM,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,IAAI;AAC5C;AAGO,MAAM,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAGO,MAAM,mBAAmB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,IAAI,eAAe,QAAQ;IACjE,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,cAAc,QAAQ;AAC/C;AAGO,MAAM,uBAAuB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACnC,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACrC,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,WAAW,KAAK,KAAK,eAAe,EAAE;IAC7D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAGO,MAAM,qBAAqB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,KAAK;IAC7C,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,kBAAkB,QAAQ;IAC3D,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,cAAc,QAAQ;AAC/C;AAGO,MAAM,2BAA2B,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/C,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,MAAM,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;QAAS;KAAS,EAAE,OAAO,CAAC;IACnD,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,kBAAkB,QAAQ;AACzD;AAGO,MAAM,0BAA0B,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,QAAQ,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;KAAU;AACtC", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/api/users/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { createSuccessResponse, handleApiError } from '@/lib/api-response'\nimport { createUserSchema } from '@/lib/validations/user'\n\n// GET /api/users - 获取用户列表\nexport async function GET() {\n  try {\n    const users = await prisma.user.findMany({\n      orderBy: { createdAt: 'desc' },\n      include: {\n        _count: {\n          select: {\n            cards: true,\n            transactions: true,\n            accountBooks: true,\n          },\n        },\n      },\n    })\n\n    return createSuccessResponse(users)\n  } catch (error) {\n    return handleApiError(error)\n  }\n}\n\n// POST /api/users - 创建新用户\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const validatedData = createUserSchema.parse(body)\n\n    const user = await prisma.user.create({\n      data: validatedData,\n      include: {\n        _count: {\n          select: {\n            cards: true,\n            transactions: true,\n            accountBooks: true,\n          },\n        },\n      },\n    })\n\n    return createSuccessResponse(user, '用户创建成功')\n  } catch (error) {\n    return handleApiError(error)\n  }\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvC,SAAS;gBAAE,WAAW;YAAO;YAC7B,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,OAAO;wBACP,cAAc;wBACd,cAAc;oBAChB;gBACF;YACF;QACF;QAEA,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE;IAC/B,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;IACxB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,mIAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC;QAE7C,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;YACN,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,OAAO;wBACP,cAAc;wBACd,cAAc;oBAChB;gBACF;YACF;QACF;QAEA,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM;IACrC,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;IACxB;AACF", "debugId": null}}]}