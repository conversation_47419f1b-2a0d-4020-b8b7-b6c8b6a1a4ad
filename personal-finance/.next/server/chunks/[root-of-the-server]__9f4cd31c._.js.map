{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/api-response.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport interface ApiResponse<T = any> {\n  success: boolean\n  data?: T\n  error?: string\n  message?: string\n  pagination?: {\n    page: number\n    limit: number\n    total: number\n    totalPages: number\n  }\n}\n\nexport class ApiError extends Error {\n  constructor(\n    message: string,\n    public statusCode: number = 500,\n    public code?: string\n  ) {\n    super(message)\n    this.name = 'ApiError'\n  }\n}\n\nexport function createSuccessResponse<T>(\n  data: T,\n  message?: string,\n  pagination?: ApiResponse<T>['pagination']\n): NextResponse<ApiResponse<T>> {\n  return NextResponse.json({\n    success: true,\n    data,\n    message,\n    pagination,\n  })\n}\n\nexport function createErrorResponse(\n  error: string | Error,\n  statusCode: number = 500\n): NextResponse<ApiResponse> {\n  const message = error instanceof Error ? error.message : error\n  \n  return NextResponse.json(\n    {\n      success: false,\n      error: message,\n    },\n    { status: statusCode }\n  )\n}\n\nexport function handleApiError(error: unknown): NextResponse<ApiResponse> {\n  console.error('API Error:', error)\n  \n  if (error instanceof ApiError) {\n    return createErrorResponse(error.message, error.statusCode)\n  }\n  \n  if (error instanceof Error) {\n    return createErrorResponse(error.message, 500)\n  }\n  \n  return createErrorResponse('Internal server error', 500)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAeO,MAAM,iBAAiB;;;IAC5B,YACE,OAAe,EACf,AAAO,aAAqB,GAAG,EAC/B,AAAO,IAAa,CACpB;QACA,KAAK,CAAC,eAHC,aAAA,iBACA,OAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,SAAS,sBACd,IAAO,EACP,OAAgB,EAChB,UAAyC;IAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT;QACA;QACA;IACF;AACF;AAEO,SAAS,oBACd,KAAqB,EACrB,aAAqB,GAAG;IAExB,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAEzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT,OAAO;IACT,GACA;QAAE,QAAQ;IAAW;AAEzB;AAEO,SAAS,eAAe,KAAc;IAC3C,QAAQ,KAAK,CAAC,cAAc;IAE5B,IAAI,iBAAiB,UAAU;QAC7B,OAAO,oBAAoB,MAAM,OAAO,EAAE,MAAM,UAAU;IAC5D;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,oBAAoB,MAAM,OAAO,EAAE;IAC5C;IAEA,OAAO,oBAAoB,yBAAyB;AACtD", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/validations/user.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// 用户注册\nexport const registerSchema = z.object({\n  email: z.string().email('邮箱格式不正确'),\n  password: z.string().min(8, '密码长度至少8位'),\n  name: z.string().min(1, '姓名不能为空').max(50, '姓名不能超过50个字符'),\n})\n\n// 用户登录\nexport const loginSchema = z.object({\n  email: z.string().email('邮箱格式不正确'),\n  password: z.string().min(1, '密码不能为空'),\n})\n\n// 更新用户信息\nexport const updateUserSchema = z.object({\n  name: z.string().min(1, '姓名不能为空').max(50, '姓名不能超过50个字符').optional(),\n  avatar: z.string().url('头像URL格式不正确').optional(),\n})\n\n// 修改密码\nexport const changePasswordSchema = z.object({\n  currentPassword: z.string().min(1, '当前密码不能为空'),\n  newPassword: z.string().min(8, '新密码长度至少8位'),\n  confirmPassword: z.string().min(1, '确认密码不能为空'),\n}).refine((data) => data.newPassword === data.confirmPassword, {\n  message: '两次输入的密码不一致',\n  path: ['confirmPassword'],\n})\n\n// 创建家庭\nexport const createFamilySchema = z.object({\n  name: z.string().min(1, '家庭名称不能为空').max(100, '家庭名称不能超过100个字符'),\n  description: z.string().max(500, '家庭描述不能超过500个字符').optional(),\n  avatar: z.string().url('头像URL格式不正确').optional(),\n})\n\n// 邀请家庭成员\nexport const inviteFamilyMemberSchema = z.object({\n  email: z.string().email('邮箱格式不正确'),\n  role: z.enum(['OWNER', 'ADMIN', 'MEMBER']).default('MEMBER'),\n  message: z.string().max(500, '邀请消息不能超过500个字符').optional(),\n})\n\n// 响应邀请\nexport const respondInvitationSchema = z.object({\n  action: z.enum(['accept', 'decline']),\n})\n\nexport type RegisterInput = z.infer<typeof registerSchema>\nexport type LoginInput = z.infer<typeof loginSchema>\nexport type UpdateUserInput = z.infer<typeof updateUserSchema>\nexport type ChangePasswordInput = z.infer<typeof changePasswordSchema>\nexport type CreateFamilyInput = z.infer<typeof createFamilySchema>\nexport type InviteFamilyMemberInput = z.infer<typeof inviteFamilyMemberSchema>\nexport type RespondInvitationInput = z.infer<typeof respondInvitationSchema>\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAGO,MAAM,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,IAAI;AAC5C;AAGO,MAAM,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAGO,MAAM,mBAAmB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,IAAI,eAAe,QAAQ;IACjE,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,cAAc,QAAQ;AAC/C;AAGO,MAAM,uBAAuB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACnC,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACrC,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,WAAW,KAAK,KAAK,eAAe,EAAE;IAC7D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAGO,MAAM,qBAAqB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,KAAK;IAC7C,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,kBAAkB,QAAQ;IAC3D,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,cAAc,QAAQ;AAC/C;AAGO,MAAM,2BAA2B,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/C,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,MAAM,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;QAAS;KAAS,EAAE,OAAO,CAAC;IACnD,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,kBAAkB,QAAQ;AACzD;AAGO,MAAM,0BAA0B,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,QAAQ,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;KAAU;AACtC", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { NextRequest } from 'next/server'\nimport { prisma } from './prisma'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'\nconst BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12')\n\nexport interface JWTPayload {\n  userId: string\n  email: string\n  familyId?: string\n  familyRole?: string\n}\n\nexport interface AuthUser {\n  id: string\n  email: string\n  name: string\n  avatar?: string\n  familyId?: string\n  familyRole?: string\n  family?: {\n    id: string\n    name: string\n    role: string\n  }\n}\n\n// 密码加密\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, BCRYPT_ROUNDS)\n}\n\n// 密码验证\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\n// 生成JWT令牌\nexport function generateToken(payload: JWTPayload): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })\n}\n\n// 验证JWT令牌\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload\n  } catch (error) {\n    return null\n  }\n}\n\n// 从请求中获取用户信息\nexport async function getUserFromRequest(request: NextRequest): Promise<AuthUser | null> {\n  try {\n    const authHeader = request.headers.get('authorization')\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return null\n    }\n\n    const token = authHeader.substring(7)\n    const payload = verifyToken(token)\n    if (!payload) {\n      return null\n    }\n\n    const user = await prisma.user.findUnique({\n      where: { id: payload.userId },\n      include: {\n        family: {\n          select: {\n            id: true,\n            name: true,\n          },\n        },\n      },\n    })\n\n    if (!user || !user.isActive) {\n      return null\n    }\n\n    return {\n      id: user.id,\n      email: user.email,\n      name: user.name,\n      avatar: user.avatar || undefined,\n      familyId: user.familyId || undefined,\n      familyRole: user.familyRole,\n      family: user.family ? {\n        id: user.family.id,\n        name: user.family.name,\n        role: user.familyRole,\n      } : undefined,\n    }\n  } catch (error) {\n    console.error('Error getting user from request:', error)\n    return null\n  }\n}\n\n// 中间件：验证用户认证\nexport function requireAuth(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(\n        JSON.stringify({ success: false, error: '未授权访问' }),\n        { status: 401, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n    return handler(request, user)\n  }\n}\n\n// 中间件：验证家庭成员权限\nexport function requireFamilyMember(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(\n        JSON.stringify({ success: false, error: '未授权访问' }),\n        { status: 401, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    if (!user.familyId) {\n      return new Response(\n        JSON.stringify({ success: false, error: '您还没有加入任何家庭' }),\n        { status: 403, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n\n// 中间件：验证家庭管理员权限\nexport function requireFamilyAdmin(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(\n        JSON.stringify({ success: false, error: '未授权访问' }),\n        { status: 401, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    if (!user.familyId) {\n      return new Response(\n        JSON.stringify({ success: false, error: '您还没有加入任何家庭' }),\n        { status: 403, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    if (user.familyRole !== 'OWNER' && user.familyRole !== 'ADMIN') {\n      return new Response(\n        JSON.stringify({ success: false, error: '需要管理员权限' }),\n        { status: 403, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n\n// 验证邮箱格式\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// 验证密码强度\nexport function isValidPassword(password: string): { valid: boolean; message?: string } {\n  if (password.length < 8) {\n    return { valid: false, message: '密码长度至少8位' }\n  }\n  \n  if (!/(?=.*[a-z])/.test(password)) {\n    return { valid: false, message: '密码必须包含小写字母' }\n  }\n  \n  if (!/(?=.*[A-Z])/.test(password)) {\n    return { valid: false, message: '密码必须包含大写字母' }\n  }\n  \n  if (!/(?=.*\\d)/.test(password)) {\n    return { valid: false, message: '密码必须包含数字' }\n  }\n  \n  return { valid: true }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AAEA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AACrD,MAAM,gBAAgB,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;AAwBrD,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,cAAc,OAAmB;IAC/C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAe;AACnE;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe,mBAAmB,OAAoB;IAC3D,IAAI;QACF,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;QACnC,MAAM,UAAU,YAAY;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;YACF;QACF;QAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC3B,OAAO;QACT;QAEA,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,QAAQ,KAAK,MAAM,IAAI;YACvB,UAAU,KAAK,QAAQ,IAAI;YAC3B,YAAY,KAAK,UAAU;YAC3B,QAAQ,KAAK,MAAM,GAAG;gBACpB,IAAI,KAAK,MAAM,CAAC,EAAE;gBAClB,MAAM,KAAK,MAAM,CAAC,IAAI;gBACtB,MAAM,KAAK,UAAU;YACvB,IAAI;QACN;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAGO,SAAS,YAAY,OAAoE;IAC9F,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,mBAAmB;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAChD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QACA,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,SAAS,oBAAoB,OAAoE;IACtG,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,mBAAmB;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAChD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAa,IACrD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,SAAS,mBAAmB,OAAoE;IACrG,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,mBAAmB;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAChD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAa,IACrD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,KAAK,SAAS;YAC9D,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAU,IAClD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,gBAAgB,QAAgB;IAC9C,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO;YAAE,OAAO;YAAO,SAAS;QAAW;IAC7C;IAEA,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW;QACjC,OAAO;YAAE,OAAO;YAAO,SAAS;QAAa;IAC/C;IAEA,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW;QACjC,OAAO;YAAE,OAAO;YAAO,SAAS;QAAa;IAC/C;IAEA,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW;QAC9B,OAAO;YAAE,OAAO;YAAO,SAAS;QAAW;IAC7C;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB", "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/api/families/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { createSuccessResponse, handleApiError, ApiError } from '@/lib/api-response'\nimport { createFamilySchema } from '@/lib/validations/user'\nimport { requireAuth } from '@/lib/auth'\nimport { FamilyRole } from '@prisma/client'\n\n// GET /api/families - 获取用户的家庭信息\nexport const GET = requireAuth(async (request: NextRequest, user) => {\n  try {\n    if (!user.familyId) {\n      return createSuccessResponse(null, '您还没有加入任何家庭')\n    }\n\n    const family = await prisma.family.findUnique({\n      where: { id: user.familyId },\n      include: {\n        creator: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n            avatar: true,\n          },\n        },\n        members: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n            avatar: true,\n            familyRole: true,\n            createdAt: true,\n          },\n          orderBy: {\n            familyRole: 'asc',\n          },\n        },\n        invitations: {\n          where: {\n            status: 'PENDING',\n            expiresAt: {\n              gt: new Date(),\n            },\n          },\n          include: {\n            sender: {\n              select: {\n                id: true,\n                name: true,\n                email: true,\n              },\n            },\n          },\n          orderBy: {\n            createdAt: 'desc',\n          },\n        },\n        _count: {\n          select: {\n            members: true,\n          },\n        },\n      },\n    })\n\n    if (!family) {\n      throw new ApiError('家庭不存在', 404)\n    }\n\n    return createSuccessResponse(family)\n  } catch (error) {\n    return handleApiError(error)\n  }\n})\n\n// POST /api/families - 创建新家庭\nexport const POST = requireAuth(async (request: NextRequest, user) => {\n  try {\n    // 检查用户是否已经在家庭中\n    if (user.familyId) {\n      throw new ApiError('您已经在一个家庭中，请先退出当前家庭', 400)\n    }\n\n    const body = await request.json()\n    const validatedData = createFamilySchema.parse(body)\n\n    // 创建家庭\n    const family = await prisma.family.create({\n      data: {\n        name: validatedData.name,\n        description: validatedData.description,\n        avatar: validatedData.avatar,\n        creatorId: user.id,\n      },\n      include: {\n        creator: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n            avatar: true,\n          },\n        },\n        _count: {\n          select: {\n            members: true,\n          },\n        },\n      },\n    })\n\n    // 将创建者加入家庭并设为所有者\n    await prisma.user.update({\n      where: { id: user.id },\n      data: {\n        familyId: family.id,\n        familyRole: FamilyRole.OWNER,\n      },\n    })\n\n    return createSuccessResponse(family, '家庭创建成功')\n  } catch (error) {\n    return handleApiError(error)\n  }\n})\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;;;;;;AAGO,MAAM,MAAM,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE,OAAO,SAAsB;IAC1D,IAAI;QACF,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM;QACrC;QAEA,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YAC5C,OAAO;gBAAE,IAAI,KAAK,QAAQ;YAAC;YAC3B,SAAS;gBACP,SAAS;oBACP,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,QAAQ;oBACV;gBACF;gBACA,SAAS;oBACP,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,QAAQ;wBACR,YAAY;wBACZ,WAAW;oBACb;oBACA,SAAS;wBACP,YAAY;oBACd;gBACF;gBACA,aAAa;oBACX,OAAO;wBACL,QAAQ;wBACR,WAAW;4BACT,IAAI,IAAI;wBACV;oBACF;oBACA,SAAS;wBACP,QAAQ;4BACN,QAAQ;gCACN,IAAI;gCACJ,MAAM;gCACN,OAAO;4BACT;wBACF;oBACF;oBACA,SAAS;wBACP,WAAW;oBACb;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,SAAS;oBACX;gBACF;YACF;QACF;QAEA,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,+HAAA,CAAA,WAAQ,CAAC,SAAS;QAC9B;QAEA,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE;IAC/B,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;IACxB;AACF;AAGO,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE,OAAO,SAAsB;IAC3D,IAAI;QACF,eAAe;QACf,IAAI,KAAK,QAAQ,EAAE;YACjB,MAAM,IAAI,+HAAA,CAAA,WAAQ,CAAC,sBAAsB;QAC3C;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,mIAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC;QAE/C,OAAO;QACP,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,MAAM,CAAC;YACxC,MAAM;gBACJ,MAAM,cAAc,IAAI;gBACxB,aAAa,cAAc,WAAW;gBACtC,QAAQ,cAAc,MAAM;gBAC5B,WAAW,KAAK,EAAE;YACpB;YACA,SAAS;gBACP,SAAS;oBACP,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,SAAS;oBACX;gBACF;YACF;QACF;QAEA,iBAAiB;QACjB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE,IAAI,KAAK,EAAE;YAAC;YACrB,MAAM;gBACJ,UAAU,OAAO,EAAE;gBACnB,YAAY,6HAAA,CAAA,aAAU,CAAC,KAAK;YAC9B;QACF;QAEA,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ;IACvC,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;IACxB;AACF", "debugId": null}}]}