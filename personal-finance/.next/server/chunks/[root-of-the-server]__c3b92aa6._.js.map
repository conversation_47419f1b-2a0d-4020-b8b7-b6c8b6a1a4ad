{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/api-response.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport interface ApiResponse<T = any> {\n  success: boolean\n  data?: T\n  error?: string\n  message?: string\n  pagination?: {\n    page: number\n    limit: number\n    total: number\n    totalPages: number\n  }\n}\n\nexport class ApiError extends Error {\n  constructor(\n    message: string,\n    public statusCode: number = 500,\n    public code?: string\n  ) {\n    super(message)\n    this.name = 'ApiError'\n  }\n}\n\nexport function createSuccessResponse<T>(\n  data: T,\n  message?: string,\n  pagination?: ApiResponse<T>['pagination']\n): NextResponse<ApiResponse<T>> {\n  return NextResponse.json({\n    success: true,\n    data,\n    message,\n    pagination,\n  })\n}\n\nexport function createErrorResponse(\n  error: string | Error,\n  statusCode: number = 500\n): NextResponse<ApiResponse> {\n  const message = error instanceof Error ? error.message : error\n  \n  return NextResponse.json(\n    {\n      success: false,\n      error: message,\n    },\n    { status: statusCode }\n  )\n}\n\nexport function handleApiError(error: unknown): NextResponse<ApiResponse> {\n  console.error('API Error:', error)\n  \n  if (error instanceof ApiError) {\n    return createErrorResponse(error.message, error.statusCode)\n  }\n  \n  if (error instanceof Error) {\n    return createErrorResponse(error.message, 500)\n  }\n  \n  return createErrorResponse('Internal server error', 500)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAeO,MAAM,iBAAiB;;;IAC5B,YACE,OAAe,EACf,AAAO,aAAqB,GAAG,EAC/B,AAAO,IAAa,CACpB;QACA,KAAK,CAAC,eAHC,aAAA,iBACA,OAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,SAAS,sBACd,IAAO,EACP,OAAgB,EAChB,UAAyC;IAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT;QACA;QACA;IACF;AACF;AAEO,SAAS,oBACd,KAAqB,EACrB,aAAqB,GAAG;IAExB,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAEzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT,OAAO;IACT,GACA;QAAE,QAAQ;IAAW;AAEzB;AAEO,SAAS,eAAe,KAAc;IAC3C,QAAQ,KAAK,CAAC,cAAc;IAE5B,IAAI,iBAAiB,UAAU;QAC7B,OAAO,oBAAoB,MAAM,OAAO,EAAE,MAAM,UAAU;IAC5D;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,oBAAoB,MAAM,OAAO,EAAE;IAC5C;IAEA,OAAO,oBAAoB,yBAAyB;AACtD", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/validations/transaction.ts"], "sourcesContent": ["import { z } from 'zod'\nimport { TransactionType, TransactionStatus } from '@prisma/client'\n\nexport const createTransactionSchema = z.object({\n  type: z.nativeEnum(TransactionType),\n  amount: z.number().positive('金额必须大于0'),\n  description: z.string().min(1, '交易描述不能为空').max(200, '交易描述不能超过200个字符'),\n  category: z.string().max(50, '分类不能超过50个字符').optional(),\n  subcategory: z.string().max(50, '子分类不能超过50个字符').optional(),\n  transactionDate: z.string().datetime('交易时间格式不正确'),\n  location: z.string().max(100, '交易地点不能超过100个字符').optional(),\n  notes: z.string().max(500, '备注不能超过500个字符').optional(),\n  userId: z.string().cuid('用户ID格式不正确'),\n  cardId: z.string().cuid('卡片ID格式不正确').optional(),\n  externalId: z.string().max(100, '外部ID不能超过100个字符').optional(),\n})\n\nexport const updateTransactionSchema = z.object({\n  type: z.nativeEnum(TransactionType).optional(),\n  status: z.nativeEnum(TransactionStatus).optional(),\n  amount: z.number().positive('金额必须大于0').optional(),\n  description: z.string().min(1, '交易描述不能为空').max(200, '交易描述不能超过200个字符').optional(),\n  category: z.string().max(50, '分类不能超过50个字符').optional(),\n  subcategory: z.string().max(50, '子分类不能超过50个字符').optional(),\n  transactionDate: z.string().datetime('交易时间格式不正确').optional(),\n  location: z.string().max(100, '交易地点不能超过100个字符').optional(),\n  notes: z.string().max(500, '备注不能超过500个字符').optional(),\n  cardId: z.string().cuid('卡片ID格式不正确').optional(),\n})\n\nexport const transactionQuerySchema = z.object({\n  page: z.string().transform(Number).pipe(z.number().min(1)).default('1'),\n  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).default('20'),\n  type: z.nativeEnum(TransactionType).optional(),\n  status: z.nativeEnum(TransactionStatus).optional(),\n  cardId: z.string().cuid().optional(),\n  category: z.string().optional(),\n  startDate: z.string().datetime().optional(),\n  endDate: z.string().datetime().optional(),\n  search: z.string().optional(),\n})\n\nexport type CreateTransactionInput = z.infer<typeof createTransactionSchema>\nexport type UpdateTransactionInput = z.infer<typeof updateTransactionSchema>\nexport type TransactionQuery = z.infer<typeof transactionQuerySchema>\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,MAAM,0BAA0B,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,MAAM,oKAAA,CAAA,IAAC,CAAC,UAAU,CAAC,6HAAA,CAAA,kBAAe;IAClC,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5B,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,KAAK;IACpD,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,eAAe,QAAQ;IACpD,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,gBAAgB,QAAQ;IACxD,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACrC,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,kBAAkB,QAAQ;IACxD,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,gBAAgB,QAAQ;IACnD,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IACxB,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,QAAQ;IAC7C,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,kBAAkB,QAAQ;AAC5D;AAEO,MAAM,0BAA0B,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,MAAM,oKAAA,CAAA,IAAC,CAAC,UAAU,CAAC,6HAAA,CAAA,kBAAe,EAAE,QAAQ;IAC5C,QAAQ,oKAAA,CAAA,IAAC,CAAC,UAAU,CAAC,6HAAA,CAAA,oBAAiB,EAAE,QAAQ;IAChD,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,WAAW,QAAQ;IAC/C,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,KAAK,kBAAkB,QAAQ;IAC9E,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,eAAe,QAAQ;IACpD,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,gBAAgB,QAAQ;IACxD,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,QAAQ;IAC1D,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,kBAAkB,QAAQ;IACxD,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,gBAAgB,QAAQ;IACnD,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,QAAQ;AAC/C;AAEO,MAAM,yBAAyB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7C,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,IAAI,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;IACnE,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,IAAI,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,OAAO,CAAC;IAC7E,MAAM,oKAAA,CAAA,IAAC,CAAC,UAAU,CAAC,6HAAA,CAAA,kBAAe,EAAE,QAAQ;IAC5C,QAAQ,oKAAA,CAAA,IAAC,CAAC,UAAU,CAAC,6HAAA,CAAA,oBAAiB,EAAE,QAAQ;IAChD,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAClC,UAAU,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,WAAW,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACzC,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACvC,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC7B", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/api/transactions/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { createSuccessResponse, handleApiError } from '@/lib/api-response'\nimport { createTransactionSchema, transactionQuerySchema } from '@/lib/validations/transaction'\n\n// GET /api/transactions - 获取交易记录列表\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const query = transactionQuerySchema.parse(Object.fromEntries(searchParams))\n\n    const where: any = {}\n    if (query.type) where.type = query.type\n    if (query.status) where.status = query.status\n    if (query.cardId) where.cardId = query.cardId\n    if (query.category) where.category = query.category\n    if (query.startDate || query.endDate) {\n      where.transactionDate = {}\n      if (query.startDate) where.transactionDate.gte = new Date(query.startDate)\n      if (query.endDate) where.transactionDate.lte = new Date(query.endDate)\n    }\n    if (query.search) {\n      where.OR = [\n        { description: { contains: query.search } },\n        { notes: { contains: query.search } },\n        { location: { contains: query.search } },\n      ]\n    }\n\n    const [transactions, total] = await Promise.all([\n      prisma.transaction.findMany({\n        where,\n        include: {\n          user: {\n            select: {\n              id: true,\n              name: true,\n            },\n          },\n          card: {\n            select: {\n              id: true,\n              name: true,\n              type: true,\n            },\n          },\n          linkedTransaction: {\n            select: {\n              id: true,\n              description: true,\n              amount: true,\n            },\n          },\n        },\n        orderBy: { transactionDate: 'desc' },\n        skip: (query.page - 1) * query.limit,\n        take: query.limit,\n      }),\n      prisma.transaction.count({ where }),\n    ])\n\n    const pagination = {\n      page: query.page,\n      limit: query.limit,\n      total,\n      totalPages: Math.ceil(total / query.limit),\n    }\n\n    return createSuccessResponse(transactions, undefined, pagination)\n  } catch (error) {\n    return handleApiError(error)\n  }\n}\n\n// POST /api/transactions - 创建新交易记录\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const validatedData = createTransactionSchema.parse(body)\n\n    // 转换日期字符串为Date对象\n    const transactionData = {\n      ...validatedData,\n      transactionDate: new Date(validatedData.transactionDate),\n    }\n\n    const transaction = await prisma.transaction.create({\n      data: transactionData,\n      include: {\n        user: {\n          select: {\n            id: true,\n            name: true,\n          },\n        },\n        card: {\n          select: {\n            id: true,\n            name: true,\n            type: true,\n          },\n        },\n      },\n    })\n\n    return createSuccessResponse(transaction, '交易记录创建成功')\n  } catch (error) {\n    return handleApiError(error)\n  }\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,0IAAA,CAAA,yBAAsB,CAAC,KAAK,CAAC,OAAO,WAAW,CAAC;QAE9D,MAAM,QAAa,CAAC;QACpB,IAAI,MAAM,IAAI,EAAE,MAAM,IAAI,GAAG,MAAM,IAAI;QACvC,IAAI,MAAM,MAAM,EAAE,MAAM,MAAM,GAAG,MAAM,MAAM;QAC7C,IAAI,MAAM,MAAM,EAAE,MAAM,MAAM,GAAG,MAAM,MAAM;QAC7C,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ,GAAG,MAAM,QAAQ;QACnD,IAAI,MAAM,SAAS,IAAI,MAAM,OAAO,EAAE;YACpC,MAAM,eAAe,GAAG,CAAC;YACzB,IAAI,MAAM,SAAS,EAAE,MAAM,eAAe,CAAC,GAAG,GAAG,IAAI,KAAK,MAAM,SAAS;YACzE,IAAI,MAAM,OAAO,EAAE,MAAM,eAAe,CAAC,GAAG,GAAG,IAAI,KAAK,MAAM,OAAO;QACvE;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,EAAE,GAAG;gBACT;oBAAE,aAAa;wBAAE,UAAU,MAAM,MAAM;oBAAC;gBAAE;gBAC1C;oBAAE,OAAO;wBAAE,UAAU,MAAM,MAAM;oBAAC;gBAAE;gBACpC;oBAAE,UAAU;wBAAE,UAAU,MAAM,MAAM;oBAAC;gBAAE;aACxC;QACH;QAEA,MAAM,CAAC,cAAc,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC9C,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;gBAC1B;gBACA,SAAS;oBACP,MAAM;wBACJ,QAAQ;4BACN,IAAI;4BACJ,MAAM;wBACR;oBACF;oBACA,MAAM;wBACJ,QAAQ;4BACN,IAAI;4BACJ,MAAM;4BACN,MAAM;wBACR;oBACF;oBACA,mBAAmB;wBACjB,QAAQ;4BACN,IAAI;4BACJ,aAAa;4BACb,QAAQ;wBACV;oBACF;gBACF;gBACA,SAAS;oBAAE,iBAAiB;gBAAO;gBACnC,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,MAAM,KAAK;gBACpC,MAAM,MAAM,KAAK;YACnB;YACA,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,KAAK,CAAC;gBAAE;YAAM;SAClC;QAED,MAAM,aAAa;YACjB,MAAM,MAAM,IAAI;YAChB,OAAO,MAAM,KAAK;YAClB;YACA,YAAY,KAAK,IAAI,CAAC,QAAQ,MAAM,KAAK;QAC3C;QAEA,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc,WAAW;IACxD,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;IACxB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,0IAAA,CAAA,0BAAuB,CAAC,KAAK,CAAC;QAEpD,iBAAiB;QACjB,MAAM,kBAAkB;YACtB,GAAG,aAAa;YAChB,iBAAiB,IAAI,KAAK,cAAc,eAAe;QACzD;QAEA,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YAClD,MAAM;YACN,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;gBACA,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,MAAM;oBACR;gBACF;YACF;QACF;QAEA,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa;IAC5C,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;IACxB;AACF", "debugId": null}}]}