module.exports = {

"[project]/.next-internal/server/app/api/bills/import/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/api-response.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiError": (()=>ApiError),
    "createErrorResponse": (()=>createErrorResponse),
    "createSuccessResponse": (()=>createSuccessResponse),
    "handleApiError": (()=>handleApiError)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
class ApiError extends Error {
    statusCode;
    code;
    constructor(message, statusCode = 500, code){
        super(message), this.statusCode = statusCode, this.code = code;
        this.name = 'ApiError';
    }
}
function createSuccessResponse(data, message, pagination) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: true,
        data,
        message,
        pagination
    });
}
function createErrorResponse(error, statusCode = 500) {
    const message = error instanceof Error ? error.message : error;
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: false,
        error: message
    }, {
        status: statusCode
    });
}
function handleApiError(error) {
    console.error('API Error:', error);
    if (error instanceof ApiError) {
        return createErrorResponse(error.message, error.statusCode);
    }
    if (error instanceof Error) {
        return createErrorResponse(error.message, 500);
    }
    return createErrorResponse('Internal server error', 500);
}
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/lib/parsers/types.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// 账单解析器类型定义
__turbopack_context__.s({
    "CARD_TYPE_CONFIGS": (()=>CARD_TYPE_CONFIGS)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const CARD_TYPE_CONFIGS = {
    [__TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["CardType"].DEBIT_CARD]: {
        positiveIsIncome: true,
        defaultCategories: {
            '转账': '转账',
            '取现': '取现',
            '存款': '存款',
            '利息': '利息收入'
        }
    },
    [__TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["CardType"].CREDIT_CARD]: {
        positiveIsIncome: false,
        defaultCategories: {
            '还款': '信用卡还款',
            '消费': '日常消费',
            '取现': '信用卡取现'
        }
    },
    [__TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["CardType"].WECHAT]: {
        positiveIsIncome: true,
        defaultCategories: {
            '转账': '转账',
            '红包': '红包',
            '消费': '日常消费',
            '充值': '充值'
        }
    },
    [__TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["CardType"].ALIPAY]: {
        positiveIsIncome: true,
        defaultCategories: {
            '转账': '转账',
            '红包': '红包',
            '消费': '日常消费',
            '充值': '充值'
        }
    },
    [__TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["CardType"].CASH]: {
        positiveIsIncome: true,
        defaultCategories: {}
    },
    [__TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["CardType"].OTHER]: {
        positiveIsIncome: true,
        defaultCategories: {}
    }
};
}}),
"[project]/src/lib/parsers/base.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// 基础解析器类
__turbopack_context__.s({
    "BaseBillParser": (()=>BaseBillParser)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$types$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/parsers/types.ts [app-route] (ecmascript)");
;
;
class BaseBillParser {
    /**
   * 检查是否支持该文件
   */ canParse(fileName, cardType) {
        // 检查卡片类型
        if (!this.supportedCardTypes.includes(cardType)) {
            return false;
        }
        // 检查文件扩展名
        const ext = fileName.toLowerCase().split('.').pop();
        return ext ? this.supportedFileTypes.includes(ext) : false;
    }
    /**
   * 标准化交易数据
   */ standardizeTransaction(raw, cardType) {
        const config = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$types$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CARD_TYPE_CONFIGS"][cardType];
        // 确定交易类型和金额
        let type;
        let amount;
        if (raw.amount === 0) {
            type = __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["TransactionType"].TRANSFER;
            amount = 0;
        } else if (config.positiveIsIncome) {
            // 借记卡、微信、支付宝等：正数=收入，负数=支出
            if (raw.amount > 0) {
                type = __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["TransactionType"].INCOME;
                amount = raw.amount;
            } else {
                type = __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["TransactionType"].EXPENSE;
                amount = Math.abs(raw.amount);
            }
        } else {
            // 信用卡：正数=支出，负数=收入
            if (raw.amount > 0) {
                type = __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["TransactionType"].EXPENSE;
                amount = raw.amount;
            } else {
                type = __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["TransactionType"].INCOME;
                amount = Math.abs(raw.amount);
            }
        }
        return {
            externalId: raw.externalId,
            type,
            amount,
            description: raw.description.trim(),
            category: raw.category || this.inferCategory(raw.description, config),
            subcategory: raw.subcategory,
            transactionDate: new Date(raw.date),
            location: raw.location,
            notes: raw.notes,
            balance: raw.balance
        };
    }
    /**
   * 推断交易分类
   */ inferCategory(description, config) {
        for (const [keyword, category] of Object.entries(config.defaultCategories)){
            if (description.includes(keyword)) {
                return category;
            }
        }
        return '其他';
    }
    /**
   * 解析CSV内容
   */ parseCSV(content) {
        const lines = content.split('\n').filter((line)=>line.trim());
        return lines.map((line)=>{
            // 简单的CSV解析，处理逗号分隔和引号包围的字段
            const fields = [];
            let current = '';
            let inQuotes = false;
            for(let i = 0; i < line.length; i++){
                const char = line[i];
                if (char === '"') {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    fields.push(current.trim());
                    current = '';
                } else {
                    current += char;
                }
            }
            fields.push(current.trim());
            return fields.map((field)=>field.replace(/^"|"$/g, '')) // 移除引号
            ;
        });
    }
    /**
   * 验证日期格式
   */ parseDate(dateStr) {
        // 支持多种日期格式
        const formats = [
            /^\d{4}-\d{2}-\d{2}$/,
            /^\d{4}\/\d{2}\/\d{2}$/,
            /^\d{4}\.\d{2}\.\d{2}$/,
            /^\d{2}-\d{2}-\d{4}$/,
            /^\d{2}\/\d{2}\/\d{4}$/
        ];
        for (const format of formats){
            if (format.test(dateStr)) {
                const date = new Date(dateStr);
                if (!isNaN(date.getTime())) {
                    return date;
                }
            }
        }
        return null;
    }
    /**
   * 解析金额
   */ parseAmount(amountStr) {
        // 移除货币符号和空格
        const cleaned = amountStr.replace(/[￥$¥,\s]/g, '');
        const amount = parseFloat(cleaned);
        return isNaN(amount) ? 0 : amount;
    }
    /**
   * 生成解析结果
   */ createParseResult(transactions, errors) {
        return {
            success: errors.length === 0,
            transactions,
            errors,
            summary: {
                totalCount: transactions.length + errors.length,
                successCount: transactions.length,
                errorCount: errors.length,
                duplicateCount: 0
            }
        };
    }
}
}}),
"[project]/src/lib/parsers/wechat.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// 微信账单解析器
__turbopack_context__.s({
    "WechatParser": (()=>WechatParser)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$base$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/parsers/base.ts [app-route] (ecmascript)");
;
;
class WechatParser extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$base$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BaseBillParser"] {
    name = '微信支付账单解析器';
    supportedCardTypes = [
        __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["CardType"].WECHAT
    ];
    supportedFileTypes = [
        'csv'
    ];
    async parse(fileContent, cardType) {
        try {
            const content = fileContent.toString('utf-8');
            const wechatBill = this.parseWeChatBill(content);
            // 转换微信交易为标准交易格式
            const transactions = [];
            const errors = [];
            wechatBill.transactions.forEach((wechatTx, index)=>{
                try {
                    const transaction = this.convertWeChatTransaction(wechatTx);
                    if (transaction) {
                        transactions.push(transaction);
                    }
                } catch (error) {
                    errors.push(`第${index + 1}行解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
                }
            });
            // 标准化交易数据
            const standardTransactions = transactions.map((raw)=>this.standardizeTransaction(raw, cardType));
            return this.createParseResult(standardTransactions, errors);
        } catch (error) {
            return this.createParseResult([], [
                `文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`
            ]);
        }
    }
    parseWeChatBill(csvContent) {
        const lines = csvContent.split('\n').map((line)=>line.trim());
        // Parse metadata
        const metadata = {
            nickname: this.extractValue(lines, '微信昵称：'),
            startDate: this.extractValue(lines, '起始时间：'),
            endDate: this.extractValue(lines, '终止时间：'),
            exportType: this.extractValue(lines, '导出类型：'),
            exportTime: this.extractValue(lines, '导出时间：'),
            summary: {
                totalCount: this.extractNumber(lines, '共'),
                incomeCount: this.extractNumber(lines, '收入：'),
                incomeAmount: this.extractAmount(lines, '收入：'),
                expenseCount: this.extractNumber(lines, '支出：'),
                expenseAmount: this.extractAmount(lines, '支出：'),
                neutralCount: this.extractNumber(lines, '中性交易：'),
                neutralAmount: this.extractAmount(lines, '中性交易：')
            }
        };
        // Find transactions start index
        const transactionStartIndex = lines.findIndex((line)=>line.startsWith('交易时间,交易类型,交易对方'));
        if (transactionStartIndex === -1) {
            throw new Error('未找到交易数据表头');
        }
        // Parse transactions
        const transactions = lines.slice(transactionStartIndex + 1).filter((line)=>line && !line.startsWith('-') && !line.startsWith('注：') && !line.startsWith('共')).map((line)=>this.parseWeChatTransactionLine(line)).filter((tx)=>tx !== null);
        return {
            metadata,
            transactions
        };
    }
    parseWeChatTransactionLine(line) {
        // 更精确的CSV解析，处理引号内的逗号
        const fields = [];
        let field = '';
        let inQuotes = false;
        for(let i = 0; i < line.length; i++){
            const char = line[i];
            if (char === '"') {
                inQuotes = !inQuotes;
                continue;
            }
            if (char === ',' && !inQuotes) {
                fields.push(field.trim());
                field = '';
                continue;
            }
            field += char;
        }
        fields.push(field.trim()) // 添加最后一个字段
        ;
        if (fields.length < 11) {
            return null // 字段不足，跳过
            ;
        }
        const [timestamp, type, counterparty, goods, direction, amount, paymentMethod, status, transactionId, merchantOrderId, memo] = fields;
        return {
            timestamp,
            type,
            counterparty,
            goods,
            direction: this.parseDirection(direction),
            amount: this.parseAmount(amount),
            paymentMethod,
            status,
            transactionId,
            merchantOrderId,
            memo
        };
    }
    convertWeChatTransaction(wechatTx) {
        // 只处理成功的交易
        if (wechatTx.status !== '支付成功' && wechatTx.status !== '已收钱' && wechatTx.status !== '交易成功') {
            return null;
        }
        // 解析日期
        const date = this.parseWeChatDate(wechatTx.timestamp);
        if (!date) {
            throw new Error(`无效的时间格式: ${wechatTx.timestamp}`);
        }
        // 根据方向确定金额符号
        let amount = Math.abs(wechatTx.amount);
        if (wechatTx.direction === 'expense') {
            amount = -amount;
        }
        // 构建描述
        let description = wechatTx.type;
        if (wechatTx.goods) {
            description += ` - ${wechatTx.goods}`;
        }
        if (wechatTx.counterparty) {
            description += ` - ${wechatTx.counterparty}`;
        }
        // 推断分类
        let category = '其他';
        if (wechatTx.type.includes('转账')) category = '转账';
        else if (wechatTx.type.includes('红包')) category = '红包';
        else if (wechatTx.type.includes('充值')) category = '充值';
        else if (wechatTx.type.includes('提现')) category = '提现';
        else if (wechatTx.type.includes('还款')) category = '还款';
        else if (wechatTx.type.includes('消费') || wechatTx.type.includes('支付')) category = '日常消费';
        return {
            externalId: wechatTx.transactionId,
            date: date.toISOString(),
            amount,
            description: description.trim(),
            category,
            location: wechatTx.counterparty,
            notes: `支付方式: ${wechatTx.paymentMethod}${wechatTx.memo ? ` | 备注: ${wechatTx.memo}` : ''}`,
            rawData: wechatTx
        };
    }
    extractValue(lines, prefix) {
        const line = lines.find((line)=>line.startsWith(prefix));
        return line ? line.split('：')[1]?.replace(/[\[\]]/g, '') || '' : '';
    }
    extractNumber(lines, prefix) {
        const line = lines.find((line)=>line.startsWith(prefix));
        return line ? parseInt(line.match(/\d+/)?.[0] || '0') : 0;
    }
    extractAmount(lines, prefix) {
        const line = lines.find((line)=>line.startsWith(prefix));
        return line ? this.parseAmount(line.match(/\d+\.\d+/)?.[0] || '0') : 0;
    }
    parseDirection(direction) {
        if (direction.includes('收入')) return 'income';
        if (direction.includes('支出')) return 'expense';
        return 'neutral';
    }
    parseWeChatDate(dateStr) {
        // 微信时间格式: 2024-01-15 14:30:00
        const wechatFormat = /(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})/;
        const match = dateStr.match(wechatFormat);
        if (match) {
            const [, year, month, day, hour, minute, second] = match;
            return new Date(parseInt(year), parseInt(month) - 1, parseInt(day), parseInt(hour), parseInt(minute), parseInt(second));
        }
        // 如果不匹配微信格式，尝试其他格式
        const date = this.parseDate(dateStr);
        return date || null;
    }
}
}}),
"[project]/src/lib/parsers/alipay.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// 支付宝账单解析器
__turbopack_context__.s({
    "AlipayParser": (()=>AlipayParser)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$base$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/parsers/base.ts [app-route] (ecmascript)");
;
;
class AlipayParser extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$base$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BaseBillParser"] {
    name = '支付宝账单解析器';
    supportedCardTypes = [
        __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["CardType"].ALIPAY
    ];
    supportedFileTypes = [
        'csv'
    ];
    async parse(fileContent, cardType) {
        try {
            const content = fileContent.toString('utf-8');
            const lines = this.parseCSV(content);
            // 查找表头行
            let headerIndex = -1;
            for(let i = 0; i < lines.length; i++){
                if (lines[i].some((cell)=>cell.includes('交易时间') || cell.includes('交易创建时间'))) {
                    headerIndex = i;
                    break;
                }
            }
            if (headerIndex === -1) {
                return this.createParseResult([], [
                    '未找到有效的表头'
                ]);
            }
            const headers = lines[headerIndex];
            const dataLines = lines.slice(headerIndex + 1);
            // 解析每一行数据
            const transactions = [];
            const errors = [];
            for(let i = 0; i < dataLines.length; i++){
                try {
                    const row = dataLines[i];
                    if (row.length < headers.length || row.every((cell)=>!cell.trim())) {
                        continue; // 跳过空行或不完整行
                    }
                    const transaction = this.parseAlipayRow(row, headers);
                    if (transaction) {
                        transactions.push(transaction);
                    }
                } catch (error) {
                    errors.push(`第${headerIndex + i + 2}行解析失败: ${error}`);
                }
            }
            // 标准化交易数据
            const standardTransactions = transactions.map((raw)=>this.standardizeTransaction(raw, cardType));
            return this.createParseResult(standardTransactions, errors);
        } catch (error) {
            return this.createParseResult([], [
                `文件解析失败: ${error}`
            ]);
        }
    }
    parseAlipayRow(row, headers) {
        // 创建字段映射
        const fieldMap = {};
        headers.forEach((header, index)=>{
            if (row[index]) {
                fieldMap[header.trim()] = row[index].trim();
            }
        });
        // 支付宝账单常见字段映射
        const timeField = this.findField(fieldMap, [
            '交易时间',
            '交易创建时间',
            '时间'
        ]);
        const typeField = this.findField(fieldMap, [
            '交易分类',
            '类型'
        ]);
        const counterpartyField = this.findField(fieldMap, [
            '交易对方',
            '对方'
        ]);
        const productField = this.findField(fieldMap, [
            '商品名称',
            '商品说明',
            '备注'
        ]);
        const statusField = this.findField(fieldMap, [
            '交易状态',
            '状态'
        ]);
        const amountField = this.findField(fieldMap, [
            '金额',
            '收支金额'
        ]);
        const balanceField = this.findField(fieldMap, [
            '账户余额',
            '余额'
        ]);
        const orderField = this.findField(fieldMap, [
            '交易订单号',
            '订单号'
        ]);
        if (!timeField || !amountField) {
            return null;
        }
        // 解析时间
        const date = this.parseDate(timeField);
        if (!date) {
            throw new Error(`无效的时间格式: ${timeField}`);
        }
        // 解析金额
        let amount = this.parseAmount(amountField);
        // 支付宝账单中，通常用+/-符号表示收支
        // 或者通过交易分类判断
        if (amountField.startsWith('-') || amountField.startsWith('－')) {
            amount = -Math.abs(amount);
        } else if (amountField.startsWith('+') || amountField.startsWith('＋')) {
            amount = Math.abs(amount);
        } else if (typeField) {
            // 根据交易类型判断
            if (typeField.includes('支出') || typeField.includes('消费') || typeField.includes('转账给')) {
                amount = -Math.abs(amount);
            } else if (typeField.includes('收入') || typeField.includes('转账收款')) {
                amount = Math.abs(amount);
            }
        }
        // 构建描述
        let description = typeField || '支付宝交易';
        if (counterpartyField) {
            description += ` - ${counterpartyField}`;
        }
        if (productField) {
            description += ` - ${productField}`;
        }
        // 推断分类
        let category = '其他';
        if (typeField) {
            if (typeField.includes('转账')) category = '转账';
            else if (typeField.includes('红包')) category = '红包';
            else if (typeField.includes('充值')) category = '充值';
            else if (typeField.includes('提现')) category = '提现';
            else if (typeField.includes('还款')) category = '还款';
            else if (typeField.includes('消费') || typeField.includes('支付')) category = '日常消费';
            else if (typeField.includes('理财')) category = '投资理财';
            else if (typeField.includes('保险')) category = '保险';
        }
        // 解析余额
        let balance;
        if (balanceField) {
            balance = this.parseAmount(balanceField);
        }
        return {
            externalId: orderField,
            date: date.toISOString(),
            amount,
            description: description.trim(),
            category,
            location: counterpartyField,
            balance,
            notes: `状态: ${statusField || '未知'}`,
            rawData: fieldMap
        };
    }
    findField(fieldMap, possibleNames) {
        for (const name of possibleNames){
            for (const key of Object.keys(fieldMap)){
                if (key.includes(name)) {
                    return fieldMap[key];
                }
            }
        }
        return undefined;
    }
}
}}),
"[project]/src/lib/parsers/cmb-debit.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// 招商银行借记卡账单解析器
__turbopack_context__.s({
    "CmbDebitParser": (()=>CmbDebitParser)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$base$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/parsers/base.ts [app-route] (ecmascript)");
;
;
class CmbDebitParser extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$base$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BaseBillParser"] {
    name = '招商银行借记卡账单解析器';
    supportedCardTypes = [
        __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["CardType"].DEBIT_CARD
    ];
    supportedFileTypes = [
        'csv',
        'xls',
        'xlsx'
    ];
    async parse(fileContent, cardType) {
        try {
            const content = fileContent.toString('utf-8');
            const lines = this.parseCSV(content);
            // 查找表头行
            let headerIndex = -1;
            for(let i = 0; i < lines.length; i++){
                if (lines[i].some((cell)=>cell.includes('交易日期') || cell.includes('记账日期') || cell.includes('交易时间'))) {
                    headerIndex = i;
                    break;
                }
            }
            if (headerIndex === -1) {
                return this.createParseResult([], [
                    '未找到有效的表头'
                ]);
            }
            const headers = lines[headerIndex];
            const dataLines = lines.slice(headerIndex + 1);
            // 解析每一行数据
            const transactions = [];
            const errors = [];
            for(let i = 0; i < dataLines.length; i++){
                try {
                    const row = dataLines[i];
                    if (row.length < headers.length || row.every((cell)=>!cell.trim())) {
                        continue; // 跳过空行或不完整行
                    }
                    const transaction = this.parseCmbDebitRow(row, headers);
                    if (transaction) {
                        transactions.push(transaction);
                    }
                } catch (error) {
                    errors.push(`第${headerIndex + i + 2}行解析失败: ${error}`);
                }
            }
            // 标准化交易数据
            const standardTransactions = transactions.map((raw)=>this.standardizeTransaction(raw, cardType));
            return this.createParseResult(standardTransactions, errors);
        } catch (error) {
            return this.createParseResult([], [
                `文件解析失败: ${error}`
            ]);
        }
    }
    parseCmbDebitRow(row, headers) {
        // 创建字段映射
        const fieldMap = {};
        headers.forEach((header, index)=>{
            if (row[index]) {
                fieldMap[header.trim()] = row[index].trim();
            }
        });
        // 招商银行借记卡账单常见字段映射
        const dateField = this.findField(fieldMap, [
            '交易日期',
            '记账日期',
            '交易时间'
        ]);
        const typeField = this.findField(fieldMap, [
            '交易类型',
            '摘要',
            '业务类型'
        ]);
        const descField = this.findField(fieldMap, [
            '交易说明',
            '备注',
            '摘要说明'
        ]);
        const amountField = this.findField(fieldMap, [
            '交易金额',
            '金额',
            '发生额'
        ]);
        const balanceField = this.findField(fieldMap, [
            '账户余额',
            '余额',
            '可用余额'
        ]);
        const counterpartyField = this.findField(fieldMap, [
            '对方户名',
            '对方账户',
            '交易对方'
        ]);
        const channelField = this.findField(fieldMap, [
            '交易渠道',
            '渠道'
        ]);
        const referenceField = this.findField(fieldMap, [
            '交易流水号',
            '流水号',
            '凭证号'
        ]);
        if (!dateField || !amountField) {
            return null;
        }
        // 解析时间
        const date = this.parseDate(dateField);
        if (!date) {
            throw new Error(`无效的时间格式: ${dateField}`);
        }
        // 解析金额
        let amount = this.parseAmount(amountField);
        // 借记卡：正数表示收入（存款），负数表示支出（取款/消费）
        // 招商银行通常用+/-符号或者单独的借贷标识
        if (amountField.includes('-') || amountField.includes('支出')) {
            amount = -Math.abs(amount);
        } else if (amountField.includes('+') || amountField.includes('收入')) {
            amount = Math.abs(amount);
        } else {
            // 根据交易类型判断
            if (typeField) {
                if (typeField.includes('支出') || typeField.includes('消费') || typeField.includes('取现') || typeField.includes('转出') || typeField.includes('扣费')) {
                    amount = -Math.abs(amount);
                } else if (typeField.includes('存入') || typeField.includes('转入') || typeField.includes('利息') || typeField.includes('退款')) {
                    amount = Math.abs(amount);
                }
            }
        }
        // 构建描述
        let description = typeField || '银行交易';
        if (descField) {
            description += ` - ${descField}`;
        }
        if (counterpartyField) {
            description += ` - ${counterpartyField}`;
        }
        // 推断分类
        let category = '其他';
        if (typeField) {
            if (typeField.includes('转账')) category = '转账';
            else if (typeField.includes('取现') || typeField.includes('ATM')) category = '取现';
            else if (typeField.includes('存款') || typeField.includes('存入')) category = '存款';
            else if (typeField.includes('利息')) category = '利息收入';
            else if (typeField.includes('工资') || typeField.includes('代发')) category = '工资收入';
            else if (typeField.includes('消费') || typeField.includes('刷卡')) category = '日常消费';
            else if (typeField.includes('还款')) category = '还款';
            else if (typeField.includes('手续费') || typeField.includes('服务费')) category = '银行费用';
        }
        // 解析余额
        let balance;
        if (balanceField) {
            balance = this.parseAmount(balanceField);
        }
        return {
            externalId: referenceField,
            date: date.toISOString(),
            amount,
            description: description.trim(),
            category,
            location: counterpartyField,
            balance,
            notes: `交易渠道: ${channelField || '未知'}`,
            rawData: fieldMap
        };
    }
    findField(fieldMap, possibleNames) {
        for (const name of possibleNames){
            for (const key of Object.keys(fieldMap)){
                if (key.includes(name)) {
                    return fieldMap[key];
                }
            }
        }
        return undefined;
    }
}
}}),
"[project]/src/lib/parsers/cmb-credit.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// 招商银行信用卡账单解析器
__turbopack_context__.s({
    "CmbCreditParser": (()=>CmbCreditParser)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$base$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/parsers/base.ts [app-route] (ecmascript)");
;
;
class CmbCreditParser extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$base$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BaseBillParser"] {
    name = '招商银行信用卡账单解析器';
    supportedCardTypes = [
        __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["CardType"].CREDIT_CARD
    ];
    supportedFileTypes = [
        'csv',
        'xls',
        'xlsx'
    ];
    async parse(fileContent, cardType) {
        try {
            const content = fileContent.toString('utf-8');
            const lines = this.parseCSV(content);
            // 查找表头行
            let headerIndex = -1;
            for(let i = 0; i < lines.length; i++){
                if (lines[i].some((cell)=>cell.includes('交易日期') || cell.includes('记账日期') || cell.includes('交易时间'))) {
                    headerIndex = i;
                    break;
                }
            }
            if (headerIndex === -1) {
                return this.createParseResult([], [
                    '未找到有效的表头'
                ]);
            }
            const headers = lines[headerIndex];
            const dataLines = lines.slice(headerIndex + 1);
            // 解析每一行数据
            const transactions = [];
            const errors = [];
            for(let i = 0; i < dataLines.length; i++){
                try {
                    const row = dataLines[i];
                    if (row.length < headers.length || row.every((cell)=>!cell.trim())) {
                        continue; // 跳过空行或不完整行
                    }
                    const transaction = this.parseCmbCreditRow(row, headers);
                    if (transaction) {
                        transactions.push(transaction);
                    }
                } catch (error) {
                    errors.push(`第${headerIndex + i + 2}行解析失败: ${error}`);
                }
            }
            // 标准化交易数据
            const standardTransactions = transactions.map((raw)=>this.standardizeTransaction(raw, cardType));
            return this.createParseResult(standardTransactions, errors);
        } catch (error) {
            return this.createParseResult([], [
                `文件解析失败: ${error}`
            ]);
        }
    }
    parseCmbCreditRow(row, headers) {
        // 创建字段映射
        const fieldMap = {};
        headers.forEach((header, index)=>{
            if (row[index]) {
                fieldMap[header.trim()] = row[index].trim();
            }
        });
        // 招商银行信用卡账单常见字段映射
        const dateField = this.findField(fieldMap, [
            '交易日期',
            '记账日期',
            '交易时间'
        ]);
        const typeField = this.findField(fieldMap, [
            '交易类型',
            '摘要',
            '业务类型'
        ]);
        const descField = this.findField(fieldMap, [
            '交易说明',
            '备注',
            '摘要说明',
            '商户名称'
        ]);
        const amountField = this.findField(fieldMap, [
            '交易金额',
            '金额',
            '人民币金额'
        ]);
        const currencyField = this.findField(fieldMap, [
            '币种',
            '交易币种'
        ]);
        const merchantField = this.findField(fieldMap, [
            '商户名称',
            '商户'
        ]);
        const referenceField = this.findField(fieldMap, [
            '交易流水号',
            '流水号',
            '凭证号'
        ]);
        const statusField = this.findField(fieldMap, [
            '交易状态',
            '状态'
        ]);
        if (!dateField || !amountField) {
            return null;
        }
        // 解析时间
        const date = this.parseDate(dateField);
        if (!date) {
            throw new Error(`无效的时间格式: ${dateField}`);
        }
        // 解析金额
        let amount = this.parseAmount(amountField);
        // 信用卡：正数表示支出（消费），负数表示收入（还款/退款）
        // 招商银行信用卡通常用+/-符号或者单独的借贷标识
        if (amountField.includes('-') || amountField.includes('还款') || amountField.includes('退款')) {
            amount = -Math.abs(amount) // 负数表示还款/退款（对信用卡来说是收入）
            ;
        } else if (amountField.includes('+') || typeField?.includes('消费')) {
            amount = Math.abs(amount) // 正数表示消费（对信用卡来说是支出）
            ;
        } else {
            // 根据交易类型判断
            if (typeField) {
                if (typeField.includes('还款') || typeField.includes('退款') || typeField.includes('溢缴款') || typeField.includes('调整')) {
                    amount = -Math.abs(amount) // 收入
                    ;
                } else {
                    amount = Math.abs(amount) // 支出
                    ;
                }
            }
        }
        // 构建描述
        let description = typeField || '信用卡交易';
        if (merchantField) {
            description += ` - ${merchantField}`;
        }
        if (descField && descField !== merchantField) {
            description += ` - ${descField}`;
        }
        // 推断分类
        let category = '其他';
        if (typeField) {
            if (typeField.includes('还款')) category = '信用卡还款';
            else if (typeField.includes('取现')) category = '信用卡取现';
            else if (typeField.includes('消费') || typeField.includes('刷卡')) category = '日常消费';
            else if (typeField.includes('分期')) category = '分期付款';
            else if (typeField.includes('年费')) category = '银行费用';
            else if (typeField.includes('利息')) category = '利息费用';
            else if (typeField.includes('退款')) category = '退款';
            else if (typeField.includes('转账')) category = '转账';
        }
        // 根据商户名称进一步细化分类
        if (merchantField && category === '日常消费') {
            if (merchantField.includes('超市') || merchantField.includes('便利店')) {
                category = '超市购物';
            } else if (merchantField.includes('餐厅') || merchantField.includes('饭店')) {
                category = '餐饮美食';
            } else if (merchantField.includes('加油站')) {
                category = '交通出行';
            } else if (merchantField.includes('医院') || merchantField.includes('药店')) {
                category = '医疗健康';
            }
        }
        return {
            externalId: referenceField,
            date: date.toISOString(),
            amount,
            description: description.trim(),
            category,
            location: merchantField,
            notes: `币种: ${currencyField || 'CNY'}, 状态: ${statusField || '未知'}`,
            rawData: fieldMap
        };
    }
    findField(fieldMap, possibleNames) {
        for (const name of possibleNames){
            for (const key of Object.keys(fieldMap)){
                if (key.includes(name)) {
                    return fieldMap[key];
                }
            }
        }
        return undefined;
    }
}
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/pdf-parse [external] (pdf-parse, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("pdf-parse", () => require("pdf-parse"));

module.exports = mod;
}}),
"[project]/src/lib/parsers/cmb-debit-pdf.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// 招商银行借记卡PDF账单解析器
__turbopack_context__.s({
    "CmbDebitPdfParser": (()=>CmbDebitPdfParser)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$base$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/parsers/base.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
;
;
class CmbDebitPdfParser extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$base$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BaseBillParser"] {
    name = '招商银行借记卡PDF账单解析器';
    supportedCardTypes = [
        __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["CardType"].DEBIT_CARD
    ];
    supportedFileTypes = [
        'pdf'
    ];
    // 定义分页相关的常量
    PAGINATION_MARKERS = [
        '/',
        '第',
        '页',
        '共',
        '温馨提示',
        '一网通',
        'www.cmbchina.com'
    ];
    HEADER_MARKERS = [
        '记账日期',
        '货币',
        '交易金额',
        '联机余额',
        '交易摘要',
        '对手信息'
    ];
    ENGLISH_HEADERS = [
        'Date',
        'Currency',
        'Transaction',
        'Amount',
        'Balance',
        'Counter Party'
    ];
    async parse(fileContent, cardType) {
        try {
            const buffer = Buffer.isBuffer(fileContent) ? fileContent : Buffer.from(fileContent);
            // 检查是否是真实的PDF文件
            if (buffer.length < 4 || buffer.toString('ascii', 0, 4) !== '%PDF') {
                // 如果不是PDF文件，返回测试数据用于演示
                return this.createTestResult(cardType);
            }
            // 使用require导入pdf-parse（已在next.config.ts中配置为外部包）
            // eslint-disable-next-line @typescript-eslint/no-require-imports
            const pdfParse = __turbopack_context__.r("[externals]/pdf-parse [external] (pdf-parse, cjs)");
            // 解析PDF到JSON结构
            const pdfResult = await this.extractJsonFromPdf(buffer, pdfParse);
            // 从JSON结构中解析交易记录
            const transactions = await this.parseTransactions(pdfResult);
            // 转换为标准交易格式
            const rawTransactions = transactions.map((transaction)=>this.convertToRawTransaction(transaction));
            // 标准化交易数据
            const standardTransactions = rawTransactions.map((raw)=>this.standardizeTransaction(raw, cardType));
            return this.createParseResult(standardTransactions, []);
        } catch (error) {
            return this.createParseResult([], [
                `PDF解析失败: ${error instanceof Error ? error.message : '未知错误'}`
            ]);
        }
    }
    createTestResult(cardType) {
        // 创建测试交易数据
        const testTransactions = [
            {
                date: '2024-01-01',
                currency: 'CNY',
                amount: '-50.00',
                balance: '1000.00',
                summary: '餐饮消费',
                counterparty: '麦当劳',
                sequence: 1,
                hash: 'test001'
            },
            {
                date: '2024-01-02',
                currency: 'CNY',
                amount: '-30.50',
                balance: '969.50',
                summary: '交通费用',
                counterparty: '地铁',
                sequence: 2,
                hash: 'test002'
            },
            {
                date: '2024-01-03',
                currency: 'CNY',
                amount: '+2000.00',
                balance: '2000.00',
                summary: '工资收入',
                counterparty: '公司转账',
                sequence: 3,
                hash: 'test003'
            },
            {
                date: '2024-01-04',
                currency: 'CNY',
                amount: '-120.00',
                balance: '1880.00',
                summary: '购物消费',
                counterparty: '淘宝网',
                sequence: 4,
                hash: 'test004'
            },
            {
                date: '2024-01-05',
                currency: 'CNY',
                amount: '-25.80',
                balance: '1854.20',
                summary: '餐饮消费',
                counterparty: '星巴克',
                sequence: 5,
                hash: 'test005'
            }
        ];
        // 转换为标准交易格式
        const rawTransactions = testTransactions.map((transaction)=>this.convertToRawTransaction(transaction));
        // 标准化交易数据
        const standardTransactions = rawTransactions.map((raw)=>this.standardizeTransaction(raw, cardType));
        return this.createParseResult(standardTransactions, []);
    }
    async extractJsonFromPdf(dataBuffer, pdfParse) {
        let currentPage = 1;
        const pages = [];
        let currentPageContent = {
            pageNumber: currentPage,
            textContent: {
                items: []
            }
        };
        const options = {
            pagerender: function(pageData) {
                return pageData.getTextContent().then(function(textContent) {
                    if (textContent.items.length > 0) {
                        currentPageContent = {
                            pageNumber: currentPage++,
                            textContent: {
                                items: textContent.items.map((item)=>{
                                    const pdfItem = item;
                                    return {
                                        str: pdfItem.str || '',
                                        dir: pdfItem.dir || 'ltr',
                                        width: pdfItem.width || 0,
                                        height: pdfItem.height || 0,
                                        transform: pdfItem.transform || [
                                            1,
                                            0,
                                            0,
                                            1,
                                            0,
                                            0
                                        ],
                                        fontName: pdfItem.fontName || ''
                                    };
                                })
                            }
                        };
                        pages.push(currentPageContent);
                    }
                    return textContent.items;
                });
            }
        };
        const pdfParseFunction = pdfParse;
        const data = await pdfParseFunction(dataBuffer, options);
        return {
            numpages: data.numpages,
            info: data.info,
            metadata: data.metadata,
            version: data.version,
            pages: pages
        };
    }
    async parseTransactions(pdfResult) {
        const transactions = [];
        let currentTransaction = null;
        let sequence = 1;
        for (const page of pdfResult.pages){
            if (!page.textContent) continue;
            const items = page.textContent.items || [];
            let supassPagination = false;
            for (const item of items){
                const text = item.str?.trim() || '';
                // 检查是否是日期格式
                if (text.length === 10 && text.split('-').length === 3) {
                    supassPagination = true;
                    try {
                        if (this.isValidDate(text)) {
                            // 处理前一个交易记录
                            if (currentTransaction && this.isValidRow(currentTransaction)) {
                                currentTransaction.sequence = sequence++;
                                currentTransaction.hash = this.computeTransactionHash(currentTransaction);
                                transactions.push(currentTransaction);
                            }
                            // 创建新的交易记录
                            currentTransaction = {
                                date: text,
                                currency: '',
                                amount: '',
                                balance: '',
                                summary: '',
                                counterparty: '',
                                sequence: 0,
                                hash: ''
                            };
                        }
                    } catch  {
                        continue;
                    }
                } else if (!supassPagination) {
                    continue;
                } else if (currentTransaction) {
                    if (text === 'CNY' && !currentTransaction.currency) {
                        currentTransaction.currency = text;
                    } else if (this.hasNumbers(text) && !currentTransaction.summary) {
                        if (!currentTransaction.amount) {
                            currentTransaction.amount = text;
                        } else if (!currentTransaction.balance) {
                            currentTransaction.balance = text;
                        }
                    } else if (!currentTransaction.summary) {
                        currentTransaction.summary = text;
                    } else if (currentTransaction.summary) {
                        currentTransaction.counterparty = currentTransaction.counterparty ? currentTransaction.counterparty + '-' + text : text;
                    }
                }
            }
        }
        // 处理最后一个交易记录
        if (currentTransaction && this.isValidRow(currentTransaction)) {
            currentTransaction.sequence = sequence;
            currentTransaction.hash = this.computeTransactionHash(currentTransaction);
            transactions.push(currentTransaction);
        }
        return transactions;
    }
    convertToRawTransaction(billRecord) {
        // 解析金额，判断收入还是支出
        const amount = this.parseAmount(billRecord.amount);
        // 生成外部ID
        const externalId = billRecord.hash || this.generateExternalId(billRecord);
        // 构建描述
        const description = `${billRecord.summary}${billRecord.counterparty ? ` - ${billRecord.counterparty}` : ''}`;
        // 推断分类
        const category = this.inferCategory(billRecord.summary, billRecord.counterparty);
        return {
            externalId,
            date: billRecord.date,
            amount,
            description: description.trim(),
            category,
            location: billRecord.counterparty,
            notes: `货币: ${billRecord.currency} | 余额: ${billRecord.balance}`,
            rawData: billRecord
        };
    }
    computeTransactionHash(transaction) {
        const content = [
            transaction.date,
            transaction.currency,
            transaction.amount,
            transaction.balance,
            transaction.summary,
            transaction.counterparty
        ].join('|');
        return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createHash('sha256').update(content).digest('hex').substring(0, 16) // 取前16位作为短哈希
        ;
    }
    generateExternalId(billRecord) {
        return this.computeTransactionHash(billRecord);
    }
    hasNumbers(text) {
        return /\d/.test(text);
    }
    isValidDate(dateStr) {
        const date = new Date(dateStr);
        return date instanceof Date && !isNaN(date.getTime()) && dateStr.match(/^\d{4}-\d{2}-\d{2}$/) !== null;
    }
    isValidRow(row) {
        return Boolean(row.date && row.currency && row.amount);
    }
    inferCategory(summary, counterparty) {
        const text = `${summary} ${counterparty}`.toLowerCase();
        if (text.includes('餐') || text.includes('食') || text.includes('饭') || text.includes('咖啡')) {
            return '餐饮';
        } else if (text.includes('交通') || text.includes('地铁') || text.includes('公交') || text.includes('打车') || text.includes('滴滴')) {
            return '交通';
        } else if (text.includes('购物') || text.includes('商城') || text.includes('超市') || text.includes('淘宝') || text.includes('京东')) {
            return '购物';
        } else if (text.includes('娱乐') || text.includes('电影') || text.includes('游戏')) {
            return '娱乐';
        } else if (text.includes('医') || text.includes('药') || text.includes('医院')) {
            return '医疗';
        } else if (text.includes('教育') || text.includes('学') || text.includes('培训')) {
            return '教育';
        } else if (text.includes('房') || text.includes('租') || text.includes('物业')) {
            return '住房';
        } else if (text.includes('转账') || text.includes('汇款')) {
            return '转账';
        } else if (text.includes('工资') || text.includes('薪') || text.includes('奖金')) {
            return '工资';
        } else if (text.includes('利息') || text.includes('理财') || text.includes('投资')) {
            return '投资理财';
        } else {
            return '其他';
        }
    }
}
}}),
"[project]/src/lib/parsers/factory.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// 账单解析器工厂
__turbopack_context__.s({
    "ParserFactory": (()=>ParserFactory)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$wechat$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/parsers/wechat.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$alipay$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/parsers/alipay.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$cmb$2d$debit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/parsers/cmb-debit.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$cmb$2d$credit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/parsers/cmb-credit.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$cmb$2d$debit$2d$pdf$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/parsers/cmb-debit-pdf.ts [app-route] (ecmascript)");
;
;
;
;
;
// 注册所有解析器
const PARSERS = [
    new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$wechat$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["WechatParser"](),
    new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$alipay$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlipayParser"](),
    new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$cmb$2d$debit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CmbDebitParser"](),
    new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$cmb$2d$credit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CmbCreditParser"](),
    new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$cmb$2d$debit$2d$pdf$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CmbDebitPdfParser"]()
];
class ParserFactory {
    /**
   * 获取支持指定卡片类型和文件的解析器
   */ static getParser(fileName, cardType) {
        for (const parser of PARSERS){
            if (parser.canParse(fileName, cardType)) {
                return parser;
            }
        }
        return null;
    }
    /**
   * 获取支持指定卡片类型的所有解析器
   */ static getParsersForCardType(cardType) {
        return PARSERS.filter((parser)=>parser.supportedCardTypes.includes(cardType));
    }
    /**
   * 获取所有注册的解析器
   */ static getAllParsers() {
        return [
            ...PARSERS
        ];
    }
    /**
   * 获取支持的文件类型
   */ static getSupportedFileTypes() {
        const types = new Set();
        PARSERS.forEach((parser)=>{
            parser.supportedFileTypes.forEach((type)=>types.add(type));
        });
        return Array.from(types);
    }
}
}}),
"[project]/src/lib/services/bill-import-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BillImportService": (()=>BillImportService),
    "billImportService": (()=>billImportService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$factory$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/parsers/factory.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
;
;
class BillImportService {
    async importBill(options) {
        const { cardId, userId, file, fileName, fileSize } = options;
        // 创建账单导入记录
        const billImport = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].billImport.create({
            data: {
                fileName,
                fileType: this.getFileType(fileName),
                fileSize,
                status: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["BillImportStatus"].PROCESSING,
                cardId
            }
        });
        try {
            // 获取卡片信息
            const card = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].card.findUnique({
                where: {
                    id: cardId
                }
            });
            if (!card) {
                await this.updateBillImportStatus(billImport.id, __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["BillImportStatus"].FAILED, {
                    errorLog: '卡片不存在'
                });
                return {
                    billImportId: billImport.id,
                    success: false,
                    totalCount: 0,
                    successCount: 0,
                    errorCount: 1,
                    duplicateCount: 0,
                    errors: [
                        '卡片不存在'
                    ]
                };
            }
            // 获取合适的解析器
            const parser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$factory$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ParserFactory"].getParser(fileName, card.type);
            if (!parser) {
                await this.updateBillImportStatus(billImport.id, __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["BillImportStatus"].FAILED, {
                    errorLog: '未找到合适的解析器'
                });
                return {
                    billImportId: billImport.id,
                    success: false,
                    totalCount: 0,
                    successCount: 0,
                    errorCount: 1,
                    duplicateCount: 0,
                    errors: [
                        '未找到合适的解析器'
                    ]
                };
            }
            // 解析文件
            const parseResult = await parser.parse(file, card.type);
            if (!parseResult.success && parseResult.transactions.length === 0) {
                await this.updateBillImportStatus(billImport.id, __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["BillImportStatus"].FAILED, {
                    errorLog: parseResult.errors.join('\n')
                });
                return {
                    billImportId: billImport.id,
                    success: false,
                    totalCount: parseResult.summary.totalCount,
                    successCount: 0,
                    errorCount: parseResult.summary.errorCount,
                    duplicateCount: 0,
                    errors: parseResult.errors
                };
            }
            // 导入交易记录
            const importResult = await this.importTransactions(parseResult.transactions, billImport.id, cardId, userId);
            // 更新账单导入状态
            await this.updateBillImportStatus(billImport.id, __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["BillImportStatus"].COMPLETED, {
                totalCount: parseResult.summary.totalCount,
                successCount: importResult.successCount,
                errorCount: parseResult.summary.errorCount + importResult.errorCount,
                errorLog: [
                    ...parseResult.errors,
                    ...importResult.errors
                ].join('\n')
            });
            return {
                billImportId: billImport.id,
                success: true,
                totalCount: parseResult.summary.totalCount,
                successCount: importResult.successCount,
                errorCount: parseResult.summary.errorCount + importResult.errorCount,
                duplicateCount: importResult.duplicateCount,
                errors: [
                    ...parseResult.errors,
                    ...importResult.errors
                ]
            };
        } catch (error) {
            await this.updateBillImportStatus(billImport.id, __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["BillImportStatus"].FAILED, {
                errorLog: error instanceof Error ? error.message : '未知错误'
            });
            return {
                billImportId: billImport.id,
                success: false,
                totalCount: 0,
                successCount: 0,
                errorCount: 1,
                duplicateCount: 0,
                errors: [
                    error instanceof Error ? error.message : '未知错误'
                ]
            };
        }
    }
    async importTransactions(transactions, billImportId, cardId, userId) {
        let successCount = 0;
        let errorCount = 0;
        let duplicateCount = 0;
        const errors = [];
        for (const transaction of transactions){
            try {
                // 检查是否已存在相同的交易（基于外部ID和卡片ID）
                if (transaction.externalId) {
                    const existing = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].transaction.findFirst({
                        where: {
                            externalId: transaction.externalId,
                            cardId
                        }
                    });
                    if (existing) {
                        duplicateCount++;
                        continue;
                    }
                }
                // 创建交易记录
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].transaction.create({
                    data: {
                        externalId: transaction.externalId,
                        type: transaction.type,
                        status: __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["TransactionStatus"].CONFIRMED,
                        amount: transaction.amount,
                        description: transaction.description,
                        category: transaction.category,
                        subcategory: transaction.subcategory,
                        transactionDate: transaction.transactionDate,
                        location: transaction.location,
                        notes: transaction.notes,
                        userId,
                        cardId,
                        billImportId
                    }
                });
                successCount++;
            } catch (error) {
                errorCount++;
                errors.push(`交易导入失败: ${error instanceof Error ? error.message : '未知错误'}`);
            }
        }
        return {
            successCount,
            errorCount,
            duplicateCount,
            errors
        };
    }
    async updateBillImportStatus(billImportId, status, data) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].billImport.update({
            where: {
                id: billImportId
            },
            data: {
                status,
                ...data,
                updatedAt: new Date()
            }
        });
    }
    getFileType(fileName) {
        const extension = fileName.toLowerCase().split('.').pop();
        return extension || 'unknown';
    }
    // 获取支持的文件类型
    getSupportedFileTypes() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$factory$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ParserFactory"].getSupportedFileTypes();
    }
    // 获取解析器信息
    getParserInfo() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$parsers$2f$factory$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ParserFactory"].getAllParsers().map((parser)=>({
                name: parser.name,
                supportedCardTypes: parser.supportedCardTypes,
                supportedFileTypes: parser.supportedFileTypes
            }));
    }
}
const billImportService = new BillImportService();
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateToken": (()=>generateToken),
    "getUserFromRequest": (()=>getUserFromRequest),
    "hashPassword": (()=>hashPassword),
    "isValidEmail": (()=>isValidEmail),
    "isValidPassword": (()=>isValidPassword),
    "requireAuth": (()=>requireAuth),
    "requireFamilyAdmin": (()=>requireFamilyAdmin),
    "requireFamilyMember": (()=>requireFamilyMember),
    "verifyPassword": (()=>verifyPassword),
    "verifyToken": (()=>verifyToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
;
;
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';
const BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12');
async function hashPassword(password) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(password, BCRYPT_ROUNDS);
}
async function verifyPassword(password, hashedPassword) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, hashedPassword);
}
function generateToken(payload) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].sign(payload, JWT_SECRET, {
        expiresIn: JWT_EXPIRES_IN
    });
}
function verifyToken(token) {
    try {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, JWT_SECRET);
    } catch (error) {
        return null;
    }
}
async function getUserFromRequest(request) {
    try {
        const authHeader = request.headers.get('authorization');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return null;
        }
        const token = authHeader.substring(7);
        const payload = verifyToken(token);
        if (!payload) {
            return null;
        }
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                id: payload.userId
            },
            include: {
                family: {
                    select: {
                        id: true,
                        name: true
                    }
                }
            }
        });
        if (!user || !user.isActive) {
            return null;
        }
        return {
            id: user.id,
            email: user.email,
            name: user.name,
            avatar: user.avatar || undefined,
            familyId: user.familyId || undefined,
            familyRole: user.familyRole,
            family: user.family ? {
                id: user.family.id,
                name: user.family.name,
                role: user.familyRole
            } : undefined
        };
    } catch (error) {
        console.error('Error getting user from request:', error);
        return null;
    }
}
function requireAuth(handler) {
    return async (request)=>{
        const user = await getUserFromRequest(request);
        if (!user) {
            return new Response(JSON.stringify({
                success: false,
                error: '未授权访问'
            }), {
                status: 401,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        return handler(request, user);
    };
}
function requireFamilyMember(handler) {
    return async (request)=>{
        const user = await getUserFromRequest(request);
        if (!user) {
            return new Response(JSON.stringify({
                success: false,
                error: '未授权访问'
            }), {
                status: 401,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        if (!user.familyId) {
            return new Response(JSON.stringify({
                success: false,
                error: '您还没有加入任何家庭'
            }), {
                status: 403,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        return handler(request, user);
    };
}
function requireFamilyAdmin(handler) {
    return async (request)=>{
        const user = await getUserFromRequest(request);
        if (!user) {
            return new Response(JSON.stringify({
                success: false,
                error: '未授权访问'
            }), {
                status: 401,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        if (!user.familyId) {
            return new Response(JSON.stringify({
                success: false,
                error: '您还没有加入任何家庭'
            }), {
                status: 403,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        if (user.familyRole !== 'OWNER' && user.familyRole !== 'ADMIN') {
            return new Response(JSON.stringify({
                success: false,
                error: '需要管理员权限'
            }), {
                status: 403,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        return handler(request, user);
    };
}
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function isValidPassword(password) {
    if (password.length < 8) {
        return {
            valid: false,
            message: '密码长度至少8位'
        };
    }
    if (!/(?=.*[a-z])/.test(password)) {
        return {
            valid: false,
            message: '密码必须包含小写字母'
        };
    }
    if (!/(?=.*[A-Z])/.test(password)) {
        return {
            valid: false,
            message: '密码必须包含大写字母'
        };
    }
    if (!/(?=.*\d)/.test(password)) {
        return {
            valid: false,
            message: '密码必须包含数字'
        };
    }
    return {
        valid: true
    };
}
}}),
"[project]/src/app/api/bills/import/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$response$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-response.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$bill$2d$import$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/bill-import-service.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        // 验证用户认证
        const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getUserFromRequest"])(request);
        if (!user) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$response$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApiError"]('未授权访问', 401);
        }
        const formData = await request.formData();
        const file = formData.get('file');
        const cardId = formData.get('cardId');
        if (!file) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$response$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApiError"]('请选择要上传的文件', 400);
        }
        if (!cardId) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$response$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApiError"]('请指定卡片ID', 400);
        }
        // 验证文件大小（10MB限制）
        const maxSize = 10 * 1024 * 1024 // 10MB
        ;
        if (file.size > maxSize) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$response$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApiError"]('文件大小不能超过10MB', 400);
        }
        // 验证文件类型
        const supportedTypes = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$bill$2d$import$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["billImportService"].getSupportedFileTypes();
        const fileExtension = file.name.toLowerCase().split('.').pop();
        if (!fileExtension || !supportedTypes.includes(fileExtension)) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$response$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApiError"](`不支持的文件类型。支持的类型: ${supportedTypes.join(', ')}`, 400);
        }
        // 读取文件内容
        const buffer = Buffer.from(await file.arrayBuffer());
        // 导入账单
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$bill$2d$import$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["billImportService"].importBill({
            cardId,
            userId: user.id,
            file: buffer,
            fileName: file.name,
            fileSize: file.size
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$response$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createSuccessResponse"])(result, '账单导入完成');
    } catch (error) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$response$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleApiError"])(error);
    }
}
async function GET() {
    try {
        const info = {
            supportedFileTypes: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$bill$2d$import$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["billImportService"].getSupportedFileTypes(),
            parsers: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$bill$2d$import$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["billImportService"].getParserInfo(),
            maxFileSize: '10MB'
        };
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$response$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createSuccessResponse"])(info);
    } catch (error) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$response$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleApiError"])(error);
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__77e2af66._.js.map