{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/api-response.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport interface ApiResponse<T = any> {\n  success: boolean\n  data?: T\n  error?: string\n  message?: string\n  pagination?: {\n    page: number\n    limit: number\n    total: number\n    totalPages: number\n  }\n}\n\nexport class ApiError extends Error {\n  constructor(\n    message: string,\n    public statusCode: number = 500,\n    public code?: string\n  ) {\n    super(message)\n    this.name = 'ApiError'\n  }\n}\n\nexport function createSuccessResponse<T>(\n  data: T,\n  message?: string,\n  pagination?: ApiResponse<T>['pagination']\n): NextResponse<ApiResponse<T>> {\n  return NextResponse.json({\n    success: true,\n    data,\n    message,\n    pagination,\n  })\n}\n\nexport function createErrorResponse(\n  error: string | Error,\n  statusCode: number = 500\n): NextResponse<ApiResponse> {\n  const message = error instanceof Error ? error.message : error\n  \n  return NextResponse.json(\n    {\n      success: false,\n      error: message,\n    },\n    { status: statusCode }\n  )\n}\n\nexport function handleApiError(error: unknown): NextResponse<ApiResponse> {\n  console.error('API Error:', error)\n  \n  if (error instanceof ApiError) {\n    return createErrorResponse(error.message, error.statusCode)\n  }\n  \n  if (error instanceof Error) {\n    return createErrorResponse(error.message, 500)\n  }\n  \n  return createErrorResponse('Internal server error', 500)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAeO,MAAM,iBAAiB;;;IAC5B,YACE,OAAe,EACf,AAAO,aAAqB,GAAG,EAC/B,AAAO,IAAa,CACpB;QACA,KAAK,CAAC,eAHC,aAAA,iBACA,OAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,SAAS,sBACd,IAAO,EACP,OAAgB,EAChB,UAAyC;IAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT;QACA;QACA;IACF;AACF;AAEO,SAAS,oBACd,KAAqB,EACrB,aAAqB,GAAG;IAExB,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAEzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT,OAAO;IACT,GACA;QAAE,QAAQ;IAAW;AAEzB;AAEO,SAAS,eAAe,KAAc;IAC3C,QAAQ,KAAK,CAAC,cAAc;IAE5B,IAAI,iBAAiB,UAAU;QAC7B,OAAO,oBAAoB,MAAM,OAAO,EAAE,MAAM,UAAU;IAC5D;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,oBAAoB,MAAM,OAAO,EAAE;IAC5C;IAEA,OAAO,oBAAoB,yBAAyB;AACtD", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/parsers/types.ts"], "sourcesContent": ["// 账单解析器类型定义\n\nimport { CardType, TransactionType } from '@prisma/client'\n\n// 原始交易数据\nexport interface RawTransaction {\n  externalId?: string          // 外部交易ID\n  date: string                 // 交易日期\n  amount: number               // 金额（原始值，可能为正负）\n  description: string          // 交易描述\n  category?: string            // 分类\n  subcategory?: string         // 子分类\n  location?: string            // 交易地点\n  counterparty?: string        // 交易对方\n  balance?: number             // 余额\n  notes?: string               // 备注\n  rawData?: Record<string, any> // 原始数据\n}\n\n// 标准化交易数据\nexport interface StandardTransaction {\n  externalId?: string\n  type: TransactionType        // 标准化后的交易类型\n  amount: number               // 标准化后的金额（绝对值）\n  description: string\n  category?: string\n  subcategory?: string\n  transactionDate: Date\n  location?: string\n  notes?: string\n  balance?: number\n}\n\n// 解析结果\nexport interface ParseResult {\n  success: boolean\n  transactions: StandardTransaction[]\n  errors: string[]\n  summary: {\n    totalCount: number\n    successCount: number\n    errorCount: number\n    duplicateCount: number\n  }\n}\n\n// 解析器接口\nexport interface BillParser {\n  // 解析器名称\n  name: string\n  \n  // 支持的卡片类型\n  supportedCardTypes: CardType[]\n  \n  // 支持的文件类型\n  supportedFileTypes: string[]\n  \n  // 检查是否支持该文件\n  canParse(fileName: string, cardType: CardType): boolean\n  \n  // 解析账单文件\n  parse(fileContent: string | Buffer, cardType: CardType): Promise<ParseResult>\n}\n\n// 卡片类型配置\nexport interface CardTypeConfig {\n  // 金额方向映射\n  // true: 正数表示收入，负数表示支出\n  // false: 正数表示支出，负数表示收入（如信用卡）\n  positiveIsIncome: boolean\n  \n  // 默认分类映射\n  defaultCategories: Record<string, string>\n}\n\n// 卡片类型配置映射\nexport const CARD_TYPE_CONFIGS: Record<CardType, CardTypeConfig> = {\n  [CardType.DEBIT_CARD]: {\n    positiveIsIncome: true,  // 借记卡：正数=收入，负数=支出\n    defaultCategories: {\n      '转账': '转账',\n      '取现': '取现',\n      '存款': '存款',\n      '利息': '利息收入',\n    }\n  },\n  [CardType.CREDIT_CARD]: {\n    positiveIsIncome: false, // 信用卡：正数=支出，负数=收入（还款）\n    defaultCategories: {\n      '还款': '信用卡还款',\n      '消费': '日常消费',\n      '取现': '信用卡取现',\n    }\n  },\n  [CardType.WECHAT]: {\n    positiveIsIncome: true,  // 微信：正数=收入，负数=支出\n    defaultCategories: {\n      '转账': '转账',\n      '红包': '红包',\n      '消费': '日常消费',\n      '充值': '充值',\n    }\n  },\n  [CardType.ALIPAY]: {\n    positiveIsIncome: true,  // 支付宝：正数=收入，负数=支出\n    defaultCategories: {\n      '转账': '转账',\n      '红包': '红包',\n      '消费': '日常消费',\n      '充值': '充值',\n    }\n  },\n  [CardType.CASH]: {\n    positiveIsIncome: true,  // 现金：正数=收入，负数=支出\n    defaultCategories: {}\n  },\n  [CardType.OTHER]: {\n    positiveIsIncome: true,  // 其他：正数=收入，负数=支出\n    defaultCategories: {}\n  }\n}\n"], "names": [], "mappings": "AAAA,YAAY;;;;AAEZ;;AA0EO,MAAM,oBAAsD;IACjE,CAAC,6HAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,EAAE;QACrB,kBAAkB;QAClB,mBAAmB;YACjB,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;IACF;IACA,CAAC,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,EAAE;QACtB,kBAAkB;QAClB,mBAAmB;YACjB,MAAM;YACN,MAAM;YACN,MAAM;QACR;IACF;IACA,CAAC,6HAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,EAAE;QACjB,kBAAkB;QAClB,mBAAmB;YACjB,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;IACF;IACA,CAAC,6HAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,EAAE;QACjB,kBAAkB;QAClB,mBAAmB;YACjB,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;IACF;IACA,CAAC,6HAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,EAAE;QACf,kBAAkB;QAClB,mBAAmB,CAAC;IACtB;IACA,CAAC,6HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,EAAE;QAChB,kBAAkB;QAClB,mBAAmB,CAAC;IACtB;AACF", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/parsers/base.ts"], "sourcesContent": ["// 基础解析器类\n\nimport { CardType, TransactionType } from '@prisma/client'\nimport { \n  BillParser, \n  RawTransaction, \n  StandardTransaction, \n  ParseResult,\n  CARD_TYPE_CONFIGS \n} from './types'\n\nexport abstract class BaseBillParser implements BillParser {\n  abstract name: string\n  abstract supportedCardTypes: CardType[]\n  abstract supportedFileTypes: string[]\n\n  /**\n   * 检查是否支持该文件\n   */\n  canParse(fileName: string, cardType: CardType): boolean {\n    // 检查卡片类型\n    if (!this.supportedCardTypes.includes(cardType)) {\n      return false\n    }\n\n    // 检查文件扩展名\n    const ext = fileName.toLowerCase().split('.').pop()\n    return ext ? this.supportedFileTypes.includes(ext) : false\n  }\n\n  /**\n   * 解析账单文件\n   */\n  abstract parse(fileContent: string | Buffer, cardType: CardType): Promise<ParseResult>\n\n  /**\n   * 标准化交易数据\n   */\n  protected standardizeTransaction(\n    raw: RawTransaction, \n    cardType: CardType\n  ): StandardTransaction {\n    const config = CARD_TYPE_CONFIGS[cardType]\n    \n    // 确定交易类型和金额\n    let type: TransactionType\n    let amount: number\n\n    if (raw.amount === 0) {\n      type = TransactionType.TRANSFER\n      amount = 0\n    } else if (config.positiveIsIncome) {\n      // 借记卡、微信、支付宝等：正数=收入，负数=支出\n      if (raw.amount > 0) {\n        type = TransactionType.INCOME\n        amount = raw.amount\n      } else {\n        type = TransactionType.EXPENSE\n        amount = Math.abs(raw.amount)\n      }\n    } else {\n      // 信用卡：正数=支出，负数=收入\n      if (raw.amount > 0) {\n        type = TransactionType.EXPENSE\n        amount = raw.amount\n      } else {\n        type = TransactionType.INCOME\n        amount = Math.abs(raw.amount)\n      }\n    }\n\n    return {\n      externalId: raw.externalId,\n      type,\n      amount,\n      description: raw.description.trim(),\n      category: raw.category || this.inferCategory(raw.description, config),\n      subcategory: raw.subcategory,\n      transactionDate: new Date(raw.date),\n      location: raw.location,\n      notes: raw.notes,\n      balance: raw.balance,\n    }\n  }\n\n  /**\n   * 推断交易分类\n   */\n  protected inferCategory(description: string, config: any): string {\n    for (const [keyword, category] of Object.entries(config.defaultCategories)) {\n      if (description.includes(keyword)) {\n        return category as string\n      }\n    }\n    return '其他'\n  }\n\n  /**\n   * 解析CSV内容\n   */\n  protected parseCSV(content: string): string[][] {\n    const lines = content.split('\\n').filter(line => line.trim())\n    return lines.map(line => {\n      // 简单的CSV解析，处理逗号分隔和引号包围的字段\n      const fields: string[] = []\n      let current = ''\n      let inQuotes = false\n      \n      for (let i = 0; i < line.length; i++) {\n        const char = line[i]\n        \n        if (char === '\"') {\n          inQuotes = !inQuotes\n        } else if (char === ',' && !inQuotes) {\n          fields.push(current.trim())\n          current = ''\n        } else {\n          current += char\n        }\n      }\n      \n      fields.push(current.trim())\n      return fields.map(field => field.replace(/^\"|\"$/g, '')) // 移除引号\n    })\n  }\n\n  /**\n   * 验证日期格式\n   */\n  protected parseDate(dateStr: string): Date | null {\n    // 支持多种日期格式\n    const formats = [\n      /^\\d{4}-\\d{2}-\\d{2}$/,           // 2024-01-01\n      /^\\d{4}\\/\\d{2}\\/\\d{2}$/,         // 2024/01/01\n      /^\\d{4}\\.\\d{2}\\.\\d{2}$/,         // 2024.01.01\n      /^\\d{2}-\\d{2}-\\d{4}$/,           // 01-01-2024\n      /^\\d{2}\\/\\d{2}\\/\\d{4}$/,         // 01/01/2024\n    ]\n\n    for (const format of formats) {\n      if (format.test(dateStr)) {\n        const date = new Date(dateStr)\n        if (!isNaN(date.getTime())) {\n          return date\n        }\n      }\n    }\n\n    return null\n  }\n\n  /**\n   * 解析金额\n   */\n  protected parseAmount(amountStr: string): number {\n    // 移除货币符号和空格\n    const cleaned = amountStr.replace(/[￥$¥,\\s]/g, '')\n    const amount = parseFloat(cleaned)\n    return isNaN(amount) ? 0 : amount\n  }\n\n  /**\n   * 生成解析结果\n   */\n  protected createParseResult(\n    transactions: StandardTransaction[],\n    errors: string[]\n  ): ParseResult {\n    return {\n      success: errors.length === 0,\n      transactions,\n      errors,\n      summary: {\n        totalCount: transactions.length + errors.length,\n        successCount: transactions.length,\n        errorCount: errors.length,\n        duplicateCount: 0, // 将在服务层处理重复检测\n      }\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,SAAS;;;;AAET;AACA;;;AAQO,MAAe;IAKpB;;GAEC,GACD,SAAS,QAAgB,EAAE,QAAkB,EAAW;QACtD,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW;YAC/C,OAAO;QACT;QAEA,UAAU;QACV,MAAM,MAAM,SAAS,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG;QACjD,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,OAAO;IACvD;IAOA;;GAEC,GACD,AAAU,uBACR,GAAmB,EACnB,QAAkB,EACG;QACrB,MAAM,SAAS,gIAAA,CAAA,oBAAiB,CAAC,SAAS;QAE1C,YAAY;QACZ,IAAI;QACJ,IAAI;QAEJ,IAAI,IAAI,MAAM,KAAK,GAAG;YACpB,OAAO,6HAAA,CAAA,kBAAe,CAAC,QAAQ;YAC/B,SAAS;QACX,OAAO,IAAI,OAAO,gBAAgB,EAAE;YAClC,0BAA0B;YAC1B,IAAI,IAAI,MAAM,GAAG,GAAG;gBAClB,OAAO,6HAAA,CAAA,kBAAe,CAAC,MAAM;gBAC7B,SAAS,IAAI,MAAM;YACrB,OAAO;gBACL,OAAO,6HAAA,CAAA,kBAAe,CAAC,OAAO;gBAC9B,SAAS,KAAK,GAAG,CAAC,IAAI,MAAM;YAC9B;QACF,OAAO;YACL,kBAAkB;YAClB,IAAI,IAAI,MAAM,GAAG,GAAG;gBAClB,OAAO,6HAAA,CAAA,kBAAe,CAAC,OAAO;gBAC9B,SAAS,IAAI,MAAM;YACrB,OAAO;gBACL,OAAO,6HAAA,CAAA,kBAAe,CAAC,MAAM;gBAC7B,SAAS,KAAK,GAAG,CAAC,IAAI,MAAM;YAC9B;QACF;QAEA,OAAO;YACL,YAAY,IAAI,UAAU;YAC1B;YACA;YACA,aAAa,IAAI,WAAW,CAAC,IAAI;YACjC,UAAU,IAAI,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,WAAW,EAAE;YAC9D,aAAa,IAAI,WAAW;YAC5B,iBAAiB,IAAI,KAAK,IAAI,IAAI;YAClC,UAAU,IAAI,QAAQ;YACtB,OAAO,IAAI,KAAK;YAChB,SAAS,IAAI,OAAO;QACtB;IACF;IAEA;;GAEC,GACD,AAAU,cAAc,WAAmB,EAAE,MAAW,EAAU;QAChE,KAAK,MAAM,CAAC,SAAS,SAAS,IAAI,OAAO,OAAO,CAAC,OAAO,iBAAiB,EAAG;YAC1E,IAAI,YAAY,QAAQ,CAAC,UAAU;gBACjC,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA;;GAEC,GACD,AAAU,SAAS,OAAe,EAAc;QAC9C,MAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;QAC1D,OAAO,MAAM,GAAG,CAAC,CAAA;YACf,0BAA0B;YAC1B,MAAM,SAAmB,EAAE;YAC3B,IAAI,UAAU;YACd,IAAI,WAAW;YAEf,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,MAAM,OAAO,IAAI,CAAC,EAAE;gBAEpB,IAAI,SAAS,KAAK;oBAChB,WAAW,CAAC;gBACd,OAAO,IAAI,SAAS,OAAO,CAAC,UAAU;oBACpC,OAAO,IAAI,CAAC,QAAQ,IAAI;oBACxB,UAAU;gBACZ,OAAO;oBACL,WAAW;gBACb;YACF;YAEA,OAAO,IAAI,CAAC,QAAQ,IAAI;YACxB,OAAO,OAAO,GAAG,CAAC,CAAA,QAAS,MAAM,OAAO,CAAC,UAAU,KAAK,OAAO;;QACjE;IACF;IAEA;;GAEC,GACD,AAAU,UAAU,OAAe,EAAe;QAChD,WAAW;QACX,MAAM,UAAU;YACd;YACA;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,UAAU,QAAS;YAC5B,IAAI,OAAO,IAAI,CAAC,UAAU;gBACxB,MAAM,OAAO,IAAI,KAAK;gBACtB,IAAI,CAAC,MAAM,KAAK,OAAO,KAAK;oBAC1B,OAAO;gBACT;YACF;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAU,YAAY,SAAiB,EAAU;QAC/C,YAAY;QACZ,MAAM,UAAU,UAAU,OAAO,CAAC,aAAa;QAC/C,MAAM,SAAS,WAAW;QAC1B,OAAO,MAAM,UAAU,IAAI;IAC7B;IAEA;;GAEC,GACD,AAAU,kBACR,YAAmC,EACnC,MAAgB,EACH;QACb,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;YACA;YACA,SAAS;gBACP,YAAY,aAAa,MAAM,GAAG,OAAO,MAAM;gBAC/C,cAAc,aAAa,MAAM;gBACjC,YAAY,OAAO,MAAM;gBACzB,gBAAgB;YAClB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/parsers/wechat.ts"], "sourcesContent": ["// 微信账单解析器\n\nimport { CardType } from '@prisma/client'\nimport { BaseBillParser } from './base'\nimport { RawTransaction, ParseResult } from './types'\nimport { WeChatBill, WeChatTransaction } from '../types/bill'\n\nexport class WechatParser extends BaseBillParser {\n  name = '微信支付账单解析器'\n  supportedCardTypes = [CardType.WECHAT]\n  supportedFileTypes = ['csv']\n\n  async parse(fileContent: string | Buffer, cardType: CardType): Promise<ParseResult> {\n    try {\n      const content = fileContent.toString('utf-8')\n      const wechatBill = this.parseWeChatBill(content)\n\n      // 转换微信交易为标准交易格式\n      const transactions: RawTransaction[] = []\n      const errors: string[] = []\n\n      wechatBill.transactions.forEach((wechatTx, index) => {\n        try {\n          const transaction = this.convertWeChatTransaction(wechatTx)\n          if (transaction) {\n            transactions.push(transaction)\n          }\n        } catch (error) {\n          errors.push(`第${index + 1}行解析失败: ${error instanceof Error ? error.message : '未知错误'}`)\n        }\n      })\n\n      // 标准化交易数据\n      const standardTransactions = transactions.map(raw =>\n        this.standardizeTransaction(raw, cardType)\n      )\n\n      return this.createParseResult(standardTransactions, errors)\n    } catch (error) {\n      return this.createParseResult([], [`文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`])\n    }\n  }\n\n  private parseWeChatBill(csvContent: string): WeChatBill {\n    const lines = csvContent.split('\\n').map(line => line.trim())\n\n    // Parse metadata\n    const metadata = {\n      nickname: this.extractValue(lines, '微信昵称：'),\n      startDate: this.extractValue(lines, '起始时间：'),\n      endDate: this.extractValue(lines, '终止时间：'),\n      exportType: this.extractValue(lines, '导出类型：'),\n      exportTime: this.extractValue(lines, '导出时间：'),\n      summary: {\n        totalCount: this.extractNumber(lines, '共'),\n        incomeCount: this.extractNumber(lines, '收入：'),\n        incomeAmount: this.extractAmount(lines, '收入：'),\n        expenseCount: this.extractNumber(lines, '支出：'),\n        expenseAmount: this.extractAmount(lines, '支出：'),\n        neutralCount: this.extractNumber(lines, '中性交易：'),\n        neutralAmount: this.extractAmount(lines, '中性交易：'),\n      }\n    }\n\n    // Find transactions start index\n    const transactionStartIndex = lines.findIndex(line =>\n      line.startsWith('交易时间,交易类型,交易对方'))\n\n    if (transactionStartIndex === -1) {\n      throw new Error('未找到交易数据表头')\n    }\n\n    // Parse transactions\n    const transactions: WeChatTransaction[] = lines\n      .slice(transactionStartIndex + 1)\n      .filter(line => line && !line.startsWith('-') && !line.startsWith('注：') && !line.startsWith('共'))\n      .map(line => this.parseWeChatTransactionLine(line))\n      .filter(tx => tx !== null) as WeChatTransaction[]\n\n    return { metadata, transactions }\n  }\n\n  private parseWeChatTransactionLine(line: string): WeChatTransaction | null {\n    // 更精确的CSV解析，处理引号内的逗号\n    const fields: string[] = []\n    let field = ''\n    let inQuotes = false\n\n    for (let i = 0; i < line.length; i++) {\n      const char = line[i]\n\n      if (char === '\"') {\n        inQuotes = !inQuotes\n        continue\n      }\n\n      if (char === ',' && !inQuotes) {\n        fields.push(field.trim())\n        field = ''\n        continue\n      }\n\n      field += char\n    }\n    fields.push(field.trim()) // 添加最后一个字段\n\n    if (fields.length < 11) {\n      return null // 字段不足，跳过\n    }\n\n    const [\n      timestamp, type, counterparty, goods, direction,\n      amount, paymentMethod, status, transactionId,\n      merchantOrderId, memo\n    ] = fields\n\n    return {\n      timestamp,\n      type,\n      counterparty,\n      goods,\n      direction: this.parseDirection(direction),\n      amount: this.parseAmount(amount),\n      paymentMethod,\n      status,\n      transactionId,\n      merchantOrderId,\n      memo\n    }\n  }\n\n  private convertWeChatTransaction(wechatTx: WeChatTransaction): RawTransaction | null {\n    // 只处理成功的交易\n    if (wechatTx.status !== '支付成功' && wechatTx.status !== '已收钱' && wechatTx.status !== '交易成功') {\n      return null\n    }\n\n    // 解析日期\n    const date = this.parseWeChatDate(wechatTx.timestamp)\n    if (!date) {\n      throw new Error(`无效的时间格式: ${wechatTx.timestamp}`)\n    }\n\n    // 根据方向确定金额符号\n    let amount = Math.abs(wechatTx.amount)\n    if (wechatTx.direction === 'expense') {\n      amount = -amount\n    }\n\n    // 构建描述\n    let description = wechatTx.type\n    if (wechatTx.goods) {\n      description += ` - ${wechatTx.goods}`\n    }\n    if (wechatTx.counterparty) {\n      description += ` - ${wechatTx.counterparty}`\n    }\n\n    // 推断分类\n    let category = '其他'\n    if (wechatTx.type.includes('转账')) category = '转账'\n    else if (wechatTx.type.includes('红包')) category = '红包'\n    else if (wechatTx.type.includes('充值')) category = '充值'\n    else if (wechatTx.type.includes('提现')) category = '提现'\n    else if (wechatTx.type.includes('还款')) category = '还款'\n    else if (wechatTx.type.includes('消费') || wechatTx.type.includes('支付')) category = '日常消费'\n\n    return {\n      externalId: wechatTx.transactionId,\n      date: date.toISOString(),\n      amount,\n      description: description.trim(),\n      category,\n      location: wechatTx.counterparty,\n      notes: `支付方式: ${wechatTx.paymentMethod}${wechatTx.memo ? ` | 备注: ${wechatTx.memo}` : ''}`,\n      rawData: wechatTx\n    }\n  }\n\n  private extractValue(lines: string[], prefix: string): string {\n    const line = lines.find(line => line.startsWith(prefix))\n    return line ? line.split('：')[1]?.replace(/[\\[\\]]/g, '') || '' : ''\n  }\n\n  private extractNumber(lines: string[], prefix: string): number {\n    const line = lines.find(line => line.startsWith(prefix))\n    return line ? parseInt(line.match(/\\d+/)?.[0] || '0') : 0\n  }\n\n  private extractAmount(lines: string[], prefix: string): number {\n    const line = lines.find(line => line.startsWith(prefix))\n    return line ? this.parseAmount(line.match(/\\d+\\.\\d+/)?.[0] || '0') : 0\n  }\n\n  private parseDirection(direction: string): 'income' | 'expense' | 'neutral' {\n    if (direction.includes('收入')) return 'income'\n    if (direction.includes('支出')) return 'expense'\n    return 'neutral'\n  }\n\n  private parseWeChatDate(dateStr: string): Date | null {\n    // 微信时间格式: 2024-01-15 14:30:00\n    const wechatFormat = /(\\d{4})-(\\d{2})-(\\d{2})\\s+(\\d{2}):(\\d{2}):(\\d{2})/\n    const match = dateStr.match(wechatFormat)\n\n    if (match) {\n      const [, year, month, day, hour, minute, second] = match\n      return new Date(\n        parseInt(year),\n        parseInt(month) - 1, // 月份从0开始\n        parseInt(day),\n        parseInt(hour),\n        parseInt(minute),\n        parseInt(second)\n      )\n    }\n\n    // 如果不匹配微信格式，尝试其他格式\n    const date = this.parseDate(dateStr)\n    return date || null\n  }\n\n\n}\n"], "names": [], "mappings": "AAAA,UAAU;;;;AAEV;AACA;;;AAIO,MAAM,qBAAqB,+HAAA,CAAA,iBAAc;IAC9C,OAAO,YAAW;IAClB,qBAAqB;QAAC,6HAAA,CAAA,WAAQ,CAAC,MAAM;KAAC,CAAA;IACtC,qBAAqB;QAAC;KAAM,CAAA;IAE5B,MAAM,MAAM,WAA4B,EAAE,QAAkB,EAAwB;QAClF,IAAI;YACF,MAAM,UAAU,YAAY,QAAQ,CAAC;YACrC,MAAM,aAAa,IAAI,CAAC,eAAe,CAAC;YAExC,gBAAgB;YAChB,MAAM,eAAiC,EAAE;YACzC,MAAM,SAAmB,EAAE;YAE3B,WAAW,YAAY,CAAC,OAAO,CAAC,CAAC,UAAU;gBACzC,IAAI;oBACF,MAAM,cAAc,IAAI,CAAC,wBAAwB,CAAC;oBAClD,IAAI,aAAa;wBACf,aAAa,IAAI,CAAC;oBACpB;gBACF,EAAE,OAAO,OAAO;oBACd,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;gBACtF;YACF;YAEA,UAAU;YACV,MAAM,uBAAuB,aAAa,GAAG,CAAC,CAAA,MAC5C,IAAI,CAAC,sBAAsB,CAAC,KAAK;YAGnC,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;QACtD,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE;gBAAC,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;aAAC;QAClG;IACF;IAEQ,gBAAgB,UAAkB,EAAc;QACtD,MAAM,QAAQ,WAAW,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QAE1D,iBAAiB;QACjB,MAAM,WAAW;YACf,UAAU,IAAI,CAAC,YAAY,CAAC,OAAO;YACnC,WAAW,IAAI,CAAC,YAAY,CAAC,OAAO;YACpC,SAAS,IAAI,CAAC,YAAY,CAAC,OAAO;YAClC,YAAY,IAAI,CAAC,YAAY,CAAC,OAAO;YACrC,YAAY,IAAI,CAAC,YAAY,CAAC,OAAO;YACrC,SAAS;gBACP,YAAY,IAAI,CAAC,aAAa,CAAC,OAAO;gBACtC,aAAa,IAAI,CAAC,aAAa,CAAC,OAAO;gBACvC,cAAc,IAAI,CAAC,aAAa,CAAC,OAAO;gBACxC,cAAc,IAAI,CAAC,aAAa,CAAC,OAAO;gBACxC,eAAe,IAAI,CAAC,aAAa,CAAC,OAAO;gBACzC,cAAc,IAAI,CAAC,aAAa,CAAC,OAAO;gBACxC,eAAe,IAAI,CAAC,aAAa,CAAC,OAAO;YAC3C;QACF;QAEA,gCAAgC;QAChC,MAAM,wBAAwB,MAAM,SAAS,CAAC,CAAA,OAC5C,KAAK,UAAU,CAAC;QAElB,IAAI,0BAA0B,CAAC,GAAG;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,qBAAqB;QACrB,MAAM,eAAoC,MACvC,KAAK,CAAC,wBAAwB,GAC9B,MAAM,CAAC,CAAA,OAAQ,QAAQ,CAAC,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,UAAU,CAAC,SAAS,CAAC,KAAK,UAAU,CAAC,MAC3F,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,0BAA0B,CAAC,OAC5C,MAAM,CAAC,CAAA,KAAM,OAAO;QAEvB,OAAO;YAAE;YAAU;QAAa;IAClC;IAEQ,2BAA2B,IAAY,EAA4B;QACzE,qBAAqB;QACrB,MAAM,SAAmB,EAAE;QAC3B,IAAI,QAAQ;QACZ,IAAI,WAAW;QAEf,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,OAAO,IAAI,CAAC,EAAE;YAEpB,IAAI,SAAS,KAAK;gBAChB,WAAW,CAAC;gBACZ;YACF;YAEA,IAAI,SAAS,OAAO,CAAC,UAAU;gBAC7B,OAAO,IAAI,CAAC,MAAM,IAAI;gBACtB,QAAQ;gBACR;YACF;YAEA,SAAS;QACX;QACA,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,WAAW;;QAErC,IAAI,OAAO,MAAM,GAAG,IAAI;YACtB,OAAO,KAAK,UAAU;;QACxB;QAEA,MAAM,CACJ,WAAW,MAAM,cAAc,OAAO,WACtC,QAAQ,eAAe,QAAQ,eAC/B,iBAAiB,KAClB,GAAG;QAEJ,OAAO;YACL;YACA;YACA;YACA;YACA,WAAW,IAAI,CAAC,cAAc,CAAC;YAC/B,QAAQ,IAAI,CAAC,WAAW,CAAC;YACzB;YACA;YACA;YACA;YACA;QACF;IACF;IAEQ,yBAAyB,QAA2B,EAAyB;QACnF,WAAW;QACX,IAAI,SAAS,MAAM,KAAK,UAAU,SAAS,MAAM,KAAK,SAAS,SAAS,MAAM,KAAK,QAAQ;YACzF,OAAO;QACT;QAEA,OAAO;QACP,MAAM,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,SAAS;QACpD,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,SAAS,SAAS,EAAE;QAClD;QAEA,aAAa;QACb,IAAI,SAAS,KAAK,GAAG,CAAC,SAAS,MAAM;QACrC,IAAI,SAAS,SAAS,KAAK,WAAW;YACpC,SAAS,CAAC;QACZ;QAEA,OAAO;QACP,IAAI,cAAc,SAAS,IAAI;QAC/B,IAAI,SAAS,KAAK,EAAE;YAClB,eAAe,CAAC,GAAG,EAAE,SAAS,KAAK,EAAE;QACvC;QACA,IAAI,SAAS,YAAY,EAAE;YACzB,eAAe,CAAC,GAAG,EAAE,SAAS,YAAY,EAAE;QAC9C;QAEA,OAAO;QACP,IAAI,WAAW;QACf,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,WAAW;aACxC,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,WAAW;aAC7C,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,WAAW;aAC7C,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,WAAW;aAC7C,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,WAAW;aAC7C,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,SAAS,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,WAAW;QAElF,OAAO;YACL,YAAY,SAAS,aAAa;YAClC,MAAM,KAAK,WAAW;YACtB;YACA,aAAa,YAAY,IAAI;YAC7B;YACA,UAAU,SAAS,YAAY;YAC/B,OAAO,CAAC,MAAM,EAAE,SAAS,aAAa,GAAG,SAAS,IAAI,GAAG,CAAC,OAAO,EAAE,SAAS,IAAI,EAAE,GAAG,IAAI;YACzF,SAAS;QACX;IACF;IAEQ,aAAa,KAAe,EAAE,MAAc,EAAU;QAC5D,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,UAAU,CAAC;QAChD,OAAO,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,WAAW,OAAO,KAAK;IACnE;IAEQ,cAAc,KAAe,EAAE,MAAc,EAAU;QAC7D,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,UAAU,CAAC;QAChD,OAAO,OAAO,SAAS,KAAK,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,OAAO;IAC1D;IAEQ,cAAc,KAAe,EAAE,MAAc,EAAU;QAC7D,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,UAAU,CAAC;QAChD,OAAO,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,CAAC,aAAa,CAAC,EAAE,IAAI,OAAO;IACvE;IAEQ,eAAe,SAAiB,EAAoC;QAC1E,IAAI,UAAU,QAAQ,CAAC,OAAO,OAAO;QACrC,IAAI,UAAU,QAAQ,CAAC,OAAO,OAAO;QACrC,OAAO;IACT;IAEQ,gBAAgB,OAAe,EAAe;QACpD,8BAA8B;QAC9B,MAAM,eAAe;QACrB,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAE5B,IAAI,OAAO;YACT,MAAM,GAAG,MAAM,OAAO,KAAK,MAAM,QAAQ,OAAO,GAAG;YACnD,OAAO,IAAI,KACT,SAAS,OACT,SAAS,SAAS,GAClB,SAAS,MACT,SAAS,OACT,SAAS,SACT,SAAS;QAEb;QAEA,mBAAmB;QACnB,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC;QAC5B,OAAO,QAAQ;IACjB;AAGF", "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/parsers/alipay.ts"], "sourcesContent": ["// 支付宝账单解析器\n\nimport { CardType } from '@prisma/client'\nimport { BaseBillParser } from './base'\nimport { RawTransaction, ParseResult } from './types'\n\nexport class AlipayParser extends BaseBillParser {\n  name = '支付宝账单解析器'\n  supportedCardTypes = [CardType.ALIPAY]\n  supportedFileTypes = ['csv']\n\n  async parse(fileContent: string | Buffer, cardType: CardType): Promise<ParseResult> {\n    try {\n      const content = fileContent.toString('utf-8')\n      const lines = this.parseCSV(content)\n      \n      // 查找表头行\n      let headerIndex = -1\n      for (let i = 0; i < lines.length; i++) {\n        if (lines[i].some(cell => cell.includes('交易时间') || cell.includes('交易创建时间'))) {\n          headerIndex = i\n          break\n        }\n      }\n\n      if (headerIndex === -1) {\n        return this.createParseResult([], ['未找到有效的表头'])\n      }\n\n      const headers = lines[headerIndex]\n      const dataLines = lines.slice(headerIndex + 1)\n\n      // 解析每一行数据\n      const transactions: RawTransaction[] = []\n      const errors: string[] = []\n\n      for (let i = 0; i < dataLines.length; i++) {\n        try {\n          const row = dataLines[i]\n          if (row.length < headers.length || row.every(cell => !cell.trim())) {\n            continue // 跳过空行或不完整行\n          }\n\n          const transaction = this.parseAlipayRow(row, headers)\n          if (transaction) {\n            transactions.push(transaction)\n          }\n        } catch (error) {\n          errors.push(`第${headerIndex + i + 2}行解析失败: ${error}`)\n        }\n      }\n\n      // 标准化交易数据\n      const standardTransactions = transactions.map(raw => \n        this.standardizeTransaction(raw, cardType)\n      )\n\n      return this.createParseResult(standardTransactions, errors)\n    } catch (error) {\n      return this.createParseResult([], [`文件解析失败: ${error}`])\n    }\n  }\n\n  private parseAlipayRow(row: string[], headers: string[]): RawTransaction | null {\n    // 创建字段映射\n    const fieldMap: Record<string, string> = {}\n    headers.forEach((header, index) => {\n      if (row[index]) {\n        fieldMap[header.trim()] = row[index].trim()\n      }\n    })\n\n    // 支付宝账单常见字段映射\n    const timeField = this.findField(fieldMap, ['交易时间', '交易创建时间', '时间'])\n    const typeField = this.findField(fieldMap, ['交易分类', '类型'])\n    const counterpartyField = this.findField(fieldMap, ['交易对方', '对方'])\n    const productField = this.findField(fieldMap, ['商品名称', '商品说明', '备注'])\n    const statusField = this.findField(fieldMap, ['交易状态', '状态'])\n    const amountField = this.findField(fieldMap, ['金额', '收支金额'])\n    const balanceField = this.findField(fieldMap, ['账户余额', '余额'])\n    const orderField = this.findField(fieldMap, ['交易订单号', '订单号'])\n\n    if (!timeField || !amountField) {\n      return null\n    }\n\n    // 解析时间\n    const date = this.parseDate(timeField)\n    if (!date) {\n      throw new Error(`无效的时间格式: ${timeField}`)\n    }\n\n    // 解析金额\n    let amount = this.parseAmount(amountField)\n    \n    // 支付宝账单中，通常用+/-符号表示收支\n    // 或者通过交易分类判断\n    if (amountField.startsWith('-') || amountField.startsWith('－')) {\n      amount = -Math.abs(amount)\n    } else if (amountField.startsWith('+') || amountField.startsWith('＋')) {\n      amount = Math.abs(amount)\n    } else if (typeField) {\n      // 根据交易类型判断\n      if (typeField.includes('支出') || typeField.includes('消费') || typeField.includes('转账给')) {\n        amount = -Math.abs(amount)\n      } else if (typeField.includes('收入') || typeField.includes('转账收款')) {\n        amount = Math.abs(amount)\n      }\n    }\n\n    // 构建描述\n    let description = typeField || '支付宝交易'\n    if (counterpartyField) {\n      description += ` - ${counterpartyField}`\n    }\n    if (productField) {\n      description += ` - ${productField}`\n    }\n\n    // 推断分类\n    let category = '其他'\n    if (typeField) {\n      if (typeField.includes('转账')) category = '转账'\n      else if (typeField.includes('红包')) category = '红包'\n      else if (typeField.includes('充值')) category = '充值'\n      else if (typeField.includes('提现')) category = '提现'\n      else if (typeField.includes('还款')) category = '还款'\n      else if (typeField.includes('消费') || typeField.includes('支付')) category = '日常消费'\n      else if (typeField.includes('理财')) category = '投资理财'\n      else if (typeField.includes('保险')) category = '保险'\n    }\n\n    // 解析余额\n    let balance: number | undefined\n    if (balanceField) {\n      balance = this.parseAmount(balanceField)\n    }\n\n    return {\n      externalId: orderField,\n      date: date.toISOString(),\n      amount,\n      description: description.trim(),\n      category,\n      location: counterpartyField,\n      balance,\n      notes: `状态: ${statusField || '未知'}`,\n      rawData: fieldMap\n    }\n  }\n\n  private findField(fieldMap: Record<string, string>, possibleNames: string[]): string | undefined {\n    for (const name of possibleNames) {\n      for (const key of Object.keys(fieldMap)) {\n        if (key.includes(name)) {\n          return fieldMap[key]\n        }\n      }\n    }\n    return undefined\n  }\n}\n"], "names": [], "mappings": "AAAA,WAAW;;;;AAEX;AACA;;;AAGO,MAAM,qBAAqB,+HAAA,CAAA,iBAAc;IAC9C,OAAO,WAAU;IACjB,qBAAqB;QAAC,6HAAA,CAAA,WAAQ,CAAC,MAAM;KAAC,CAAA;IACtC,qBAAqB;QAAC;KAAM,CAAA;IAE5B,MAAM,MAAM,WAA4B,EAAE,QAAkB,EAAwB;QAClF,IAAI;YACF,MAAM,UAAU,YAAY,QAAQ,CAAC;YACrC,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC;YAE5B,QAAQ;YACR,IAAI,cAAc,CAAC;YACnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,WAAW,KAAK,QAAQ,CAAC,YAAY;oBAC3E,cAAc;oBACd;gBACF;YACF;YAEA,IAAI,gBAAgB,CAAC,GAAG;gBACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE;oBAAC;iBAAW;YAChD;YAEA,MAAM,UAAU,KAAK,CAAC,YAAY;YAClC,MAAM,YAAY,MAAM,KAAK,CAAC,cAAc;YAE5C,UAAU;YACV,MAAM,eAAiC,EAAE;YACzC,MAAM,SAAmB,EAAE;YAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACzC,IAAI;oBACF,MAAM,MAAM,SAAS,CAAC,EAAE;oBACxB,IAAI,IAAI,MAAM,GAAG,QAAQ,MAAM,IAAI,IAAI,KAAK,CAAC,CAAA,OAAQ,CAAC,KAAK,IAAI,KAAK;wBAClE,UAAS,YAAY;oBACvB;oBAEA,MAAM,cAAc,IAAI,CAAC,cAAc,CAAC,KAAK;oBAC7C,IAAI,aAAa;wBACf,aAAa,IAAI,CAAC;oBACpB;gBACF,EAAE,OAAO,OAAO;oBACd,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc,IAAI,EAAE,OAAO,EAAE,OAAO;gBACtD;YACF;YAEA,UAAU;YACV,MAAM,uBAAuB,aAAa,GAAG,CAAC,CAAA,MAC5C,IAAI,CAAC,sBAAsB,CAAC,KAAK;YAGnC,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;QACtD,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE;gBAAC,CAAC,QAAQ,EAAE,OAAO;aAAC;QACxD;IACF;IAEQ,eAAe,GAAa,EAAE,OAAiB,EAAyB;QAC9E,SAAS;QACT,MAAM,WAAmC,CAAC;QAC1C,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,IAAI,GAAG,CAAC,MAAM,EAAE;gBACd,QAAQ,CAAC,OAAO,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI;YAC3C;QACF;QAEA,cAAc;QACd,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;YAAU;SAAK;QACnE,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;SAAK;QACzD,MAAM,oBAAoB,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;SAAK;QACjE,MAAM,eAAe,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;YAAQ;SAAK;QACpE,MAAM,cAAc,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;SAAK;QAC3D,MAAM,cAAc,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAM;SAAO;QAC3D,MAAM,eAAe,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;SAAK;QAC5D,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAS;SAAM;QAE5D,IAAI,CAAC,aAAa,CAAC,aAAa;YAC9B,OAAO;QACT;QAEA,OAAO;QACP,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC;QAC5B,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,WAAW;QACzC;QAEA,OAAO;QACP,IAAI,SAAS,IAAI,CAAC,WAAW,CAAC;QAE9B,sBAAsB;QACtB,aAAa;QACb,IAAI,YAAY,UAAU,CAAC,QAAQ,YAAY,UAAU,CAAC,MAAM;YAC9D,SAAS,CAAC,KAAK,GAAG,CAAC;QACrB,OAAO,IAAI,YAAY,UAAU,CAAC,QAAQ,YAAY,UAAU,CAAC,MAAM;YACrE,SAAS,KAAK,GAAG,CAAC;QACpB,OAAO,IAAI,WAAW;YACpB,WAAW;YACX,IAAI,UAAU,QAAQ,CAAC,SAAS,UAAU,QAAQ,CAAC,SAAS,UAAU,QAAQ,CAAC,QAAQ;gBACrF,SAAS,CAAC,KAAK,GAAG,CAAC;YACrB,OAAO,IAAI,UAAU,QAAQ,CAAC,SAAS,UAAU,QAAQ,CAAC,SAAS;gBACjE,SAAS,KAAK,GAAG,CAAC;YACpB;QACF;QAEA,OAAO;QACP,IAAI,cAAc,aAAa;QAC/B,IAAI,mBAAmB;YACrB,eAAe,CAAC,GAAG,EAAE,mBAAmB;QAC1C;QACA,IAAI,cAAc;YAChB,eAAe,CAAC,GAAG,EAAE,cAAc;QACrC;QAEA,OAAO;QACP,IAAI,WAAW;QACf,IAAI,WAAW;YACb,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACpC,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACzC,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACzC,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACzC,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACzC,IAAI,UAAU,QAAQ,CAAC,SAAS,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACrE,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACzC,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;QAChD;QAEA,OAAO;QACP,IAAI;QACJ,IAAI,cAAc;YAChB,UAAU,IAAI,CAAC,WAAW,CAAC;QAC7B;QAEA,OAAO;YACL,YAAY;YACZ,MAAM,KAAK,WAAW;YACtB;YACA,aAAa,YAAY,IAAI;YAC7B;YACA,UAAU;YACV;YACA,OAAO,CAAC,IAAI,EAAE,eAAe,MAAM;YACnC,SAAS;QACX;IACF;IAEQ,UAAU,QAAgC,EAAE,aAAuB,EAAsB;QAC/F,KAAK,MAAM,QAAQ,cAAe;YAChC,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,UAAW;gBACvC,IAAI,IAAI,QAAQ,CAAC,OAAO;oBACtB,OAAO,QAAQ,CAAC,IAAI;gBACtB;YACF;QACF;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 708, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/parsers/cmb-debit.ts"], "sourcesContent": ["// 招商银行借记卡账单解析器\n\nimport { CardType } from '@prisma/client'\nimport { BaseBillParser } from './base'\nimport { RawTransaction, ParseResult } from './types'\n\nexport class CmbDebitParser extends BaseBillParser {\n  name = '招商银行借记卡账单解析器'\n  supportedCardTypes = [CardType.DEBIT_CARD]\n  supportedFileTypes = ['csv', 'xls', 'xlsx']\n\n  async parse(fileContent: string | Buffer, cardType: CardType): Promise<ParseResult> {\n    try {\n      const content = fileContent.toString('utf-8')\n      const lines = this.parseCSV(content)\n      \n      // 查找表头行\n      let headerIndex = -1\n      for (let i = 0; i < lines.length; i++) {\n        if (lines[i].some(cell => \n          cell.includes('交易日期') || \n          cell.includes('记账日期') ||\n          cell.includes('交易时间')\n        )) {\n          headerIndex = i\n          break\n        }\n      }\n\n      if (headerIndex === -1) {\n        return this.createParseResult([], ['未找到有效的表头'])\n      }\n\n      const headers = lines[headerIndex]\n      const dataLines = lines.slice(headerIndex + 1)\n\n      // 解析每一行数据\n      const transactions: RawTransaction[] = []\n      const errors: string[] = []\n\n      for (let i = 0; i < dataLines.length; i++) {\n        try {\n          const row = dataLines[i]\n          if (row.length < headers.length || row.every(cell => !cell.trim())) {\n            continue // 跳过空行或不完整行\n          }\n\n          const transaction = this.parseCmbDebitRow(row, headers)\n          if (transaction) {\n            transactions.push(transaction)\n          }\n        } catch (error) {\n          errors.push(`第${headerIndex + i + 2}行解析失败: ${error}`)\n        }\n      }\n\n      // 标准化交易数据\n      const standardTransactions = transactions.map(raw => \n        this.standardizeTransaction(raw, cardType)\n      )\n\n      return this.createParseResult(standardTransactions, errors)\n    } catch (error) {\n      return this.createParseResult([], [`文件解析失败: ${error}`])\n    }\n  }\n\n  private parseCmbDebitRow(row: string[], headers: string[]): RawTransaction | null {\n    // 创建字段映射\n    const fieldMap: Record<string, string> = {}\n    headers.forEach((header, index) => {\n      if (row[index]) {\n        fieldMap[header.trim()] = row[index].trim()\n      }\n    })\n\n    // 招商银行借记卡账单常见字段映射\n    const dateField = this.findField(fieldMap, ['交易日期', '记账日期', '交易时间'])\n    const typeField = this.findField(fieldMap, ['交易类型', '摘要', '业务类型'])\n    const descField = this.findField(fieldMap, ['交易说明', '备注', '摘要说明'])\n    const amountField = this.findField(fieldMap, ['交易金额', '金额', '发生额'])\n    const balanceField = this.findField(fieldMap, ['账户余额', '余额', '可用余额'])\n    const counterpartyField = this.findField(fieldMap, ['对方户名', '对方账户', '交易对方'])\n    const channelField = this.findField(fieldMap, ['交易渠道', '渠道'])\n    const referenceField = this.findField(fieldMap, ['交易流水号', '流水号', '凭证号'])\n\n    if (!dateField || !amountField) {\n      return null\n    }\n\n    // 解析时间\n    const date = this.parseDate(dateField)\n    if (!date) {\n      throw new Error(`无效的时间格式: ${dateField}`)\n    }\n\n    // 解析金额\n    let amount = this.parseAmount(amountField)\n    \n    // 借记卡：正数表示收入（存款），负数表示支出（取款/消费）\n    // 招商银行通常用+/-符号或者单独的借贷标识\n    if (amountField.includes('-') || amountField.includes('支出')) {\n      amount = -Math.abs(amount)\n    } else if (amountField.includes('+') || amountField.includes('收入')) {\n      amount = Math.abs(amount)\n    } else {\n      // 根据交易类型判断\n      if (typeField) {\n        if (typeField.includes('支出') || \n            typeField.includes('消费') || \n            typeField.includes('取现') ||\n            typeField.includes('转出') ||\n            typeField.includes('扣费')) {\n          amount = -Math.abs(amount)\n        } else if (typeField.includes('存入') || \n                   typeField.includes('转入') ||\n                   typeField.includes('利息') ||\n                   typeField.includes('退款')) {\n          amount = Math.abs(amount)\n        }\n      }\n    }\n\n    // 构建描述\n    let description = typeField || '银行交易'\n    if (descField) {\n      description += ` - ${descField}`\n    }\n    if (counterpartyField) {\n      description += ` - ${counterpartyField}`\n    }\n\n    // 推断分类\n    let category = '其他'\n    if (typeField) {\n      if (typeField.includes('转账')) category = '转账'\n      else if (typeField.includes('取现') || typeField.includes('ATM')) category = '取现'\n      else if (typeField.includes('存款') || typeField.includes('存入')) category = '存款'\n      else if (typeField.includes('利息')) category = '利息收入'\n      else if (typeField.includes('工资') || typeField.includes('代发')) category = '工资收入'\n      else if (typeField.includes('消费') || typeField.includes('刷卡')) category = '日常消费'\n      else if (typeField.includes('还款')) category = '还款'\n      else if (typeField.includes('手续费') || typeField.includes('服务费')) category = '银行费用'\n    }\n\n    // 解析余额\n    let balance: number | undefined\n    if (balanceField) {\n      balance = this.parseAmount(balanceField)\n    }\n\n    return {\n      externalId: referenceField,\n      date: date.toISOString(),\n      amount,\n      description: description.trim(),\n      category,\n      location: counterpartyField,\n      balance,\n      notes: `交易渠道: ${channelField || '未知'}`,\n      rawData: fieldMap\n    }\n  }\n\n  private findField(fieldMap: Record<string, string>, possibleNames: string[]): string | undefined {\n    for (const name of possibleNames) {\n      for (const key of Object.keys(fieldMap)) {\n        if (key.includes(name)) {\n          return fieldMap[key]\n        }\n      }\n    }\n    return undefined\n  }\n}\n"], "names": [], "mappings": "AAAA,eAAe;;;;AAEf;AACA;;;AAGO,MAAM,uBAAuB,+HAAA,CAAA,iBAAc;IAChD,OAAO,eAAc;IACrB,qBAAqB;QAAC,6HAAA,CAAA,WAAQ,CAAC,UAAU;KAAC,CAAA;IAC1C,qBAAqB;QAAC;QAAO;QAAO;KAAO,CAAA;IAE3C,MAAM,MAAM,WAA4B,EAAE,QAAkB,EAAwB;QAClF,IAAI;YACF,MAAM,UAAU,YAAY,QAAQ,CAAC;YACrC,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC;YAE5B,QAAQ;YACR,IAAI,cAAc,CAAC;YACnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA,OAChB,KAAK,QAAQ,CAAC,WACd,KAAK,QAAQ,CAAC,WACd,KAAK,QAAQ,CAAC,UACb;oBACD,cAAc;oBACd;gBACF;YACF;YAEA,IAAI,gBAAgB,CAAC,GAAG;gBACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE;oBAAC;iBAAW;YAChD;YAEA,MAAM,UAAU,KAAK,CAAC,YAAY;YAClC,MAAM,YAAY,MAAM,KAAK,CAAC,cAAc;YAE5C,UAAU;YACV,MAAM,eAAiC,EAAE;YACzC,MAAM,SAAmB,EAAE;YAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACzC,IAAI;oBACF,MAAM,MAAM,SAAS,CAAC,EAAE;oBACxB,IAAI,IAAI,MAAM,GAAG,QAAQ,MAAM,IAAI,IAAI,KAAK,CAAC,CAAA,OAAQ,CAAC,KAAK,IAAI,KAAK;wBAClE,UAAS,YAAY;oBACvB;oBAEA,MAAM,cAAc,IAAI,CAAC,gBAAgB,CAAC,KAAK;oBAC/C,IAAI,aAAa;wBACf,aAAa,IAAI,CAAC;oBACpB;gBACF,EAAE,OAAO,OAAO;oBACd,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc,IAAI,EAAE,OAAO,EAAE,OAAO;gBACtD;YACF;YAEA,UAAU;YACV,MAAM,uBAAuB,aAAa,GAAG,CAAC,CAAA,MAC5C,IAAI,CAAC,sBAAsB,CAAC,KAAK;YAGnC,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;QACtD,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE;gBAAC,CAAC,QAAQ,EAAE,OAAO;aAAC;QACxD;IACF;IAEQ,iBAAiB,GAAa,EAAE,OAAiB,EAAyB;QAChF,SAAS;QACT,MAAM,WAAmC,CAAC;QAC1C,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,IAAI,GAAG,CAAC,MAAM,EAAE;gBACd,QAAQ,CAAC,OAAO,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI;YAC3C;QACF;QAEA,kBAAkB;QAClB,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;YAAQ;SAAO;QACnE,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;YAAM;SAAO;QACjE,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;YAAM;SAAO;QACjE,MAAM,cAAc,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;YAAM;SAAM;QAClE,MAAM,eAAe,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;YAAM;SAAO;QACpE,MAAM,oBAAoB,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;YAAQ;SAAO;QAC3E,MAAM,eAAe,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;SAAK;QAC5D,MAAM,iBAAiB,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAS;YAAO;SAAM;QAEvE,IAAI,CAAC,aAAa,CAAC,aAAa;YAC9B,OAAO;QACT;QAEA,OAAO;QACP,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC;QAC5B,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,WAAW;QACzC;QAEA,OAAO;QACP,IAAI,SAAS,IAAI,CAAC,WAAW,CAAC;QAE9B,+BAA+B;QAC/B,wBAAwB;QACxB,IAAI,YAAY,QAAQ,CAAC,QAAQ,YAAY,QAAQ,CAAC,OAAO;YAC3D,SAAS,CAAC,KAAK,GAAG,CAAC;QACrB,OAAO,IAAI,YAAY,QAAQ,CAAC,QAAQ,YAAY,QAAQ,CAAC,OAAO;YAClE,SAAS,KAAK,GAAG,CAAC;QACpB,OAAO;YACL,WAAW;YACX,IAAI,WAAW;gBACb,IAAI,UAAU,QAAQ,CAAC,SACnB,UAAU,QAAQ,CAAC,SACnB,UAAU,QAAQ,CAAC,SACnB,UAAU,QAAQ,CAAC,SACnB,UAAU,QAAQ,CAAC,OAAO;oBAC5B,SAAS,CAAC,KAAK,GAAG,CAAC;gBACrB,OAAO,IAAI,UAAU,QAAQ,CAAC,SACnB,UAAU,QAAQ,CAAC,SACnB,UAAU,QAAQ,CAAC,SACnB,UAAU,QAAQ,CAAC,OAAO;oBACnC,SAAS,KAAK,GAAG,CAAC;gBACpB;YACF;QACF;QAEA,OAAO;QACP,IAAI,cAAc,aAAa;QAC/B,IAAI,WAAW;YACb,eAAe,CAAC,GAAG,EAAE,WAAW;QAClC;QACA,IAAI,mBAAmB;YACrB,eAAe,CAAC,GAAG,EAAE,mBAAmB;QAC1C;QAEA,OAAO;QACP,IAAI,WAAW;QACf,IAAI,WAAW;YACb,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACpC,IAAI,UAAU,QAAQ,CAAC,SAAS,UAAU,QAAQ,CAAC,QAAQ,WAAW;iBACtE,IAAI,UAAU,QAAQ,CAAC,SAAS,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACrE,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACzC,IAAI,UAAU,QAAQ,CAAC,SAAS,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACrE,IAAI,UAAU,QAAQ,CAAC,SAAS,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACrE,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACzC,IAAI,UAAU,QAAQ,CAAC,UAAU,UAAU,QAAQ,CAAC,QAAQ,WAAW;QAC9E;QAEA,OAAO;QACP,IAAI;QACJ,IAAI,cAAc;YAChB,UAAU,IAAI,CAAC,WAAW,CAAC;QAC7B;QAEA,OAAO;YACL,YAAY;YACZ,MAAM,KAAK,WAAW;YACtB;YACA,aAAa,YAAY,IAAI;YAC7B;YACA,UAAU;YACV;YACA,OAAO,CAAC,MAAM,EAAE,gBAAgB,MAAM;YACtC,SAAS;QACX;IACF;IAEQ,UAAU,QAAgC,EAAE,aAAuB,EAAsB;QAC/F,KAAK,MAAM,QAAQ,cAAe;YAChC,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,UAAW;gBACvC,IAAI,IAAI,QAAQ,CAAC,OAAO;oBACtB,OAAO,QAAQ,CAAC,IAAI;gBACtB;YACF;QACF;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 899, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/parsers/cmb-credit.ts"], "sourcesContent": ["// 招商银行信用卡账单解析器\n\nimport { CardType } from '@prisma/client'\nimport { BaseBillParser } from './base'\nimport { RawTransaction, ParseResult } from './types'\n\nexport class CmbCreditParser extends BaseBillParser {\n  name = '招商银行信用卡账单解析器'\n  supportedCardTypes = [CardType.CREDIT_CARD]\n  supportedFileTypes = ['csv', 'xls', 'xlsx']\n\n  async parse(fileContent: string | Buffer, cardType: CardType): Promise<ParseResult> {\n    try {\n      const content = fileContent.toString('utf-8')\n      const lines = this.parseCSV(content)\n      \n      // 查找表头行\n      let headerIndex = -1\n      for (let i = 0; i < lines.length; i++) {\n        if (lines[i].some(cell => \n          cell.includes('交易日期') || \n          cell.includes('记账日期') ||\n          cell.includes('交易时间')\n        )) {\n          headerIndex = i\n          break\n        }\n      }\n\n      if (headerIndex === -1) {\n        return this.createParseResult([], ['未找到有效的表头'])\n      }\n\n      const headers = lines[headerIndex]\n      const dataLines = lines.slice(headerIndex + 1)\n\n      // 解析每一行数据\n      const transactions: RawTransaction[] = []\n      const errors: string[] = []\n\n      for (let i = 0; i < dataLines.length; i++) {\n        try {\n          const row = dataLines[i]\n          if (row.length < headers.length || row.every(cell => !cell.trim())) {\n            continue // 跳过空行或不完整行\n          }\n\n          const transaction = this.parseCmbCreditRow(row, headers)\n          if (transaction) {\n            transactions.push(transaction)\n          }\n        } catch (error) {\n          errors.push(`第${headerIndex + i + 2}行解析失败: ${error}`)\n        }\n      }\n\n      // 标准化交易数据\n      const standardTransactions = transactions.map(raw => \n        this.standardizeTransaction(raw, cardType)\n      )\n\n      return this.createParseResult(standardTransactions, errors)\n    } catch (error) {\n      return this.createParseResult([], [`文件解析失败: ${error}`])\n    }\n  }\n\n  private parseCmbCreditRow(row: string[], headers: string[]): RawTransaction | null {\n    // 创建字段映射\n    const fieldMap: Record<string, string> = {}\n    headers.forEach((header, index) => {\n      if (row[index]) {\n        fieldMap[header.trim()] = row[index].trim()\n      }\n    })\n\n    // 招商银行信用卡账单常见字段映射\n    const dateField = this.findField(fieldMap, ['交易日期', '记账日期', '交易时间'])\n    const typeField = this.findField(fieldMap, ['交易类型', '摘要', '业务类型'])\n    const descField = this.findField(fieldMap, ['交易说明', '备注', '摘要说明', '商户名称'])\n    const amountField = this.findField(fieldMap, ['交易金额', '金额', '人民币金额'])\n    const currencyField = this.findField(fieldMap, ['币种', '交易币种'])\n    const merchantField = this.findField(fieldMap, ['商户名称', '商户'])\n    const referenceField = this.findField(fieldMap, ['交易流水号', '流水号', '凭证号'])\n    const statusField = this.findField(fieldMap, ['交易状态', '状态'])\n\n    if (!dateField || !amountField) {\n      return null\n    }\n\n    // 解析时间\n    const date = this.parseDate(dateField)\n    if (!date) {\n      throw new Error(`无效的时间格式: ${dateField}`)\n    }\n\n    // 解析金额\n    let amount = this.parseAmount(amountField)\n    \n    // 信用卡：正数表示支出（消费），负数表示收入（还款/退款）\n    // 招商银行信用卡通常用+/-符号或者单独的借贷标识\n    if (amountField.includes('-') || amountField.includes('还款') || amountField.includes('退款')) {\n      amount = -Math.abs(amount) // 负数表示还款/退款（对信用卡来说是收入）\n    } else if (amountField.includes('+') || typeField?.includes('消费')) {\n      amount = Math.abs(amount) // 正数表示消费（对信用卡来说是支出）\n    } else {\n      // 根据交易类型判断\n      if (typeField) {\n        if (typeField.includes('还款') || \n            typeField.includes('退款') ||\n            typeField.includes('溢缴款') ||\n            typeField.includes('调整')) {\n          amount = -Math.abs(amount) // 收入\n        } else {\n          amount = Math.abs(amount) // 支出\n        }\n      }\n    }\n\n    // 构建描述\n    let description = typeField || '信用卡交易'\n    if (merchantField) {\n      description += ` - ${merchantField}`\n    }\n    if (descField && descField !== merchantField) {\n      description += ` - ${descField}`\n    }\n\n    // 推断分类\n    let category = '其他'\n    if (typeField) {\n      if (typeField.includes('还款')) category = '信用卡还款'\n      else if (typeField.includes('取现')) category = '信用卡取现'\n      else if (typeField.includes('消费') || typeField.includes('刷卡')) category = '日常消费'\n      else if (typeField.includes('分期')) category = '分期付款'\n      else if (typeField.includes('年费')) category = '银行费用'\n      else if (typeField.includes('利息')) category = '利息费用'\n      else if (typeField.includes('退款')) category = '退款'\n      else if (typeField.includes('转账')) category = '转账'\n    }\n\n    // 根据商户名称进一步细化分类\n    if (merchantField && category === '日常消费') {\n      if (merchantField.includes('超市') || merchantField.includes('便利店')) {\n        category = '超市购物'\n      } else if (merchantField.includes('餐厅') || merchantField.includes('饭店')) {\n        category = '餐饮美食'\n      } else if (merchantField.includes('加油站')) {\n        category = '交通出行'\n      } else if (merchantField.includes('医院') || merchantField.includes('药店')) {\n        category = '医疗健康'\n      }\n    }\n\n    return {\n      externalId: referenceField,\n      date: date.toISOString(),\n      amount,\n      description: description.trim(),\n      category,\n      location: merchantField,\n      notes: `币种: ${currencyField || 'CNY'}, 状态: ${statusField || '未知'}`,\n      rawData: fieldMap\n    }\n  }\n\n  private findField(fieldMap: Record<string, string>, possibleNames: string[]): string | undefined {\n    for (const name of possibleNames) {\n      for (const key of Object.keys(fieldMap)) {\n        if (key.includes(name)) {\n          return fieldMap[key]\n        }\n      }\n    }\n    return undefined\n  }\n}\n"], "names": [], "mappings": "AAAA,eAAe;;;;AAEf;AACA;;;AAGO,MAAM,wBAAwB,+HAAA,CAAA,iBAAc;IACjD,OAAO,eAAc;IACrB,qBAAqB;QAAC,6HAAA,CAAA,WAAQ,CAAC,WAAW;KAAC,CAAA;IAC3C,qBAAqB;QAAC;QAAO;QAAO;KAAO,CAAA;IAE3C,MAAM,MAAM,WAA4B,EAAE,QAAkB,EAAwB;QAClF,IAAI;YACF,MAAM,UAAU,YAAY,QAAQ,CAAC;YACrC,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC;YAE5B,QAAQ;YACR,IAAI,cAAc,CAAC;YACnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA,OAChB,KAAK,QAAQ,CAAC,WACd,KAAK,QAAQ,CAAC,WACd,KAAK,QAAQ,CAAC,UACb;oBACD,cAAc;oBACd;gBACF;YACF;YAEA,IAAI,gBAAgB,CAAC,GAAG;gBACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE;oBAAC;iBAAW;YAChD;YAEA,MAAM,UAAU,KAAK,CAAC,YAAY;YAClC,MAAM,YAAY,MAAM,KAAK,CAAC,cAAc;YAE5C,UAAU;YACV,MAAM,eAAiC,EAAE;YACzC,MAAM,SAAmB,EAAE;YAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACzC,IAAI;oBACF,MAAM,MAAM,SAAS,CAAC,EAAE;oBACxB,IAAI,IAAI,MAAM,GAAG,QAAQ,MAAM,IAAI,IAAI,KAAK,CAAC,CAAA,OAAQ,CAAC,KAAK,IAAI,KAAK;wBAClE,UAAS,YAAY;oBACvB;oBAEA,MAAM,cAAc,IAAI,CAAC,iBAAiB,CAAC,KAAK;oBAChD,IAAI,aAAa;wBACf,aAAa,IAAI,CAAC;oBACpB;gBACF,EAAE,OAAO,OAAO;oBACd,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc,IAAI,EAAE,OAAO,EAAE,OAAO;gBACtD;YACF;YAEA,UAAU;YACV,MAAM,uBAAuB,aAAa,GAAG,CAAC,CAAA,MAC5C,IAAI,CAAC,sBAAsB,CAAC,KAAK;YAGnC,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;QACtD,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE;gBAAC,CAAC,QAAQ,EAAE,OAAO;aAAC;QACxD;IACF;IAEQ,kBAAkB,GAAa,EAAE,OAAiB,EAAyB;QACjF,SAAS;QACT,MAAM,WAAmC,CAAC;QAC1C,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,IAAI,GAAG,CAAC,MAAM,EAAE;gBACd,QAAQ,CAAC,OAAO,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI;YAC3C;QACF;QAEA,kBAAkB;QAClB,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;YAAQ;SAAO;QACnE,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;YAAM;SAAO;QACjE,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;YAAM;YAAQ;SAAO;QACzE,MAAM,cAAc,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;YAAM;SAAQ;QACpE,MAAM,gBAAgB,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAM;SAAO;QAC7D,MAAM,gBAAgB,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;SAAK;QAC7D,MAAM,iBAAiB,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAS;YAAO;SAAM;QACvE,MAAM,cAAc,IAAI,CAAC,SAAS,CAAC,UAAU;YAAC;YAAQ;SAAK;QAE3D,IAAI,CAAC,aAAa,CAAC,aAAa;YAC9B,OAAO;QACT;QAEA,OAAO;QACP,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC;QAC5B,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,WAAW;QACzC;QAEA,OAAO;QACP,IAAI,SAAS,IAAI,CAAC,WAAW,CAAC;QAE9B,+BAA+B;QAC/B,2BAA2B;QAC3B,IAAI,YAAY,QAAQ,CAAC,QAAQ,YAAY,QAAQ,CAAC,SAAS,YAAY,QAAQ,CAAC,OAAO;YACzF,SAAS,CAAC,KAAK,GAAG,CAAC,QAAQ,uBAAuB;;QACpD,OAAO,IAAI,YAAY,QAAQ,CAAC,QAAQ,WAAW,SAAS,OAAO;YACjE,SAAS,KAAK,GAAG,CAAC,QAAQ,oBAAoB;;QAChD,OAAO;YACL,WAAW;YACX,IAAI,WAAW;gBACb,IAAI,UAAU,QAAQ,CAAC,SACnB,UAAU,QAAQ,CAAC,SACnB,UAAU,QAAQ,CAAC,UACnB,UAAU,QAAQ,CAAC,OAAO;oBAC5B,SAAS,CAAC,KAAK,GAAG,CAAC,QAAQ,KAAK;;gBAClC,OAAO;oBACL,SAAS,KAAK,GAAG,CAAC,QAAQ,KAAK;;gBACjC;YACF;QACF;QAEA,OAAO;QACP,IAAI,cAAc,aAAa;QAC/B,IAAI,eAAe;YACjB,eAAe,CAAC,GAAG,EAAE,eAAe;QACtC;QACA,IAAI,aAAa,cAAc,eAAe;YAC5C,eAAe,CAAC,GAAG,EAAE,WAAW;QAClC;QAEA,OAAO;QACP,IAAI,WAAW;QACf,IAAI,WAAW;YACb,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACpC,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACzC,IAAI,UAAU,QAAQ,CAAC,SAAS,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACrE,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACzC,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACzC,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACzC,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;iBACzC,IAAI,UAAU,QAAQ,CAAC,OAAO,WAAW;QAChD;QAEA,gBAAgB;QAChB,IAAI,iBAAiB,aAAa,QAAQ;YACxC,IAAI,cAAc,QAAQ,CAAC,SAAS,cAAc,QAAQ,CAAC,QAAQ;gBACjE,WAAW;YACb,OAAO,IAAI,cAAc,QAAQ,CAAC,SAAS,cAAc,QAAQ,CAAC,OAAO;gBACvE,WAAW;YACb,OAAO,IAAI,cAAc,QAAQ,CAAC,QAAQ;gBACxC,WAAW;YACb,OAAO,IAAI,cAAc,QAAQ,CAAC,SAAS,cAAc,QAAQ,CAAC,OAAO;gBACvE,WAAW;YACb;QACF;QAEA,OAAO;YACL,YAAY;YACZ,MAAM,KAAK,WAAW;YACtB;YACA,aAAa,YAAY,IAAI;YAC7B;YACA,UAAU;YACV,OAAO,CAAC,IAAI,EAAE,iBAAiB,MAAM,MAAM,EAAE,eAAe,MAAM;YAClE,SAAS;QACX;IACF;IAEQ,UAAU,QAAgC,EAAE,aAAuB,EAAsB;QAC/F,KAAK,MAAM,QAAQ,cAAe;YAChC,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,UAAW;gBACvC,IAAI,IAAI,QAAQ,CAAC,OAAO;oBACtB,OAAO,QAAQ,CAAC,IAAI;gBACtB;YACF;QACF;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/parsers/factory.ts"], "sourcesContent": ["// 账单解析器工厂\n\nimport { CardType } from '@prisma/client'\nimport { BillParser } from './types'\nimport { WechatParser } from './wechat'\nimport { AlipayParser } from './alipay'\nimport { CmbDebitParser } from './cmb-debit'\nimport { CmbCreditParser } from './cmb-credit'\n\n// 注册所有解析器\nconst PARSERS: BillParser[] = [\n  new WechatParser(),\n  new AlipayParser(),\n  new CmbDebitParser(),\n  new CmbCreditParser(),\n]\n\nexport class ParserFactory {\n  /**\n   * 获取支持指定卡片类型和文件的解析器\n   */\n  static getParser(fileName: string, cardType: CardType): BillParser | null {\n    for (const parser of PARSERS) {\n      if (parser.canParse(fileName, cardType)) {\n        return parser\n      }\n    }\n    return null\n  }\n\n  /**\n   * 获取支持指定卡片类型的所有解析器\n   */\n  static getParsersForCardType(cardType: CardType): BillParser[] {\n    return PARSERS.filter(parser => \n      parser.supportedCardTypes.includes(cardType)\n    )\n  }\n\n  /**\n   * 获取所有注册的解析器\n   */\n  static getAllParsers(): BillParser[] {\n    return [...PARSERS]\n  }\n\n  /**\n   * 获取支持的文件类型\n   */\n  static getSupportedFileTypes(): string[] {\n    const types = new Set<string>()\n    PARSERS.forEach(parser => {\n      parser.supportedFileTypes.forEach(type => types.add(type))\n    })\n    return Array.from(types)\n  }\n}\n"], "names": [], "mappings": "AAAA,UAAU;;;;AAIV;AACA;AACA;AACA;;;;;AAEA,UAAU;AACV,MAAM,UAAwB;IAC5B,IAAI,iIAAA,CAAA,eAAY;IAChB,IAAI,iIAAA,CAAA,eAAY;IAChB,IAAI,uIAAA,CAAA,iBAAc;IAClB,IAAI,wIAAA,CAAA,kBAAe;CACpB;AAEM,MAAM;IACX;;GAEC,GACD,OAAO,UAAU,QAAgB,EAAE,QAAkB,EAAqB;QACxE,KAAK,MAAM,UAAU,QAAS;YAC5B,IAAI,OAAO,QAAQ,CAAC,UAAU,WAAW;gBACvC,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,sBAAsB,QAAkB,EAAgB;QAC7D,OAAO,QAAQ,MAAM,CAAC,CAAA,SACpB,OAAO,kBAAkB,CAAC,QAAQ,CAAC;IAEvC;IAEA;;GAEC,GACD,OAAO,gBAA8B;QACnC,OAAO;eAAI;SAAQ;IACrB;IAEA;;GAEC,GACD,OAAO,wBAAkC;QACvC,MAAM,QAAQ,IAAI;QAClB,QAAQ,OAAO,CAAC,CAAA;YACd,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAA,OAAQ,MAAM,GAAG,CAAC;QACtD;QACA,OAAO,MAAM,IAAI,CAAC;IACpB;AACF", "debugId": null}}, {"offset": {"line": 1157, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/services/bill-import-service.ts"], "sourcesContent": ["import { prisma } from '@/lib/prisma'\nimport { ParserFactory } from '@/lib/parsers/factory'\nimport { BillImportStatus, TransactionStatus } from '@prisma/client'\nimport { StandardTransaction } from '@/lib/parsers/types'\n\nexport interface ImportBillOptions {\n  cardId: string\n  userId: string\n  file: Buffer\n  fileName: string\n  fileSize: number\n}\n\nexport interface ImportResult {\n  billImportId: string\n  success: boolean\n  totalCount: number\n  successCount: number\n  errorCount: number\n  duplicateCount: number\n  errors: string[]\n}\n\nexport class BillImportService {\n  async importBill(options: ImportBillOptions): Promise<ImportResult> {\n    const { cardId, userId, file, fileName, fileSize } = options\n\n    // 创建账单导入记录\n    const billImport = await prisma.billImport.create({\n      data: {\n        fileName,\n        fileType: this.getFileType(fileName),\n        fileSize,\n        status: BillImportStatus.PROCESSING,\n        cardId,\n      },\n    })\n\n    try {\n      // 获取卡片信息\n      const card = await prisma.card.findUnique({\n        where: { id: cardId },\n      })\n\n      if (!card) {\n        await this.updateBillImportStatus(billImport.id, BillImportStatus.FAILED, {\n          errorLog: '卡片不存在',\n        })\n        return {\n          billImportId: billImport.id,\n          success: false,\n          totalCount: 0,\n          successCount: 0,\n          errorCount: 1,\n          duplicateCount: 0,\n          errors: ['卡片不存在'],\n        }\n      }\n\n      // 获取合适的解析器\n      const parser = ParserFactory.getParser(fileName, card.type)\n      if (!parser) {\n        await this.updateBillImportStatus(billImport.id, BillImportStatus.FAILED, {\n          errorLog: '未找到合适的解析器',\n        })\n        return {\n          billImportId: billImport.id,\n          success: false,\n          totalCount: 0,\n          successCount: 0,\n          errorCount: 1,\n          duplicateCount: 0,\n          errors: ['未找到合适的解析器'],\n        }\n      }\n\n      // 解析文件\n      const parseResult = await parser.parse(file, card.type)\n      \n      if (!parseResult.success && parseResult.transactions.length === 0) {\n        await this.updateBillImportStatus(billImport.id, BillImportStatus.FAILED, {\n          errorLog: parseResult.errors.join('\\n'),\n        })\n        return {\n          billImportId: billImport.id,\n          success: false,\n          totalCount: parseResult.summary.totalCount,\n          successCount: 0,\n          errorCount: parseResult.summary.errorCount,\n          duplicateCount: 0,\n          errors: parseResult.errors,\n        }\n      }\n\n      // 导入交易记录\n      const importResult = await this.importTransactions(\n        parseResult.transactions,\n        billImport.id,\n        cardId,\n        userId\n      )\n\n      // 更新账单导入状态\n      await this.updateBillImportStatus(billImport.id, BillImportStatus.COMPLETED, {\n        totalCount: parseResult.summary.totalCount,\n        successCount: importResult.successCount,\n        errorCount: parseResult.summary.errorCount + importResult.errorCount,\n        errorLog: [...parseResult.errors, ...importResult.errors].join('\\n'),\n      })\n\n      return {\n        billImportId: billImport.id,\n        success: true,\n        totalCount: parseResult.summary.totalCount,\n        successCount: importResult.successCount,\n        errorCount: parseResult.summary.errorCount + importResult.errorCount,\n        duplicateCount: importResult.duplicateCount,\n        errors: [...parseResult.errors, ...importResult.errors],\n      }\n    } catch (error) {\n      await this.updateBillImportStatus(billImport.id, BillImportStatus.FAILED, {\n        errorLog: error instanceof Error ? error.message : '未知错误',\n      })\n      \n      return {\n        billImportId: billImport.id,\n        success: false,\n        totalCount: 0,\n        successCount: 0,\n        errorCount: 1,\n        duplicateCount: 0,\n        errors: [error instanceof Error ? error.message : '未知错误'],\n      }\n    }\n  }\n\n  private async importTransactions(\n    transactions: StandardTransaction[],\n    billImportId: string,\n    cardId: string,\n    userId: string\n  ): Promise<{\n    successCount: number\n    errorCount: number\n    duplicateCount: number\n    errors: string[]\n  }> {\n    let successCount = 0\n    let errorCount = 0\n    let duplicateCount = 0\n    const errors: string[] = []\n\n    for (const transaction of transactions) {\n      try {\n        // 检查是否已存在相同的交易（基于外部ID和卡片ID）\n        if (transaction.externalId) {\n          const existing = await prisma.transaction.findFirst({\n            where: {\n              externalId: transaction.externalId,\n              cardId,\n            },\n          })\n\n          if (existing) {\n            duplicateCount++\n            continue\n          }\n        }\n\n        // 创建交易记录\n        await prisma.transaction.create({\n          data: {\n            externalId: transaction.externalId,\n            type: transaction.type,\n            status: TransactionStatus.CONFIRMED,\n            amount: transaction.amount,\n            description: transaction.description,\n            category: transaction.category,\n            subcategory: transaction.subcategory,\n            transactionDate: transaction.transactionDate,\n            location: transaction.location,\n            notes: transaction.notes,\n            userId,\n            cardId,\n            billImportId,\n          },\n        })\n\n        successCount++\n      } catch (error) {\n        errorCount++\n        errors.push(`交易导入失败: ${error instanceof Error ? error.message : '未知错误'}`)\n      }\n    }\n\n    return { successCount, errorCount, duplicateCount, errors }\n  }\n\n  private async updateBillImportStatus(\n    billImportId: string,\n    status: BillImportStatus,\n    data: {\n      totalCount?: number\n      successCount?: number\n      errorCount?: number\n      errorLog?: string\n    }\n  ): Promise<void> {\n    await prisma.billImport.update({\n      where: { id: billImportId },\n      data: {\n        status,\n        ...data,\n        updatedAt: new Date(),\n      },\n    })\n  }\n\n  private getFileType(fileName: string): string {\n    const extension = fileName.toLowerCase().split('.').pop()\n    return extension || 'unknown'\n  }\n\n  // 获取支持的文件类型\n  getSupportedFileTypes(): string[] {\n    return ParserFactory.getSupportedFileTypes()\n  }\n\n  // 获取解析器信息\n  getParserInfo() {\n    return ParserFactory.getAllParsers().map(parser => ({\n      name: parser.name,\n      supportedCardTypes: parser.supportedCardTypes,\n      supportedFileTypes: parser.supportedFileTypes,\n    }))\n  }\n}\n\nexport const billImportService = new BillImportService()\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAqBO,MAAM;IACX,MAAM,WAAW,OAA0B,EAAyB;QAClE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;QAErD,WAAW;QACX,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YAChD,MAAM;gBACJ;gBACA,UAAU,IAAI,CAAC,WAAW,CAAC;gBAC3B;gBACA,QAAQ,6HAAA,CAAA,mBAAgB,CAAC,UAAU;gBACnC;YACF;QACF;QAEA,IAAI;YACF,SAAS;YACT,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,OAAO;oBAAE,IAAI;gBAAO;YACtB;YAEA,IAAI,CAAC,MAAM;gBACT,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,EAAE,6HAAA,CAAA,mBAAgB,CAAC,MAAM,EAAE;oBACxE,UAAU;gBACZ;gBACA,OAAO;oBACL,cAAc,WAAW,EAAE;oBAC3B,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;wBAAC;qBAAQ;gBACnB;YACF;YAEA,WAAW;YACX,MAAM,SAAS,kIAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,UAAU,KAAK,IAAI;YAC1D,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,EAAE,6HAAA,CAAA,mBAAgB,CAAC,MAAM,EAAE;oBACxE,UAAU;gBACZ;gBACA,OAAO;oBACL,cAAc,WAAW,EAAE;oBAC3B,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;wBAAC;qBAAY;gBACvB;YACF;YAEA,OAAO;YACP,MAAM,cAAc,MAAM,OAAO,KAAK,CAAC,MAAM,KAAK,IAAI;YAEtD,IAAI,CAAC,YAAY,OAAO,IAAI,YAAY,YAAY,CAAC,MAAM,KAAK,GAAG;gBACjE,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,EAAE,6HAAA,CAAA,mBAAgB,CAAC,MAAM,EAAE;oBACxE,UAAU,YAAY,MAAM,CAAC,IAAI,CAAC;gBACpC;gBACA,OAAO;oBACL,cAAc,WAAW,EAAE;oBAC3B,SAAS;oBACT,YAAY,YAAY,OAAO,CAAC,UAAU;oBAC1C,cAAc;oBACd,YAAY,YAAY,OAAO,CAAC,UAAU;oBAC1C,gBAAgB;oBAChB,QAAQ,YAAY,MAAM;gBAC5B;YACF;YAEA,SAAS;YACT,MAAM,eAAe,MAAM,IAAI,CAAC,kBAAkB,CAChD,YAAY,YAAY,EACxB,WAAW,EAAE,EACb,QACA;YAGF,WAAW;YACX,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,EAAE,6HAAA,CAAA,mBAAgB,CAAC,SAAS,EAAE;gBAC3E,YAAY,YAAY,OAAO,CAAC,UAAU;gBAC1C,cAAc,aAAa,YAAY;gBACvC,YAAY,YAAY,OAAO,CAAC,UAAU,GAAG,aAAa,UAAU;gBACpE,UAAU;uBAAI,YAAY,MAAM;uBAAK,aAAa,MAAM;iBAAC,CAAC,IAAI,CAAC;YACjE;YAEA,OAAO;gBACL,cAAc,WAAW,EAAE;gBAC3B,SAAS;gBACT,YAAY,YAAY,OAAO,CAAC,UAAU;gBAC1C,cAAc,aAAa,YAAY;gBACvC,YAAY,YAAY,OAAO,CAAC,UAAU,GAAG,aAAa,UAAU;gBACpE,gBAAgB,aAAa,cAAc;gBAC3C,QAAQ;uBAAI,YAAY,MAAM;uBAAK,aAAa,MAAM;iBAAC;YACzD;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,EAAE,6HAAA,CAAA,mBAAgB,CAAC,MAAM,EAAE;gBACxE,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACrD;YAEA,OAAO;gBACL,cAAc,WAAW,EAAE;gBAC3B,SAAS;gBACT,YAAY;gBACZ,cAAc;gBACd,YAAY;gBACZ,gBAAgB;gBAChB,QAAQ;oBAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;iBAAO;YAC3D;QACF;IACF;IAEA,MAAc,mBACZ,YAAmC,EACnC,YAAoB,EACpB,MAAc,EACd,MAAc,EAMb;QACD,IAAI,eAAe;QACnB,IAAI,aAAa;QACjB,IAAI,iBAAiB;QACrB,MAAM,SAAmB,EAAE;QAE3B,KAAK,MAAM,eAAe,aAAc;YACtC,IAAI;gBACF,4BAA4B;gBAC5B,IAAI,YAAY,UAAU,EAAE;oBAC1B,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,SAAS,CAAC;wBAClD,OAAO;4BACL,YAAY,YAAY,UAAU;4BAClC;wBACF;oBACF;oBAEA,IAAI,UAAU;wBACZ;wBACA;oBACF;gBACF;gBAEA,SAAS;gBACT,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;oBAC9B,MAAM;wBACJ,YAAY,YAAY,UAAU;wBAClC,MAAM,YAAY,IAAI;wBACtB,QAAQ,6HAAA,CAAA,oBAAiB,CAAC,SAAS;wBACnC,QAAQ,YAAY,MAAM;wBAC1B,aAAa,YAAY,WAAW;wBACpC,UAAU,YAAY,QAAQ;wBAC9B,aAAa,YAAY,WAAW;wBACpC,iBAAiB,YAAY,eAAe;wBAC5C,UAAU,YAAY,QAAQ;wBAC9B,OAAO,YAAY,KAAK;wBACxB;wBACA;wBACA;oBACF;gBACF;gBAEA;YACF,EAAE,OAAO,OAAO;gBACd;gBACA,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;YAC1E;QACF;QAEA,OAAO;YAAE;YAAc;YAAY;YAAgB;QAAO;IAC5D;IAEA,MAAc,uBACZ,YAAoB,EACpB,MAAwB,EACxB,IAKC,EACc;QACf,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YAC7B,OAAO;gBAAE,IAAI;YAAa;YAC1B,MAAM;gBACJ;gBACA,GAAG,IAAI;gBACP,WAAW,IAAI;YACjB;QACF;IACF;IAEQ,YAAY,QAAgB,EAAU;QAC5C,MAAM,YAAY,SAAS,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG;QACvD,OAAO,aAAa;IACtB;IAEA,YAAY;IACZ,wBAAkC;QAChC,OAAO,kIAAA,CAAA,gBAAa,CAAC,qBAAqB;IAC5C;IAEA,UAAU;IACV,gBAAgB;QACd,OAAO,kIAAA,CAAA,gBAAa,CAAC,aAAa,GAAG,GAAG,CAAC,CAAA,SAAU,CAAC;gBAClD,MAAM,OAAO,IAAI;gBACjB,oBAAoB,OAAO,kBAAkB;gBAC7C,oBAAoB,OAAO,kBAAkB;YAC/C,CAAC;IACH;AACF;AAEO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 1397, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { NextRequest } from 'next/server'\nimport { prisma } from './prisma'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'\nconst BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12')\n\nexport interface JWTPayload {\n  userId: string\n  email: string\n  familyId?: string\n  familyRole?: string\n}\n\nexport interface AuthUser {\n  id: string\n  email: string\n  name: string\n  avatar?: string\n  familyId?: string\n  familyRole?: string\n  family?: {\n    id: string\n    name: string\n    role: string\n  }\n}\n\n// 密码加密\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, BCRYPT_ROUNDS)\n}\n\n// 密码验证\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\n// 生成JWT令牌\nexport function generateToken(payload: JWTPayload): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })\n}\n\n// 验证JWT令牌\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload\n  } catch (error) {\n    return null\n  }\n}\n\n// 从请求中获取用户信息\nexport async function getUserFromRequest(request: NextRequest): Promise<AuthUser | null> {\n  try {\n    const authHeader = request.headers.get('authorization')\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return null\n    }\n\n    const token = authHeader.substring(7)\n    const payload = verifyToken(token)\n    if (!payload) {\n      return null\n    }\n\n    const user = await prisma.user.findUnique({\n      where: { id: payload.userId },\n      include: {\n        family: {\n          select: {\n            id: true,\n            name: true,\n          },\n        },\n      },\n    })\n\n    if (!user || !user.isActive) {\n      return null\n    }\n\n    return {\n      id: user.id,\n      email: user.email,\n      name: user.name,\n      avatar: user.avatar || undefined,\n      familyId: user.familyId || undefined,\n      familyRole: user.familyRole,\n      family: user.family ? {\n        id: user.family.id,\n        name: user.family.name,\n        role: user.familyRole,\n      } : undefined,\n    }\n  } catch (error) {\n    console.error('Error getting user from request:', error)\n    return null\n  }\n}\n\n// 中间件：验证用户认证\nexport function requireAuth(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(\n        JSON.stringify({ success: false, error: '未授权访问' }),\n        { status: 401, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n    return handler(request, user)\n  }\n}\n\n// 中间件：验证家庭成员权限\nexport function requireFamilyMember(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(\n        JSON.stringify({ success: false, error: '未授权访问' }),\n        { status: 401, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    if (!user.familyId) {\n      return new Response(\n        JSON.stringify({ success: false, error: '您还没有加入任何家庭' }),\n        { status: 403, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n\n// 中间件：验证家庭管理员权限\nexport function requireFamilyAdmin(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      return new Response(\n        JSON.stringify({ success: false, error: '未授权访问' }),\n        { status: 401, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    if (!user.familyId) {\n      return new Response(\n        JSON.stringify({ success: false, error: '您还没有加入任何家庭' }),\n        { status: 403, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    if (user.familyRole !== 'OWNER' && user.familyRole !== 'ADMIN') {\n      return new Response(\n        JSON.stringify({ success: false, error: '需要管理员权限' }),\n        { status: 403, headers: { 'Content-Type': 'application/json' } }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n\n// 验证邮箱格式\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// 验证密码强度\nexport function isValidPassword(password: string): { valid: boolean; message?: string } {\n  if (password.length < 8) {\n    return { valid: false, message: '密码长度至少8位' }\n  }\n  \n  if (!/(?=.*[a-z])/.test(password)) {\n    return { valid: false, message: '密码必须包含小写字母' }\n  }\n  \n  if (!/(?=.*[A-Z])/.test(password)) {\n    return { valid: false, message: '密码必须包含大写字母' }\n  }\n  \n  if (!/(?=.*\\d)/.test(password)) {\n    return { valid: false, message: '密码必须包含数字' }\n  }\n  \n  return { valid: true }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AAEA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AACrD,MAAM,gBAAgB,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;AAwBrD,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,cAAc,OAAmB;IAC/C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAe;AACnE;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe,mBAAmB,OAAoB;IAC3D,IAAI;QACF,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;QACnC,MAAM,UAAU,YAAY;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;YACF;QACF;QAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC3B,OAAO;QACT;QAEA,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,QAAQ,KAAK,MAAM,IAAI;YACvB,UAAU,KAAK,QAAQ,IAAI;YAC3B,YAAY,KAAK,UAAU;YAC3B,QAAQ,KAAK,MAAM,GAAG;gBACpB,IAAI,KAAK,MAAM,CAAC,EAAE;gBAClB,MAAM,KAAK,MAAM,CAAC,IAAI;gBACtB,MAAM,KAAK,UAAU;YACvB,IAAI;QACN;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAGO,SAAS,YAAY,OAAoE;IAC9F,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,mBAAmB;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAChD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QACA,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,SAAS,oBAAoB,OAAoE;IACtG,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,mBAAmB;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAChD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAa,IACrD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,SAAS,mBAAmB,OAAoE;IACrG,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,mBAAmB;QACtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAQ,IAChD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAa,IACrD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,KAAK,SAAS;YAC9D,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAU,IAClD;gBAAE,QAAQ;gBAAK,SAAS;oBAAE,gBAAgB;gBAAmB;YAAE;QAEnE;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,gBAAgB,QAAgB;IAC9C,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO;YAAE,OAAO;YAAO,SAAS;QAAW;IAC7C;IAEA,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW;QACjC,OAAO;YAAE,OAAO;YAAO,SAAS;QAAa;IAC/C;IAEA,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW;QACjC,OAAO;YAAE,OAAO;YAAO,SAAS;QAAa;IAC/C;IAEA,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW;QAC9B,OAAO;YAAE,OAAO;YAAO,SAAS;QAAW;IAC7C;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB", "debugId": null}}, {"offset": {"line": 1604, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/api/bills/import/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { createSuccessResponse, handleApiError, ApiError } from '@/lib/api-response'\nimport { billImportService } from '@/lib/services/bill-import-service'\nimport { getUserFromRequest } from '@/lib/auth'\n\n// POST /api/bills/import - 导入账单文件\nexport async function POST(request: NextRequest) {\n  try {\n    // 验证用户认证\n    const user = await getUserFromRequest(request)\n    if (!user) {\n      throw new ApiError('未授权访问', 401)\n    }\n\n    const formData = await request.formData()\n\n    const file = formData.get('file') as File\n    const cardId = formData.get('cardId') as string\n\n    if (!file) {\n      throw new ApiError('请选择要上传的文件', 400)\n    }\n\n    if (!cardId) {\n      throw new ApiError('请指定卡片ID', 400)\n    }\n\n    // 验证文件大小（10MB限制）\n    const maxSize = 10 * 1024 * 1024 // 10MB\n    if (file.size > maxSize) {\n      throw new ApiError('文件大小不能超过10MB', 400)\n    }\n\n    // 验证文件类型\n    const supportedTypes = billImportService.getSupportedFileTypes()\n    const fileExtension = file.name.toLowerCase().split('.').pop()\n    \n    if (!fileExtension || !supportedTypes.includes(fileExtension)) {\n      throw new ApiError(\n        `不支持的文件类型。支持的类型: ${supportedTypes.join(', ')}`,\n        400\n      )\n    }\n\n    // 读取文件内容\n    const buffer = Buffer.from(await file.arrayBuffer())\n\n    // 导入账单\n    const result = await billImportService.importBill({\n      cardId,\n      userId: user.userId,\n      file: buffer,\n      fileName: file.name,\n      fileSize: file.size,\n    })\n\n    return createSuccessResponse(result, '账单导入完成')\n  } catch (error) {\n    return handleApiError(error)\n  }\n}\n\n// GET /api/bills/import - 获取支持的解析器信息\nexport async function GET() {\n  try {\n    const info = {\n      supportedFileTypes: billImportService.getSupportedFileTypes(),\n      parsers: billImportService.getParserInfo(),\n      maxFileSize: '10MB',\n    }\n\n    return createSuccessResponse(info)\n  } catch (error) {\n    return handleApiError(error)\n  }\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,SAAS;QACT,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE;QACtC,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,+HAAA,CAAA,WAAQ,CAAC,SAAS;QAC9B;QAEA,MAAM,WAAW,MAAM,QAAQ,QAAQ;QAEvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAC1B,MAAM,SAAS,SAAS,GAAG,CAAC;QAE5B,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,+HAAA,CAAA,WAAQ,CAAC,aAAa;QAClC;QAEA,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,+HAAA,CAAA,WAAQ,CAAC,WAAW;QAChC;QAEA,iBAAiB;QACjB,MAAM,UAAU,KAAK,OAAO,KAAK,OAAO;;QACxC,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,MAAM,IAAI,+HAAA,CAAA,WAAQ,CAAC,gBAAgB;QACrC;QAEA,SAAS;QACT,MAAM,iBAAiB,qJAAA,CAAA,oBAAiB,CAAC,qBAAqB;QAC9D,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG;QAE5D,IAAI,CAAC,iBAAiB,CAAC,eAAe,QAAQ,CAAC,gBAAgB;YAC7D,MAAM,IAAI,+HAAA,CAAA,WAAQ,CAChB,CAAC,gBAAgB,EAAE,eAAe,IAAI,CAAC,OAAO,EAC9C;QAEJ;QAEA,SAAS;QACT,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;QAEjD,OAAO;QACP,MAAM,SAAS,MAAM,qJAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC;YAChD;YACA,QAAQ,KAAK,MAAM;YACnB,MAAM;YACN,UAAU,KAAK,IAAI;YACnB,UAAU,KAAK,IAAI;QACrB;QAEA,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ;IACvC,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;IACxB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO;YACX,oBAAoB,qJAAA,CAAA,oBAAiB,CAAC,qBAAqB;YAC3D,SAAS,qJAAA,CAAA,oBAAiB,CAAC,aAAa;YACxC,aAAa;QACf;QAEA,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE;IAC/B,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;IACxB;AACF", "debugId": null}}]}