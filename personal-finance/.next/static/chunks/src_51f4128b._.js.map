{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency = 'CNY'): string {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n  }).format(amount)\n}\n\nexport function formatDate(date: Date | string, format: 'short' | 'long' | 'time' = 'short'): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  \n  switch (format) {\n    case 'short':\n      return d.toLocaleDateString('zh-CN')\n    case 'long':\n      return d.toLocaleDateString('zh-CN', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        weekday: 'long',\n      })\n    case 'time':\n      return d.toLocaleString('zh-CN')\n    default:\n      return d.toLocaleDateString('zh-CN')\n  }\n}\n\nexport function formatRelativeTime(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) {\n    return '刚刚'\n  } else if (diffInSeconds < 3600) {\n    return `${Math.floor(diffInSeconds / 60)}分钟前`\n  } else if (diffInSeconds < 86400) {\n    return `${Math.floor(diffInSeconds / 3600)}小时前`\n  } else if (diffInSeconds < 2592000) {\n    return `${Math.floor(diffInSeconds / 86400)}天前`\n  } else {\n    return formatDate(d)\n  }\n}\n\nexport function getTransactionTypeLabel(type: string): string {\n  const labels: Record<string, string> = {\n    INCOME: '收入',\n    EXPENSE: '支出',\n    TRANSFER: '转账',\n  }\n  return labels[type] || type\n}\n\nexport function getCardTypeLabel(type: string): string {\n  const labels: Record<string, string> = {\n    BANK_CARD: '银行卡',\n    CREDIT_CARD: '信用卡',\n    WECHAT: '微信',\n    ALIPAY: '支付宝',\n    CASH: '现金',\n    OTHER: '其他',\n  }\n  return labels[type] || type\n}\n\nexport function getTransactionStatusLabel(status: string): string {\n  const labels: Record<string, string> = {\n    PENDING: '待确认',\n    CONFIRMED: '已确认',\n    CANCELLED: '已取消',\n    LINKED: '已关联',\n  }\n  return labels[status] || status\n}\n\nexport function getStatusColor(status: string): string {\n  const colors: Record<string, string> = {\n    PENDING: 'bg-yellow-100 text-yellow-800',\n    CONFIRMED: 'bg-green-100 text-green-800',\n    CANCELLED: 'bg-red-100 text-red-800',\n    LINKED: 'bg-blue-100 text-blue-800',\n  }\n  return colors[status] || 'bg-gray-100 text-gray-800'\n}\n\nexport function getTypeColor(type: string): string {\n  const colors: Record<string, string> = {\n    INCOME: 'text-green-600',\n    EXPENSE: 'text-red-600',\n    TRANSFER: 'text-blue-600',\n  }\n  return colors[type] || 'text-gray-600'\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) {\n      clearTimeout(timeout)\n    }\n    \n    timeout = setTimeout(() => {\n      func(...args)\n    }, wait)\n  }\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB,EAAE,SAAoC,OAAO;IACzF,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAEtD,OAAQ;QACN,KAAK;YACH,OAAO,EAAE,kBAAkB,CAAC;QAC9B,KAAK;YACH,OAAO,EAAE,kBAAkB,CAAC,SAAS;gBACnC,MAAM;gBACN,OAAO;gBACP,KAAK;gBACL,SAAS;YACX;QACF,KAAK;YACH,OAAO,EAAE,cAAc,CAAC;QAC1B;YACE,OAAO,EAAE,kBAAkB,CAAC;IAChC;AACF;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,GAAG,CAAC;IAC/C,OAAO,IAAI,gBAAgB,OAAO;QAChC,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,GAAG,CAAC;IACjD,OAAO,IAAI,gBAAgB,SAAS;QAClC,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,EAAE,CAAC;IACjD,OAAO;QACL,OAAO,WAAW;IACpB;AACF;AAEO,SAAS,wBAAwB,IAAY;IAClD,MAAM,SAAiC;QACrC,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,SAAiC;QACrC,WAAW;QACX,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,OAAO;IACT;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,0BAA0B,MAAc;IACtD,MAAM,SAAiC;QACrC,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,SAAiC;QACrC,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAEO,SAAS,aAAa,IAAY;IACvC,MAAM,SAAiC;QACrC,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS;YACX,aAAa;QACf;QAEA,UAAU,WAAW;YACnB,QAAQ;QACV,GAAG;IACL;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n  children: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading = false, disabled, children, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    }\n    \n    const sizes = {\n      sm: 'h-8 px-3 text-sm',\n      md: 'h-10 px-4 text-sm',\n      lg: 'h-12 px-6 text-base',\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC/F,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/family/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/Button'\nimport { formatDate, formatRelativeTime } from '@/lib/utils'\n\ninterface Family {\n  id: string\n  name: string\n  description?: string\n  avatar?: string\n  createdAt: string\n  creator: {\n    id: string\n    name: string\n    email: string\n    avatar?: string\n  }\n  members: Array<{\n    id: string\n    name: string\n    email: string\n    avatar?: string\n    familyRole: string\n    createdAt: string\n  }>\n  invitations: Array<{\n    id: string\n    email: string\n    role: string\n    status: string\n    message?: string\n    expiresAt: string\n    createdAt: string\n    sender: {\n      id: string\n      name: string\n      email: string\n    }\n  }>\n  _count: {\n    members: number\n  }\n}\n\nexport default function FamilyPage() {\n  const [family, setFamily] = useState<Family | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [showCreateForm, setShowCreateForm] = useState(false)\n  const [showInviteForm, setShowInviteForm] = useState(false)\n  const [user, setUser] = useState<any>(null)\n\n  useEffect(() => {\n    // 获取当前用户信息\n    const userData = localStorage.getItem('user')\n    if (userData) {\n      setUser(JSON.parse(userData))\n    }\n    \n    fetchFamily()\n  }, [])\n\n  const fetchFamily = async () => {\n    try {\n      const token = localStorage.getItem('token')\n      if (!token) {\n        setLoading(false)\n        return\n      }\n\n      const response = await fetch('/api/families', {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n        },\n      })\n\n      const data = await response.json()\n      \n      if (data.success && data.data) {\n        setFamily(data.data)\n      }\n    } catch (error) {\n      console.error('Failed to fetch family:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const getRoleLabel = (role: string) => {\n    const labels: Record<string, string> = {\n      OWNER: '所有者',\n      ADMIN: '管理员',\n      MEMBER: '成员',\n    }\n    return labels[role] || role\n  }\n\n  const getStatusLabel = (status: string) => {\n    const labels: Record<string, string> = {\n      PENDING: '待处理',\n      ACCEPTED: '已接受',\n      DECLINED: '已拒绝',\n      EXPIRED: '已过期',\n    }\n    return labels[status] || status\n  }\n\n  const getStatusColor = (status: string) => {\n    const colors: Record<string, string> = {\n      PENDING: 'bg-yellow-100 text-yellow-800',\n      ACCEPTED: 'bg-green-100 text-green-800',\n      DECLINED: 'bg-red-100 text-red-800',\n      EXPIRED: 'bg-gray-100 text-gray-800',\n    }\n    return colors[status] || 'bg-gray-100 text-gray-800'\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!family) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <div className=\"bg-white shadow\">\n          <div className=\"container mx-auto px-4 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">家庭管理</h1>\n              <Link\n                href=\"/dashboard\"\n                className=\"text-blue-600 hover:text-blue-800 transition-colors\"\n              >\n                返回仪表板\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"container mx-auto px-4 py-8\">\n          <div className=\"max-w-2xl mx-auto text-center\">\n            <svg\n              className=\"mx-auto h-24 w-24 text-gray-400\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n              />\n            </svg>\n            <h3 className=\"mt-4 text-lg font-medium text-gray-900\">您还没有加入任何家庭</h3>\n            <p className=\"mt-2 text-gray-500\">创建一个新家庭或等待其他人邀请您加入</p>\n            <div className=\"mt-6\">\n              <Button onClick={() => setShowCreateForm(true)}>\n                创建家庭\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* 创建家庭表单 */}\n        {showCreateForm && (\n          <CreateFamilyForm\n            onClose={() => setShowCreateForm(false)}\n            onSuccess={() => {\n              setShowCreateForm(false)\n              fetchFamily()\n            }}\n          />\n        )}\n      </div>\n    )\n  }\n\n  const canManageFamily = user && (user.familyRole === 'OWNER' || user.familyRole === 'ADMIN')\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"bg-white shadow\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">家庭管理</h1>\n            <div className=\"flex space-x-4\">\n              {canManageFamily && (\n                <Button\n                  variant=\"outline\"\n                  onClick={() => setShowInviteForm(true)}\n                >\n                  邀请成员\n                </Button>\n              )}\n              <Link\n                href=\"/dashboard\"\n                className=\"text-blue-600 hover:text-blue-800 transition-colors\"\n              >\n                返回仪表板\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* 家庭信息 */}\n        <div className=\"bg-white rounded-lg shadow mb-8\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h2 className=\"text-lg font-medium text-gray-900\">家庭信息</h2>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"flex items-start space-x-4\">\n              {family.avatar ? (\n                <img\n                  src={family.avatar}\n                  alt={family.name}\n                  className=\"w-16 h-16 rounded-full\"\n                />\n              ) : (\n                <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-8 h-8 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n              )}\n              <div className=\"flex-1\">\n                <h3 className=\"text-xl font-semibold text-gray-900\">{family.name}</h3>\n                {family.description && (\n                  <p className=\"text-gray-600 mt-1\">{family.description}</p>\n                )}\n                <div className=\"mt-2 text-sm text-gray-500\">\n                  <p>创建者: {family.creator.name}</p>\n                  <p>创建时间: {formatDate(family.createdAt, 'long')}</p>\n                  <p>成员数量: {family._count.members}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 家庭成员 */}\n        <div className=\"bg-white rounded-lg shadow mb-8\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h2 className=\"text-lg font-medium text-gray-900\">家庭成员</h2>\n          </div>\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    成员\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    角色\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    加入时间\n                  </th>\n                  <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    操作\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {family.members.map((member) => (\n                  <tr key={member.id}>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        {member.avatar ? (\n                          <img\n                            src={member.avatar}\n                            alt={member.name}\n                            className=\"w-8 h-8 rounded-full\"\n                          />\n                        ) : (\n                          <div className=\"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center\">\n                            <span className=\"text-xs text-gray-600\">\n                              {member.name.charAt(0)}\n                            </span>\n                          </div>\n                        )}\n                        <div className=\"ml-3\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {member.name}\n                          </div>\n                          <div className=\"text-sm text-gray-500\">\n                            {member.email}\n                          </div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                        member.familyRole === 'OWNER' \n                          ? 'bg-purple-100 text-purple-800'\n                          : member.familyRole === 'ADMIN'\n                          ? 'bg-blue-100 text-blue-800'\n                          : 'bg-green-100 text-green-800'\n                      }`}>\n                        {getRoleLabel(member.familyRole)}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {formatRelativeTime(member.createdAt)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                      {canManageFamily && member.id !== user?.id && (\n                        <Button variant=\"ghost\" size=\"sm\">\n                          管理\n                        </Button>\n                      )}\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* 待处理邀请 */}\n        {family.invitations.length > 0 && (\n          <div className=\"bg-white rounded-lg shadow\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h2 className=\"text-lg font-medium text-gray-900\">待处理邀请</h2>\n            </div>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      邮箱\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      角色\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      状态\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      邀请时间\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      过期时间\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {family.invitations.map((invitation) => (\n                    <tr key={invitation.id}>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {invitation.email}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\">\n                          {getRoleLabel(invitation.role)}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(invitation.status)}`}>\n                          {getStatusLabel(invitation.status)}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {formatRelativeTime(invitation.createdAt)}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {formatDate(invitation.expiresAt)}\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 邀请成员表单 */}\n      {showInviteForm && (\n        <InviteMemberForm\n          onClose={() => setShowInviteForm(false)}\n          onSuccess={() => {\n            setShowInviteForm(false)\n            fetchFamily()\n          }}\n        />\n      )}\n    </div>\n  )\n}\n\n// 创建家庭表单组件\nfunction CreateFamilyForm({ onClose, onSuccess }: { onClose: () => void; onSuccess: () => void }) {\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      const token = localStorage.getItem('token')\n      const response = await fetch('/api/families', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`,\n        },\n        body: JSON.stringify(formData),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        onSuccess()\n      } else {\n        setError(data.error || '创建失败')\n      }\n    } catch (error) {\n      console.error('Create family error:', error)\n      setError('网络错误，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n        <div className=\"mt-3\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">创建家庭</h3>\n          \n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4\">\n              {error}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                家庭名称\n              </label>\n              <input\n                type=\"text\"\n                required\n                value={formData.name}\n                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"请输入家庭名称\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                家庭描述（可选）\n              </label>\n              <textarea\n                value={formData.description}\n                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                rows={3}\n                placeholder=\"请输入家庭描述\"\n              />\n            </div>\n\n            <div className=\"flex justify-end space-x-2 pt-4\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={onClose}\n              >\n                取消\n              </Button>\n              <Button\n                type=\"submit\"\n                loading={loading}\n              >\n                创建\n              </Button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// 邀请成员表单组件\nfunction InviteMemberForm({ onClose, onSuccess }: { onClose: () => void; onSuccess: () => void }) {\n  const [formData, setFormData] = useState({\n    email: '',\n    role: 'MEMBER',\n    message: '',\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      const token = localStorage.getItem('token')\n      const response = await fetch('/api/families/invite', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`,\n        },\n        body: JSON.stringify(formData),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        onSuccess()\n      } else {\n        setError(data.error || '邀请失败')\n      }\n    } catch (error) {\n      console.error('Invite member error:', error)\n      setError('网络错误，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n        <div className=\"mt-3\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">邀请家庭成员</h3>\n          \n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4\">\n              {error}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                邮箱地址\n              </label>\n              <input\n                type=\"email\"\n                required\n                value={formData.email}\n                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"请输入被邀请人的邮箱\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                角色\n              </label>\n              <select\n                value={formData.role}\n                onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"MEMBER\">成员</option>\n                <option value=\"ADMIN\">管理员</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                邀请消息（可选）\n              </label>\n              <textarea\n                value={formData.message}\n                onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                rows={3}\n                placeholder=\"请输入邀请消息\"\n              />\n            </div>\n\n            <div className=\"flex justify-end space-x-2 pt-4\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={onClose}\n              >\n                取消\n              </Button>\n              <Button\n                type=\"submit\"\n                loading={loading}\n              >\n                发送邀请\n              </Button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AA8Ce,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAEtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,WAAW;YACX,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,IAAI,UAAU;gBACZ,QAAQ,KAAK,KAAK,CAAC;YACrB;YAEA;QACF;+BAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO;gBACV,WAAW;gBACX;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;gBAC7B,UAAU,KAAK,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAiC;YACrC,OAAO;YACP,OAAO;YACP,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAiC;YACrC,SAAS;YACT,UAAU;YACV,UAAU;YACV,SAAS;QACX;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAiC;YACrC,SAAS;YACT,UAAU;YACV,UAAU;YACV,SAAS;QACX;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;8BAOP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,SAAQ;gCACR,QAAO;0CAEP,cAAA,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;0CAGN,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,kBAAkB;8CAAO;;;;;;;;;;;;;;;;;;;;;;gBAQrD,gCACC,6LAAC;oBACC,SAAS,IAAM,kBAAkB;oBACjC,WAAW;wBACT,kBAAkB;wBAClB;oBACF;;;;;;;;;;;;IAKV;IAEA,MAAM,kBAAkB,QAAQ,CAAC,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,KAAK,OAAO;IAE3F,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAI,WAAU;;oCACZ,iCACC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,kBAAkB;kDAClC;;;;;;kDAIH,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQT,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;0CAEpD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;wCACZ,OAAO,MAAM,iBACZ,6LAAC;4CACC,KAAK,OAAO,MAAM;4CAClB,KAAK,OAAO,IAAI;4CAChB,WAAU;;;;;iEAGZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAId,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAuC,OAAO,IAAI;;;;;;gDAC/D,OAAO,WAAW,kBACjB,6LAAC;oDAAE,WAAU;8DAAsB,OAAO,WAAW;;;;;;8DAEvD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;gEAAE;gEAAM,OAAO,OAAO,CAAC,IAAI;;;;;;;sEAC5B,6LAAC;;gEAAE;gEAAO,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS,EAAE;;;;;;;sEACvC,6LAAC;;gEAAE;gEAAO,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;0CAEpD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;;;;;;;;;;;;sDAKpG,6LAAC;4CAAM,WAAU;sDACd,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,uBACnB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;oEACZ,OAAO,MAAM,iBACZ,6LAAC;wEACC,KAAK,OAAO,MAAM;wEAClB,KAAK,OAAO,IAAI;wEAChB,WAAU;;;;;6FAGZ,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;sFACb,OAAO,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;kFAI1B,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FACZ,OAAO,IAAI;;;;;;0FAEd,6LAAC;gFAAI,WAAU;0FACZ,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;sEAKrB,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,yDAAyD,EACzE,OAAO,UAAU,KAAK,UAClB,kCACA,OAAO,UAAU,KAAK,UACtB,8BACA,+BACJ;0EACC,aAAa,OAAO,UAAU;;;;;;;;;;;sEAGnC,6LAAC;4DAAG,WAAU;sEACX,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,SAAS;;;;;;sEAEtC,6LAAC;4DAAG,WAAU;sEACX,mBAAmB,OAAO,EAAE,KAAK,MAAM,oBACtC,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;0EAAK;;;;;;;;;;;;mDA1C/B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAuD3B,OAAO,WAAW,CAAC,MAAM,GAAG,mBAC3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;0CAEpD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;;;;;;;;;;;;sDAKnG,6LAAC;4CAAM,WAAU;sDACd,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,2BACvB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,WAAW,KAAK;;;;;;sEAEnB,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAU;0EACb,aAAa,WAAW,IAAI;;;;;;;;;;;sEAGjC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,WAAW,MAAM,GAAG;0EAC7G,eAAe,WAAW,MAAM;;;;;;;;;;;sEAGrC,6LAAC;4DAAG,WAAU;sEACX,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,SAAS;;;;;;sEAE1C,6LAAC;4DAAG,WAAU;sEACX,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,SAAS;;;;;;;mDAlB3B,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YA8BnC,gCACC,6LAAC;gBACC,SAAS,IAAM,kBAAkB;gBACjC,WAAW;oBACT,kBAAkB;oBAClB;gBACF;;;;;;;;;;;;AAKV;GAhWwB;KAAA;AAkWxB,WAAW;AACX,SAAS,iBAAiB,EAAE,OAAO,EAAE,SAAS,EAAkD;;IAC9F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;IACf;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB;YACF,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;oBAEtD,uBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIL,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,QAAQ;wCACR,OAAO,SAAS,IAAI;wCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACvE,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC9E,WAAU;wCACV,MAAM;wCACN,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS;kDACV;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAS;kDACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;IAnGS;MAAA;AAqGT,WAAW;AACX,SAAS,iBAAiB,EAAE,OAAO,EAAE,SAAS,EAAkD;;IAC9F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,MAAM;QACN,SAAS;IACX;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB;YACF,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;oBAEtD,uBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIL,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,QAAQ;wCACR,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACxE,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,OAAO,SAAS,IAAI;wCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACvE,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;;;;;;;;;;;;;0CAI1B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,OAAO,SAAS,OAAO;wCACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC1E,WAAU;wCACV,MAAM;wCACN,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS;kDACV;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAS;kDACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;IAlHS;MAAA", "debugId": null}}]}