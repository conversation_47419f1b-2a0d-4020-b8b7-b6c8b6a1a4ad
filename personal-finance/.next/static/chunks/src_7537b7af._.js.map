{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { useRouter } from 'next/navigation'\n\ninterface User {\n  id: string\n  email: string\n  name: string\n  avatar?: string\n  familyId?: string\n  familyRole?: string\n  family?: {\n    id: string\n    name: string\n    role: string\n  }\n}\n\ninterface AuthState {\n  user: User | null\n  token: string | null\n  loading: boolean\n  isAuthenticated: boolean\n}\n\nexport function useAuth() {\n  const router = useRouter()\n  const [authState, setAuthState] = useState<AuthState>({\n    user: null,\n    token: null,\n    loading: true,\n    isAuthenticated: false,\n  })\n\n  // 定义logout函数（需要在useEffect之前定义以避免循环依赖）\n  const logout = useCallback(() => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('token')\n      localStorage.removeItem('user')\n    }\n\n    setAuthState({\n      user: null,\n      token: null,\n      loading: false,\n      isAuthenticated: false,\n    })\n\n    router.push('/auth/login')\n  }, [router])\n\n  useEffect(() => {\n    // 确保只在客户端执行\n    if (typeof window === 'undefined') {\n      setAuthState(prev => ({ ...prev, loading: false }))\n      return\n    }\n\n    // 从localStorage获取认证信息\n    const token = localStorage.getItem('token')\n    const userStr = localStorage.getItem('user')\n\n    if (token && userStr) {\n      try {\n        const user = JSON.parse(userStr)\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n      } catch (error) {\n        console.error('Failed to parse user data:', error)\n        logout()\n      }\n    } else {\n      setAuthState(prev => ({ ...prev, loading: false }))\n    }\n  }, [logout])\n\n  const login = async (email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        const { user, token } = data.data\n\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', token)\n          localStorage.setItem('user', JSON.stringify(user))\n        }\n\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n\n        return { success: true }\n      } else {\n        return { success: false, error: data.error }\n      }\n    } catch (error) {\n      console.error('Login error:', error)\n      return { success: false, error: '网络错误，请重试' }\n    }\n  }\n\n  const register = async (name: string, email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ name, email, password }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        const { user, token } = data.data\n\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', token)\n          localStorage.setItem('user', JSON.stringify(user))\n        }\n\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n\n        return { success: true }\n      } else {\n        return { success: false, error: data.error }\n      }\n    } catch (error) {\n      console.error('Register error:', error)\n      return { success: false, error: '网络错误，请重试' }\n    }\n  }\n\n\n\n  const updateUser = (updatedUser: Partial<User>) => {\n    if (authState.user) {\n      const newUser = { ...authState.user, ...updatedUser }\n      if (typeof window !== 'undefined') {\n        localStorage.setItem('user', JSON.stringify(newUser))\n      }\n      setAuthState(prev => ({\n        ...prev,\n        user: newUser,\n      }))\n    }\n  }\n\n  const getAuthHeaders = useCallback(() => {\n    if (authState.token) {\n      return {\n        'Authorization': `Bearer ${authState.token}`,\n        'Content-Type': 'application/json',\n      }\n    }\n    return {\n      'Content-Type': 'application/json',\n    }\n  }, [authState.token])\n\n  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {\n    // 如果body是FormData，不要设置Content-Type，让浏览器自动设置\n    const isFormData = options.body instanceof FormData\n\n    const headers = {\n      ...(isFormData ? {} : getAuthHeaders()), // FormData时只设置Authorization\n      ...options.headers,\n    }\n\n    // 如果是FormData，只添加Authorization头\n    if (isFormData && authState.token) {\n      headers['Authorization'] = `Bearer ${authState.token}`\n    }\n\n    const response = await fetch(url, {\n      ...options,\n      headers,\n    })\n\n    if (response.status === 401) {\n      // Token过期或无效，自动登出\n      logout()\n      throw new Error('认证失败，请重新登录')\n    }\n\n    return response\n  }, [getAuthHeaders, logout])\n\n  return {\n    ...authState,\n    login,\n    register,\n    logout,\n    updateUser,\n    getAuthHeaders,\n    apiCall,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AA0BO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM;QACN,OAAO;QACP,SAAS;QACT,iBAAiB;IACnB;IAEA,sCAAsC;IACtC,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uCAAE;YACzB,wCAAmC;gBACjC,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;YAC1B;YAEA,aAAa;gBACX,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,iBAAiB;YACnB;YAEA,OAAO,IAAI,CAAC;QACd;sCAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,YAAY;YACZ,uCAAmC;;YAGnC;YAEA,sBAAsB;YACtB,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,UAAU,aAAa,OAAO,CAAC;YAErC,IAAI,SAAS,SAAS;gBACpB,IAAI;oBACF,MAAM,OAAO,KAAK,KAAK,CAAC;oBACxB,aAAa;wBACX;wBACA;wBACA,SAAS;wBACT,iBAAiB;oBACnB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C;gBACF;YACF,OAAO;gBACL;yCAAa,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,SAAS;wBAAM,CAAC;;YACnD;QACF;4BAAG;QAAC;KAAO;IAEX,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI;gBAEjC,wCAAmC;oBACjC,aAAa,OAAO,CAAC,SAAS;oBAC9B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;gBAC9C;gBAEA,aAAa;oBACX;oBACA;oBACA,SAAS;oBACT,iBAAiB;gBACnB;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK;gBAAC;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;IAEA,MAAM,WAAW,OAAO,MAAc,OAAe;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;oBAAO;gBAAS;YAC/C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI;gBAEjC,wCAAmC;oBACjC,aAAa,OAAO,CAAC,SAAS;oBAC9B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;gBAC9C;gBAEA,aAAa;oBACX;oBACA;oBACA,SAAS;oBACT,iBAAiB;gBACnB;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK;gBAAC;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;IAIA,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU,IAAI,EAAE;YAClB,MAAM,UAAU;gBAAE,GAAG,UAAU,IAAI;gBAAE,GAAG,WAAW;YAAC;YACpD,wCAAmC;gBACjC,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;YAC9C;YACA,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,MAAM;gBACR,CAAC;QACH;IACF;IAEA,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YACjC,IAAI,UAAU,KAAK,EAAE;gBACnB,OAAO;oBACL,iBAAiB,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE;oBAC5C,gBAAgB;gBAClB;YACF;YACA,OAAO;gBACL,gBAAgB;YAClB;QACF;8CAAG;QAAC,UAAU,KAAK;KAAC;IAEpB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wCAAE,OAAO,KAAa,UAAuB,CAAC,CAAC;YACvE,4CAA4C;YAC5C,MAAM,aAAa,QAAQ,IAAI,YAAY;YAE3C,MAAM,UAAU;gBACd,GAAI,aAAa,CAAC,IAAI,gBAAgB;gBACtC,GAAG,QAAQ,OAAO;YACpB;YAEA,gCAAgC;YAChC,IAAI,cAAc,UAAU,KAAK,EAAE;gBACjC,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE;YACxD;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,GAAG,OAAO;gBACV;YACF;YAEA,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,kBAAkB;gBAClB;gBACA,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;QACT;uCAAG;QAAC;QAAgB;KAAO;IAE3B,OAAO;QACL,GAAG,SAAS;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAjMgB;;QACC,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface ClientOnlyProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n}\n\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  const [hasMounted, setHasMounted] = useState(false)\n\n  useEffect(() => {\n    setHasMounted(true)\n  }, [])\n\n  if (!hasMounted) {\n    return <>{fallback}</>\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASO,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,cAAc;QAChB;+BAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;GAZgB;KAAA", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/hooks/useAuth'\nimport { ClientOnly } from '@/components/ClientOnly'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requireFamily?: boolean // 是否需要用户已加入家庭\n  requireFamilyAdmin?: boolean // 是否需要家庭管理员权限\n}\n\nconst LoadingSpinner = () => (\n  <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n    <div className=\"text-center\">\n      <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      <p className=\"mt-4 text-gray-600\">加载中...</p>\n    </div>\n  </div>\n)\n\nfunction ProtectedRouteContent({\n  children,\n  requireFamily = false,\n  requireFamilyAdmin = false\n}: ProtectedRouteProps) {\n  const router = useRouter()\n  const { user, loading, isAuthenticated } = useAuth()\n\n  useEffect(() => {\n    if (!loading) {\n      if (!isAuthenticated) {\n        router.push('/auth/login')\n        return\n      }\n\n      if (requireFamily && !user?.familyId) {\n        router.push('/family')\n        return\n      }\n\n      if (requireFamilyAdmin && user?.familyRole !== 'OWNER' && user?.familyRole !== 'ADMIN') {\n        router.push('/dashboard')\n        return\n      }\n    }\n  }, [loading, isAuthenticated, user, router, requireFamily, requireFamilyAdmin])\n\n  if (loading) {\n    return <LoadingSpinner />\n  }\n\n  if (!isAuthenticated) {\n    return null // 将重定向到登录页面\n  }\n\n  if (requireFamily && !user?.familyId) {\n    return null // 将重定向到家庭页面\n  }\n\n  if (requireFamilyAdmin && user?.familyRole !== 'OWNER' && user?.familyRole !== 'ADMIN') {\n    return null // 将重定向到仪表板\n  }\n\n  return <>{children}</>\n}\n\nexport function ProtectedRoute(props: ProtectedRouteProps) {\n  return (\n    <ClientOnly fallback={<LoadingSpinner />}>\n      <ProtectedRouteContent {...props} />\n    </ClientOnly>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAaA,MAAM,iBAAiB,kBACrB,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;;;;;;;;;;;;KAJlC;AASN,SAAS,sBAAsB,EAC7B,QAAQ,EACR,gBAAgB,KAAK,EACrB,qBAAqB,KAAK,EACN;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,CAAC,SAAS;gBACZ,IAAI,CAAC,iBAAiB;oBACpB,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,iBAAiB,CAAC,MAAM,UAAU;oBACpC,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,sBAAsB,MAAM,eAAe,WAAW,MAAM,eAAe,SAAS;oBACtF,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;0CAAG;QAAC;QAAS;QAAiB;QAAM;QAAQ;QAAe;KAAmB;IAE9E,IAAI,SAAS;QACX,qBAAO,6LAAC;;;;;IACV;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,KAAK,YAAY;;IAC1B;IAEA,IAAI,iBAAiB,CAAC,MAAM,UAAU;QACpC,OAAO,KAAK,YAAY;;IAC1B;IAEA,IAAI,sBAAsB,MAAM,eAAe,WAAW,MAAM,eAAe,SAAS;QACtF,OAAO,KAAK,WAAW;;IACzB;IAEA,qBAAO;kBAAG;;AACZ;GA5CS;;QAKQ,qIAAA,CAAA,YAAS;QACmB,0HAAA,CAAA,UAAO;;;MAN3C;AA8CF,SAAS,eAAe,KAA0B;IACvD,qBACE,6LAAC,mIAAA,CAAA,aAAU;QAAC,wBAAU,6LAAC;;;;;kBACrB,cAAA,6LAAC;YAAuB,GAAG,KAAK;;;;;;;;;;;AAGtC;MANgB", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency = 'CNY'): string {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n  }).format(amount)\n}\n\nexport function formatDate(date: Date | string, format: 'short' | 'long' | 'time' = 'short'): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  \n  switch (format) {\n    case 'short':\n      return d.toLocaleDateString('zh-CN')\n    case 'long':\n      return d.toLocaleDateString('zh-CN', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        weekday: 'long',\n      })\n    case 'time':\n      return d.toLocaleString('zh-CN')\n    default:\n      return d.toLocaleDateString('zh-CN')\n  }\n}\n\nexport function formatRelativeTime(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) {\n    return '刚刚'\n  } else if (diffInSeconds < 3600) {\n    return `${Math.floor(diffInSeconds / 60)}分钟前`\n  } else if (diffInSeconds < 86400) {\n    return `${Math.floor(diffInSeconds / 3600)}小时前`\n  } else if (diffInSeconds < 2592000) {\n    return `${Math.floor(diffInSeconds / 86400)}天前`\n  } else {\n    return formatDate(d)\n  }\n}\n\nexport function getTransactionTypeLabel(type: string): string {\n  const labels: Record<string, string> = {\n    INCOME: '收入',\n    EXPENSE: '支出',\n    TRANSFER: '转账',\n  }\n  return labels[type] || type\n}\n\nexport function getCardTypeLabel(type: string): string {\n  const labels: Record<string, string> = {\n    BANK_CARD: '银行卡',\n    CREDIT_CARD: '信用卡',\n    WECHAT: '微信',\n    ALIPAY: '支付宝',\n    CASH: '现金',\n    OTHER: '其他',\n  }\n  return labels[type] || type\n}\n\nexport function getTransactionStatusLabel(status: string): string {\n  const labels: Record<string, string> = {\n    PENDING: '待确认',\n    CONFIRMED: '已确认',\n    CANCELLED: '已取消',\n    LINKED: '已关联',\n  }\n  return labels[status] || status\n}\n\nexport function getStatusColor(status: string): string {\n  const colors: Record<string, string> = {\n    PENDING: 'bg-yellow-100 text-yellow-800',\n    CONFIRMED: 'bg-green-100 text-green-800',\n    CANCELLED: 'bg-red-100 text-red-800',\n    LINKED: 'bg-blue-100 text-blue-800',\n  }\n  return colors[status] || 'bg-gray-100 text-gray-800'\n}\n\nexport function getTypeColor(type: string): string {\n  const colors: Record<string, string> = {\n    INCOME: 'text-green-600',\n    EXPENSE: 'text-red-600',\n    TRANSFER: 'text-blue-600',\n  }\n  return colors[type] || 'text-gray-600'\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) {\n      clearTimeout(timeout)\n    }\n    \n    timeout = setTimeout(() => {\n      func(...args)\n    }, wait)\n  }\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB,EAAE,SAAoC,OAAO;IACzF,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAEtD,OAAQ;QACN,KAAK;YACH,OAAO,EAAE,kBAAkB,CAAC;QAC9B,KAAK;YACH,OAAO,EAAE,kBAAkB,CAAC,SAAS;gBACnC,MAAM;gBACN,OAAO;gBACP,KAAK;gBACL,SAAS;YACX;QACF,KAAK;YACH,OAAO,EAAE,cAAc,CAAC;QAC1B;YACE,OAAO,EAAE,kBAAkB,CAAC;IAChC;AACF;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,GAAG,CAAC;IAC/C,OAAO,IAAI,gBAAgB,OAAO;QAChC,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,GAAG,CAAC;IACjD,OAAO,IAAI,gBAAgB,SAAS;QAClC,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,EAAE,CAAC;IACjD,OAAO;QACL,OAAO,WAAW;IACpB;AACF;AAEO,SAAS,wBAAwB,IAAY;IAClD,MAAM,SAAiC;QACrC,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,SAAiC;QACrC,WAAW;QACX,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,OAAO;IACT;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,0BAA0B,MAAc;IACtD,MAAM,SAAiC;QACrC,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,SAAiC;QACrC,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAEO,SAAS,aAAa,IAAY;IACvC,MAAM,SAAiC;QACrC,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS;YACX,aAAa;QACf;QAEA,UAAU,WAAW;YACnB,QAAQ;QACV,GAAG;IACL;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n  children: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading = false, disabled, children, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    }\n    \n    const sizes = {\n      sm: 'h-8 px-3 text-sm',\n      md: 'h-10 px-4 text-sm',\n      lg: 'h-12 px-6 text-base',\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC/F,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/transactions/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useSearchParams } from 'next/navigation'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute'\nimport { Button } from '@/components/ui/Button'\nimport { ArrowLeftIcon } from '@heroicons/react/24/outline'\n\ninterface Transaction {\n  id: string\n  externalId?: string\n  type: string\n  status: string\n  amount: number\n  description: string\n  category?: string\n  subcategory?: string\n  transactionDate: string\n  location?: string\n  notes?: string\n  createdAt: string\n  user: {\n    id: string\n    name: string\n  }\n  card: {\n    id: string\n    name: string\n    type: string\n  }\n}\n\ninterface TransactionListResponse {\n  success: boolean\n  data: Transaction[]\n  pagination: {\n    page: number\n    limit: number\n    total: number\n    totalPages: number\n  }\n}\n\nfunction TransactionsPageContent() {\n  const { apiCall } = useAuth()\n  const searchParams = useSearchParams()\n  const cardId = searchParams.get('cardId')\n  \n  const [transactions, setTransactions] = useState<Transaction[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  const [cardName, setCardName] = useState<string>('')\n\n  useEffect(() => {\n    if (cardId) {\n      fetchTransactions()\n      fetchCardInfo()\n    } else {\n      setError('未指定卡片ID')\n      setLoading(false)\n    }\n  }, [cardId])\n\n  const fetchTransactions = async () => {\n    try {\n      const response = await apiCall(`/api/transactions?cardId=${cardId}&limit=50`)\n      const data: TransactionListResponse = await response.json()\n      \n      if (data.success) {\n        setTransactions(data.data)\n      } else {\n        setError('获取交易记录失败')\n      }\n    } catch (error) {\n      console.error('获取交易记录失败:', error)\n      setError('获取交易记录失败')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const fetchCardInfo = async () => {\n    try {\n      const response = await apiCall(`/api/cards/${cardId}`)\n      const data = await response.json()\n      \n      if (data.success) {\n        setCardName(data.data.name)\n      }\n    } catch (error) {\n      console.error('获取卡片信息失败:', error)\n    }\n  }\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY'\n    }).format(amount)\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleString('zh-CN')\n  }\n\n  const getTransactionTypeLabel = (type: string) => {\n    const labels: Record<string, string> = {\n      INCOME: '收入',\n      EXPENSE: '支出',\n      TRANSFER: '转账',\n    }\n    return labels[type] || type\n  }\n\n  const getAmountColor = (type: string, amount: number) => {\n    if (type === 'INCOME' || amount > 0) {\n      return 'text-green-600'\n    } else {\n      return 'text-red-600'\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center min-h-64\">\n        <div className=\"text-gray-500\">加载中...</div>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-red-500 text-lg mb-4\">{error}</div>\n        <Link href=\"/cards\">\n          <Button>返回卡片列表</Button>\n        </Link>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* 页面头部 */}\n      <div className=\"flex items-center justify-between mb-8\">\n        <div className=\"flex items-center space-x-4\">\n          <Link href=\"/cards\">\n            <Button variant=\"outline\" size=\"sm\">\n              <ArrowLeftIcon className=\"h-4 w-4 mr-2\" />\n              返回卡片\n            </Button>\n          </Link>\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">交易记录</h1>\n            {cardName && (\n              <p className=\"text-gray-600 mt-1\">{cardName}</p>\n            )}\n          </div>\n        </div>\n        \n        <div className=\"text-sm text-gray-500\">\n          共 {transactions.length} 笔交易\n        </div>\n      </div>\n\n      {/* 交易列表 */}\n      {transactions.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <div className=\"text-gray-500 text-lg mb-4\">暂无交易记录</div>\n          <p className=\"text-gray-400\">导入账单后，交易记录将显示在这里</p>\n        </div>\n      ) : (\n        <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    交易时间\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    类型\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    描述\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    分类\n                  </th>\n                  <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    金额\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    地点\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {transactions.map((transaction) => (\n                  <tr key={transaction.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {formatDate(transaction.transactionDate)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                        transaction.type === 'INCOME' \n                          ? 'bg-green-100 text-green-800'\n                          : transaction.type === 'EXPENSE'\n                          ? 'bg-red-100 text-red-800'\n                          : 'bg-blue-100 text-blue-800'\n                      }`}>\n                        {getTransactionTypeLabel(transaction.type)}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 text-sm text-gray-900\">\n                      <div className=\"max-w-xs truncate\" title={transaction.description}>\n                        {transaction.description}\n                      </div>\n                      {transaction.notes && (\n                        <div className=\"text-xs text-gray-500 mt-1 max-w-xs truncate\" title={transaction.notes}>\n                          {transaction.notes}\n                        </div>\n                      )}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {transaction.category || '-'}\n                    </td>\n                    <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium text-right ${getAmountColor(transaction.type, transaction.amount)}`}>\n                      {formatCurrency(transaction.amount)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {transaction.location || '-'}\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default function TransactionsPage() {\n  return (\n    <ProtectedRoute>\n      <TransactionsPageContent />\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AA6CA,SAAS;;IACP,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,aAAa,GAAG,CAAC;IAEhC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,IAAI,QAAQ;gBACV;gBACA;YACF,OAAO;gBACL,SAAS;gBACT,WAAW;YACb;QACF;4CAAG;QAAC;KAAO;IAEX,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,CAAC,yBAAyB,EAAE,OAAO,SAAS,CAAC;YAC5E,MAAM,OAAgC,MAAM,SAAS,IAAI;YAEzD,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB,KAAK,IAAI;YAC3B,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,CAAC,WAAW,EAAE,QAAQ;YACrD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,KAAK,IAAI,CAAC,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,cAAc,CAAC;IAC7C;IAEA,MAAM,0BAA0B,CAAC;QAC/B,MAAM,SAAiC;YACrC,QAAQ;YACR,SAAS;YACT,UAAU;QACZ;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,MAAM,iBAAiB,CAAC,MAAc;QACpC,IAAI,SAAS,YAAY,SAAS,GAAG;YACnC,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BAA6B;;;;;;8BAC5C,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;kCAAC;;;;;;;;;;;;;;;;;IAIhB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,6LAAC,4NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI9C,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;oCAChD,0BACC,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;kCAKzC,6LAAC;wBAAI,WAAU;;4BAAwB;4BAClC,aAAa,MAAM;4BAAC;;;;;;;;;;;;;YAK1B,aAAa,MAAM,KAAK,kBACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA6B;;;;;;kCAC5C,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;qCAG/B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAkF;;;;;;sDAGhG,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;;;;;;;;;;;;0CAKnG,6LAAC;gCAAM,WAAU;0CACd,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC;wCAAwB,WAAU;;0DACjC,6LAAC;gDAAG,WAAU;0DACX,WAAW,YAAY,eAAe;;;;;;0DAEzC,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAK,WAAW,CAAC,yDAAyD,EACzE,YAAY,IAAI,KAAK,WACjB,gCACA,YAAY,IAAI,KAAK,YACrB,4BACA,6BACJ;8DACC,wBAAwB,YAAY,IAAI;;;;;;;;;;;0DAG7C,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAI,WAAU;wDAAoB,OAAO,YAAY,WAAW;kEAC9D,YAAY,WAAW;;;;;;oDAEzB,YAAY,KAAK,kBAChB,6LAAC;wDAAI,WAAU;wDAA+C,OAAO,YAAY,KAAK;kEACnF,YAAY,KAAK;;;;;;;;;;;;0DAIxB,6LAAC;gDAAG,WAAU;0DACX,YAAY,QAAQ,IAAI;;;;;;0DAE3B,6LAAC;gDAAG,WAAW,CAAC,2DAA2D,EAAE,eAAe,YAAY,IAAI,EAAE,YAAY,MAAM,GAAG;0DAChI,eAAe,YAAY,MAAM;;;;;;0DAEpC,6LAAC;gDAAG,WAAU;0DACX,YAAY,QAAQ,IAAI;;;;;;;uCAhCpB,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CzC;GAvMS;;QACa,0HAAA,CAAA,UAAO;QACN,qIAAA,CAAA,kBAAe;;;KAF7B;AAyMM,SAAS;IACtB,qBACE,6LAAC,+IAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC;;;;;;;;;;AAGP;MANwB", "debugId": null}}]}