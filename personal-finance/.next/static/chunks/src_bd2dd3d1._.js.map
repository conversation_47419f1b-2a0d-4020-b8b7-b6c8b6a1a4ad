{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { useRouter } from 'next/navigation'\n\ninterface User {\n  id: string\n  email: string\n  name: string\n  avatar?: string\n  familyId?: string\n  familyRole?: string\n  family?: {\n    id: string\n    name: string\n    role: string\n  }\n}\n\ninterface AuthState {\n  user: User | null\n  token: string | null\n  loading: boolean\n  isAuthenticated: boolean\n}\n\nexport function useAuth() {\n  const router = useRouter()\n  const [authState, setAuthState] = useState<AuthState>({\n    user: null,\n    token: null,\n    loading: true,\n    isAuthenticated: false,\n  })\n\n  // 定义logout函数（需要在useEffect之前定义以避免循环依赖）\n  const logout = useCallback(() => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('token')\n      localStorage.removeItem('user')\n    }\n\n    setAuthState({\n      user: null,\n      token: null,\n      loading: false,\n      isAuthenticated: false,\n    })\n\n    router.push('/auth/login')\n  }, [router])\n\n  useEffect(() => {\n    // 确保只在客户端执行\n    if (typeof window === 'undefined') {\n      setAuthState(prev => ({ ...prev, loading: false }))\n      return\n    }\n\n    // 从localStorage获取认证信息\n    const token = localStorage.getItem('token')\n    const userStr = localStorage.getItem('user')\n\n    if (token && userStr) {\n      try {\n        const user = JSON.parse(userStr)\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n      } catch (error) {\n        console.error('Failed to parse user data:', error)\n        logout()\n      }\n    } else {\n      setAuthState(prev => ({ ...prev, loading: false }))\n    }\n  }, [logout])\n\n  const login = async (email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        const { user, token } = data.data\n\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', token)\n          localStorage.setItem('user', JSON.stringify(user))\n        }\n\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n\n        return { success: true }\n      } else {\n        return { success: false, error: data.error }\n      }\n    } catch (error) {\n      console.error('Login error:', error)\n      return { success: false, error: '网络错误，请重试' }\n    }\n  }\n\n  const register = async (name: string, email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ name, email, password }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        const { user, token } = data.data\n\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', token)\n          localStorage.setItem('user', JSON.stringify(user))\n        }\n\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n\n        return { success: true }\n      } else {\n        return { success: false, error: data.error }\n      }\n    } catch (error) {\n      console.error('Register error:', error)\n      return { success: false, error: '网络错误，请重试' }\n    }\n  }\n\n\n\n  const updateUser = (updatedUser: Partial<User>) => {\n    if (authState.user) {\n      const newUser = { ...authState.user, ...updatedUser }\n      if (typeof window !== 'undefined') {\n        localStorage.setItem('user', JSON.stringify(newUser))\n      }\n      setAuthState(prev => ({\n        ...prev,\n        user: newUser,\n      }))\n    }\n  }\n\n  const getAuthHeaders = useCallback(() => {\n    if (authState.token) {\n      return {\n        'Authorization': `Bearer ${authState.token}`,\n        'Content-Type': 'application/json',\n      }\n    }\n    return {\n      'Content-Type': 'application/json',\n    }\n  }, [authState.token])\n\n  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {\n    // 如果body是FormData，不要设置Content-Type，让浏览器自动设置\n    const isFormData = options.body instanceof FormData\n\n    const headers = {\n      ...(isFormData ? {} : getAuthHeaders()), // FormData时只设置Authorization\n      ...options.headers,\n    }\n\n    // 如果是FormData，只添加Authorization头\n    if (isFormData && authState.token) {\n      headers['Authorization'] = `Bearer ${authState.token}`\n    }\n\n    const response = await fetch(url, {\n      ...options,\n      headers,\n    })\n\n    if (response.status === 401) {\n      // Token过期或无效，自动登出\n      logout()\n      throw new Error('认证失败，请重新登录')\n    }\n\n    return response\n  }, [getAuthHeaders, logout])\n\n  return {\n    ...authState,\n    login,\n    register,\n    logout,\n    updateUser,\n    getAuthHeaders,\n    apiCall,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AA0BO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM;QACN,OAAO;QACP,SAAS;QACT,iBAAiB;IACnB;IAEA,sCAAsC;IACtC,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uCAAE;YACzB,wCAAmC;gBACjC,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;YAC1B;YAEA,aAAa;gBACX,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,iBAAiB;YACnB;YAEA,OAAO,IAAI,CAAC;QACd;sCAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,YAAY;YACZ,uCAAmC;;YAGnC;YAEA,sBAAsB;YACtB,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,UAAU,aAAa,OAAO,CAAC;YAErC,IAAI,SAAS,SAAS;gBACpB,IAAI;oBACF,MAAM,OAAO,KAAK,KAAK,CAAC;oBACxB,aAAa;wBACX;wBACA;wBACA,SAAS;wBACT,iBAAiB;oBACnB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C;gBACF;YACF,OAAO;gBACL;yCAAa,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,SAAS;wBAAM,CAAC;;YACnD;QACF;4BAAG;QAAC;KAAO;IAEX,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI;gBAEjC,wCAAmC;oBACjC,aAAa,OAAO,CAAC,SAAS;oBAC9B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;gBAC9C;gBAEA,aAAa;oBACX;oBACA;oBACA,SAAS;oBACT,iBAAiB;gBACnB;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK;gBAAC;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;IAEA,MAAM,WAAW,OAAO,MAAc,OAAe;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;oBAAO;gBAAS;YAC/C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI;gBAEjC,wCAAmC;oBACjC,aAAa,OAAO,CAAC,SAAS;oBAC9B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;gBAC9C;gBAEA,aAAa;oBACX;oBACA;oBACA,SAAS;oBACT,iBAAiB;gBACnB;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK;gBAAC;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;IAIA,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU,IAAI,EAAE;YAClB,MAAM,UAAU;gBAAE,GAAG,UAAU,IAAI;gBAAE,GAAG,WAAW;YAAC;YACpD,wCAAmC;gBACjC,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;YAC9C;YACA,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,MAAM;gBACR,CAAC;QACH;IACF;IAEA,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YACjC,IAAI,UAAU,KAAK,EAAE;gBACnB,OAAO;oBACL,iBAAiB,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE;oBAC5C,gBAAgB;gBAClB;YACF;YACA,OAAO;gBACL,gBAAgB;YAClB;QACF;8CAAG;QAAC,UAAU,KAAK;KAAC;IAEpB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wCAAE,OAAO,KAAa,UAAuB,CAAC,CAAC;YACvE,4CAA4C;YAC5C,MAAM,aAAa,QAAQ,IAAI,YAAY;YAE3C,MAAM,UAAU;gBACd,GAAI,aAAa,CAAC,IAAI,gBAAgB;gBACtC,GAAG,QAAQ,OAAO;YACpB;YAEA,gCAAgC;YAChC,IAAI,cAAc,UAAU,KAAK,EAAE;gBACjC,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE;YACxD;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,GAAG,OAAO;gBACV;YACF;YAEA,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,kBAAkB;gBAClB;gBACA,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;QACT;uCAAG;QAAC;QAAgB;KAAO;IAE3B,OAAO;QACL,GAAG,SAAS;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAjMgB;;QACC,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface ClientOnlyProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n}\n\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  const [hasMounted, setHasMounted] = useState(false)\n\n  useEffect(() => {\n    setHasMounted(true)\n  }, [])\n\n  if (!hasMounted) {\n    return <>{fallback}</>\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASO,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,cAAc;QAChB;+BAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;GAZgB;KAAA", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/hooks/useAuth'\nimport { ClientOnly } from '@/components/ClientOnly'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requireFamily?: boolean // 是否需要用户已加入家庭\n  requireFamilyAdmin?: boolean // 是否需要家庭管理员权限\n}\n\nconst LoadingSpinner = () => (\n  <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n    <div className=\"text-center\">\n      <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      <p className=\"mt-4 text-gray-600\">加载中...</p>\n    </div>\n  </div>\n)\n\nfunction ProtectedRouteContent({\n  children,\n  requireFamily = false,\n  requireFamilyAdmin = false\n}: ProtectedRouteProps) {\n  const router = useRouter()\n  const { user, loading, isAuthenticated } = useAuth()\n\n  useEffect(() => {\n    if (!loading) {\n      if (!isAuthenticated) {\n        router.push('/auth/login')\n        return\n      }\n\n      if (requireFamily && !user?.familyId) {\n        router.push('/family')\n        return\n      }\n\n      if (requireFamilyAdmin && user?.familyRole !== 'OWNER' && user?.familyRole !== 'ADMIN') {\n        router.push('/dashboard')\n        return\n      }\n    }\n  }, [loading, isAuthenticated, user, router, requireFamily, requireFamilyAdmin])\n\n  if (loading) {\n    return <LoadingSpinner />\n  }\n\n  if (!isAuthenticated) {\n    return null // 将重定向到登录页面\n  }\n\n  if (requireFamily && !user?.familyId) {\n    return null // 将重定向到家庭页面\n  }\n\n  if (requireFamilyAdmin && user?.familyRole !== 'OWNER' && user?.familyRole !== 'ADMIN') {\n    return null // 将重定向到仪表板\n  }\n\n  return <>{children}</>\n}\n\nexport function ProtectedRoute(props: ProtectedRouteProps) {\n  return (\n    <ClientOnly fallback={<LoadingSpinner />}>\n      <ProtectedRouteContent {...props} />\n    </ClientOnly>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAaA,MAAM,iBAAiB,kBACrB,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;;;;;;;;;;;;KAJlC;AASN,SAAS,sBAAsB,EAC7B,QAAQ,EACR,gBAAgB,KAAK,EACrB,qBAAqB,KAAK,EACN;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,CAAC,SAAS;gBACZ,IAAI,CAAC,iBAAiB;oBACpB,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,iBAAiB,CAAC,MAAM,UAAU;oBACpC,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,sBAAsB,MAAM,eAAe,WAAW,MAAM,eAAe,SAAS;oBACtF,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;0CAAG;QAAC;QAAS;QAAiB;QAAM;QAAQ;QAAe;KAAmB;IAE9E,IAAI,SAAS;QACX,qBAAO,6LAAC;;;;;IACV;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,KAAK,YAAY;;IAC1B;IAEA,IAAI,iBAAiB,CAAC,MAAM,UAAU;QACpC,OAAO,KAAK,YAAY;;IAC1B;IAEA,IAAI,sBAAsB,MAAM,eAAe,WAAW,MAAM,eAAe,SAAS;QACtF,OAAO,KAAK,WAAW;;IACzB;IAEA,qBAAO;kBAAG;;AACZ;GA5CS;;QAKQ,qIAAA,CAAA,YAAS;QACmB,0HAAA,CAAA,UAAO;;;MAN3C;AA8CF,SAAS,eAAe,KAA0B;IACvD,qBACE,6LAAC,mIAAA,CAAA,aAAU;QAAC,wBAAU,6LAAC;;;;;kBACrB,cAAA,6LAAC;YAAuB,GAAG,KAAK;;;;;;;;;;;AAGtC;MANgB", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/cards/CardGrid.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { PencilIcon, TrashIcon } from '@heroicons/react/24/outline'\n\ninterface Card {\n  id: string\n  name: string\n  type: string\n  accountNo?: string\n  bankName?: string\n  balance: number\n  isActive: boolean\n  createdAt: string\n  user: {\n    id: string\n    name: string\n  }\n  _count: {\n    transactions: number\n    billImports: number\n  }\n}\n\ninterface CardGridProps {\n  cards: Card[]\n  onEdit: (card: Card) => void\n  onDelete: (cardId: string) => void\n}\n\nexport function CardGrid({ cards, onEdit, onDelete }: CardGridProps) {\n  const getCardTypeLabel = (type: string) => {\n    const labels: Record<string, string> = {\n      DEBIT_CARD: '借记卡',\n      CREDIT_CARD: '信用卡',\n      WECHAT: '微信',\n      ALIPAY: '支付宝',\n      CASH: '现金',\n      OTHER: '其他',\n    }\n    return labels[type] || type\n  }\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY'\n    }).format(amount)\n  }\n\n  const getCardColor = (type: string) => {\n    const colors: Record<string, string> = {\n      DEBIT_CARD: 'from-blue-500 to-blue-600',\n      CREDIT_CARD: 'from-purple-500 to-purple-600',\n      WECHAT: 'from-green-500 to-green-600',\n      ALIPAY: 'from-blue-400 to-blue-500',\n      CASH: 'from-gray-500 to-gray-600',\n      OTHER: 'from-indigo-500 to-indigo-600',\n    }\n    return colors[type] || 'from-gray-500 to-gray-600'\n  }\n\n  if (cards.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-gray-500 text-lg mb-4\">暂无卡片</div>\n        <p className=\"text-gray-400\">点击上方\"添加卡片\"按钮创建您的第一张卡片</p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n      {cards.filter(card => card.isActive).map((card) => (\n        <div\n          key={card.id}\n          className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\"\n        >\n          {/* 卡片头部 - 模拟银行卡样式 */}\n          <div className={`bg-gradient-to-r ${getCardColor(card.type)} p-6 text-white relative`}>\n            <div className=\"flex justify-between items-start\">\n              <div>\n                <h3 className=\"text-lg font-semibold truncate\">{card.name}</h3>\n                <p className=\"text-sm opacity-90 mt-1\">{getCardTypeLabel(card.type)}</p>\n              </div>\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => onEdit(card)}\n                  className=\"p-1 hover:bg-white/20 rounded-full transition-colors\"\n                  title=\"编辑卡片\"\n                >\n                  <PencilIcon className=\"h-4 w-4\" />\n                </button>\n                <button\n                  onClick={() => onDelete(card.id)}\n                  className=\"p-1 hover:bg-white/20 rounded-full transition-colors\"\n                  title=\"删除卡片\"\n                >\n                  <TrashIcon className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </div>\n            \n            {card.accountNo && (\n              <div className=\"mt-4\">\n                <p className=\"text-sm opacity-75\">卡号</p>\n                <p className=\"font-mono text-lg tracking-wider\">{card.accountNo}</p>\n              </div>\n            )}\n            \n            <div className=\"mt-4\">\n              <p className=\"text-sm opacity-75\">余额</p>\n              <p className=\"text-2xl font-bold\">{formatCurrency(card.balance)}</p>\n            </div>\n          </div>\n\n          {/* 卡片内容 */}\n          <div className=\"p-6\">\n            {card.bankName && (\n              <div className=\"mb-4\">\n                <p className=\"text-sm text-gray-600\">银行: {card.bankName}</p>\n              </div>\n            )}\n\n            {/* 统计信息 */}\n            <div className=\"grid grid-cols-2 gap-4 mb-6\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-blue-600\">{card._count.transactions}</div>\n                <div className=\"text-sm text-gray-500\">交易记录</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-green-600\">{card._count.billImports}</div>\n                <div className=\"text-sm text-gray-500\">账单导入</div>\n              </div>\n            </div>\n\n            {/* 操作按钮 */}\n            <div className=\"space-y-3\">\n              <Link\n                href={`/import?cardId=${card.id}`}\n                className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-center block\"\n              >\n                账单导入\n              </Link>\n              <Link\n                href={`/transactions?cardId=${card.id}`}\n                className=\"w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors text-center block\"\n              >\n                查看交易\n              </Link>\n            </div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AA8BO,SAAS,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAiB;IACjE,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAiC;YACrC,YAAY;YACZ,aAAa;YACb,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,OAAO;QACT;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAiC;YACrC,YAAY;YACZ,aAAa;YACb,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,OAAO;QACT;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BAA6B;;;;;;8BAC5C,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,GAAG,CAAC,CAAC,qBACxC,6LAAC;gBAEC,WAAU;;kCAGV,6LAAC;wBAAI,WAAW,CAAC,iBAAiB,EAAE,aAAa,KAAK,IAAI,EAAE,wBAAwB,CAAC;;0CACnF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAkC,KAAK,IAAI;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAA2B,iBAAiB,KAAK,IAAI;;;;;;;;;;;;kDAEpE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,OAAO;gDACtB,WAAU;gDACV,OAAM;0DAEN,cAAA,6LAAC,sNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,6LAAC;gDACC,SAAS,IAAM,SAAS,KAAK,EAAE;gDAC/B,WAAU;gDACV,OAAM;0DAEN,cAAA,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;4BAK1B,KAAK,SAAS,kBACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,6LAAC;wCAAE,WAAU;kDAAoC,KAAK,SAAS;;;;;;;;;;;;0CAInE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,6LAAC;wCAAE,WAAU;kDAAsB,eAAe,KAAK,OAAO;;;;;;;;;;;;;;;;;;kCAKlE,6LAAC;wBAAI,WAAU;;4BACZ,KAAK,QAAQ,kBACZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;wCAAwB;wCAAK,KAAK,QAAQ;;;;;;;;;;;;0CAK3D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoC,KAAK,MAAM,CAAC,YAAY;;;;;;0DAC3E,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAqC,KAAK,MAAM,CAAC,WAAW;;;;;;0DAC3E,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAK3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE;wCACjC,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,qBAAqB,EAAE,KAAK,EAAE,EAAE;wCACvC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;eAxEA,KAAK,EAAE;;;;;;;;;;AAiFtB;KA9HgB", "debugId": null}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ui/BankSelect.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { ChevronDownIcon, CheckIcon } from '@heroicons/react/24/outline'\n\ninterface Bank {\n  id: string\n  name: string\n  shortName: string\n  logo?: string\n}\n\n// 主流银行列表\nconst BANKS: Bank[] = [\n  { id: 'icbc', name: '中国工商银行', shortName: '工商银行' },\n  { id: 'abc', name: '中国农业银行', shortName: '农业银行' },\n  { id: 'boc', name: '中国银行', shortName: '中国银行' },\n  { id: 'ccb', name: '中国建设银行', shortName: '建设银行' },\n  { id: 'bcom', name: '交通银行', shortName: '交通银行' },\n  { id: 'cmb', name: '招商银行', shortName: '招商银行' },\n  { id: 'cib', name: '兴业银行', shortName: '兴业银行' },\n  { id: 'spdb', name: '浦发银行', shortName: '浦发银行' },\n  { id: 'cmbc', name: '中国民生银行', shortName: '民生银行' },\n  { id: 'citic', name: '中信银行', shortName: '中信银行' },\n  { id: 'hxb', name: '华夏银行', shortName: '华夏银行' },\n  { id: 'gdb', name: '广发银行', shortName: '广发银行' },\n  { id: 'ping_an', name: '平安银行', shortName: '平安银行' },\n  { id: 'ceb', name: '光大银行', shortName: '光大银行' },\n  { id: 'psbc', name: '中国邮政储蓄银行', shortName: '邮储银行' },\n  { id: 'bob', name: '北京银行', shortName: '北京银行' },\n  { id: 'njcb', name: '南京银行', shortName: '南京银行' },\n  { id: 'nbcb', name: '宁波银行', shortName: '宁波银行' },\n  { id: 'other', name: '其他银行', shortName: '其他' },\n]\n\ninterface BankSelectProps {\n  value?: string\n  onChange: (bankName: string) => void\n  placeholder?: string\n  disabled?: boolean\n}\n\nexport function BankSelect({ value, onChange, placeholder = '请选择银行', disabled = false }: BankSelectProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const [searchTerm, setSearchTerm] = useState('')\n\n  const selectedBank = BANKS.find(bank => bank.name === value || bank.shortName === value)\n  \n  const filteredBanks = BANKS.filter(bank =>\n    bank.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    bank.shortName.toLowerCase().includes(searchTerm.toLowerCase())\n  )\n\n  const handleSelect = (bank: Bank) => {\n    onChange(bank.name)\n    setIsOpen(false)\n    setSearchTerm('')\n  }\n\n  return (\n    <div className=\"relative\">\n      <button\n        type=\"button\"\n        onClick={() => !disabled && setIsOpen(!isOpen)}\n        disabled={disabled}\n        className={`\n          relative w-full cursor-default rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-left shadow-sm \n          focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 sm:text-sm\n          ${disabled ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : 'hover:border-gray-400'}\n        `}\n      >\n        <span className=\"block truncate\">\n          {selectedBank ? selectedBank.name : placeholder}\n        </span>\n        <span className=\"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2\">\n          <ChevronDownIcon className=\"h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\n        </span>\n      </button>\n\n      {isOpen && !disabled && (\n        <div className=\"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm\">\n          {/* 搜索框 */}\n          <div className=\"sticky top-0 z-10 bg-white px-3 py-2 border-b border-gray-200\">\n            <input\n              type=\"text\"\n              className=\"w-full rounded-md border border-gray-300 px-3 py-1 text-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500\"\n              placeholder=\"搜索银行...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              onClick={(e) => e.stopPropagation()}\n            />\n          </div>\n\n          {/* 银行列表 */}\n          {filteredBanks.length === 0 ? (\n            <div className=\"px-3 py-2 text-gray-500 text-sm\">\n              未找到匹配的银行\n            </div>\n          ) : (\n            filteredBanks.map((bank) => (\n              <div\n                key={bank.id}\n                className={`\n                  relative cursor-default select-none py-2 pl-3 pr-9 hover:bg-indigo-600 hover:text-white\n                  ${selectedBank?.id === bank.id ? 'bg-indigo-600 text-white' : 'text-gray-900'}\n                `}\n                onClick={() => handleSelect(bank)}\n              >\n                <div className=\"flex items-center\">\n                  <span className={`block truncate ${selectedBank?.id === bank.id ? 'font-medium' : 'font-normal'}`}>\n                    {bank.name}\n                  </span>\n                  {bank.shortName !== bank.name && (\n                    <span className=\"ml-2 text-sm text-gray-500\">\n                      ({bank.shortName})\n                    </span>\n                  )}\n                </div>\n\n                {selectedBank?.id === bank.id && (\n                  <span className=\"absolute inset-y-0 right-0 flex items-center pr-4\">\n                    <CheckIcon className=\"h-5 w-5\" aria-hidden=\"true\" />\n                  </span>\n                )}\n              </div>\n            ))\n          )}\n        </div>\n      )}\n\n      {/* 点击外部关闭下拉框 */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 z-0\"\n          onClick={() => setIsOpen(false)}\n        />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAYA,SAAS;AACT,MAAM,QAAgB;IACpB;QAAE,IAAI;QAAQ,MAAM;QAAU,WAAW;IAAO;IAChD;QAAE,IAAI;QAAO,MAAM;QAAU,WAAW;IAAO;IAC/C;QAAE,IAAI;QAAO,MAAM;QAAQ,WAAW;IAAO;IAC7C;QAAE,IAAI;QAAO,MAAM;QAAU,WAAW;IAAO;IAC/C;QAAE,IAAI;QAAQ,MAAM;QAAQ,WAAW;IAAO;IAC9C;QAAE,IAAI;QAAO,MAAM;QAAQ,WAAW;IAAO;IAC7C;QAAE,IAAI;QAAO,MAAM;QAAQ,WAAW;IAAO;IAC7C;QAAE,IAAI;QAAQ,MAAM;QAAQ,WAAW;IAAO;IAC9C;QAAE,IAAI;QAAQ,MAAM;QAAU,WAAW;IAAO;IAChD;QAAE,IAAI;QAAS,MAAM;QAAQ,WAAW;IAAO;IAC/C;QAAE,IAAI;QAAO,MAAM;QAAQ,WAAW;IAAO;IAC7C;QAAE,IAAI;QAAO,MAAM;QAAQ,WAAW;IAAO;IAC7C;QAAE,IAAI;QAAW,MAAM;QAAQ,WAAW;IAAO;IACjD;QAAE,IAAI;QAAO,MAAM;QAAQ,WAAW;IAAO;IAC7C;QAAE,IAAI;QAAQ,MAAM;QAAY,WAAW;IAAO;IAClD;QAAE,IAAI;QAAO,MAAM;QAAQ,WAAW;IAAO;IAC7C;QAAE,IAAI;QAAQ,MAAM;QAAQ,WAAW;IAAO;IAC9C;QAAE,IAAI;QAAQ,MAAM;QAAQ,WAAW;IAAO;IAC9C;QAAE,IAAI;QAAS,MAAM;QAAQ,WAAW;IAAK;CAC9C;AASM,SAAS,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,OAAO,EAAE,WAAW,KAAK,EAAmB;;IACtG,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,SAAS,KAAK,SAAS,KAAK;IAElF,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OACjC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG9D,MAAM,eAAe,CAAC;QACpB,SAAS,KAAK,IAAI;QAClB,UAAU;QACV,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAK;gBACL,SAAS,IAAM,CAAC,YAAY,UAAU,CAAC;gBACvC,UAAU;gBACV,WAAW,CAAC;;;UAGV,EAAE,WAAW,gDAAgD,wBAAwB;QACvF,CAAC;;kCAED,6LAAC;wBAAK,WAAU;kCACb,eAAe,aAAa,IAAI,GAAG;;;;;;kCAEtC,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC,gOAAA,CAAA,kBAAe;4BAAC,WAAU;4BAAwB,eAAY;;;;;;;;;;;;;;;;;YAIlE,UAAU,CAAC,0BACV,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,WAAU;4BACV,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;;;;;;oBAKpC,cAAc,MAAM,KAAK,kBACxB,6LAAC;wBAAI,WAAU;kCAAkC;;;;;+BAIjD,cAAc,GAAG,CAAC,CAAC,qBACjB,6LAAC;4BAEC,WAAW,CAAC;;kBAEV,EAAE,cAAc,OAAO,KAAK,EAAE,GAAG,6BAA6B,gBAAgB;gBAChF,CAAC;4BACD,SAAS,IAAM,aAAa;;8CAE5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAW,CAAC,eAAe,EAAE,cAAc,OAAO,KAAK,EAAE,GAAG,gBAAgB,eAAe;sDAC9F,KAAK,IAAI;;;;;;wCAEX,KAAK,SAAS,KAAK,KAAK,IAAI,kBAC3B,6LAAC;4CAAK,WAAU;;gDAA6B;gDACzC,KAAK,SAAS;gDAAC;;;;;;;;;;;;;gCAKtB,cAAc,OAAO,KAAK,EAAE,kBAC3B,6LAAC;oCAAK,WAAU;8CACd,cAAA,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;2BApB1C,KAAK,EAAE;;;;;;;;;;;YA8BrB,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;;;;;;;AAKnC;GAjGgB;KAAA", "debugId": null}}, {"offset": {"line": 1047, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/cards/CardForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/hooks/useAuth'\nimport { useToast } from '@/hooks/useToast'\nimport { Button } from '@/components/ui/Button'\nimport { BankSelect } from '@/components/ui/BankSelect'\n\ninterface Card {\n  id: string\n  name: string\n  type: string\n  accountNo?: string\n  bankName?: string\n  balance: number\n  description?: string\n  isActive: boolean\n}\n\ninterface CardFormProps {\n  card?: Card | null\n  onSuccess: () => void\n  onCancel: () => void\n}\n\nconst CARD_TYPES = [\n  { value: 'DEBIT_CARD', label: '借记卡' },\n  { value: 'CREDIT_CARD', label: '信用卡' },\n  { value: 'WECHAT', label: '微信' },\n  { value: 'ALIPAY', label: '支付宝' },\n  { value: 'CASH', label: '现金' },\n  { value: 'OTHER', label: '其他' },\n]\n\nexport function CardForm({ card, onSuccess, onCancel }: CardFormProps) {\n  const { apiCall } = useAuth()\n  const { showSuccess, showError } = useToast()\n  const [loading, setLoading] = useState(false)\n  const [formData, setFormData] = useState({\n    name: '',\n    type: 'DEBIT_CARD',\n    accountNo: '',\n    bankName: '',\n    balance: 0,\n    description: '',\n    isActive: true,\n  })\n\n  useEffect(() => {\n    if (card) {\n      setFormData({\n        name: card.name,\n        type: card.type,\n        accountNo: card.accountNo || '',\n        bankName: card.bankName || '',\n        balance: card.balance,\n        description: card.description || '',\n        isActive: card.isActive,\n      })\n    }\n  }, [card])\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n\n    try {\n      const url = card ? `/api/cards/${card.id}` : '/api/cards'\n      const method = card ? 'PUT' : 'POST'\n\n      const response = await apiCall(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      })\n\n      const data = await response.json()\n      if (data.success) {\n        showSuccess(card ? '卡片更新成功' : '卡片创建成功')\n        onSuccess()\n      } else {\n        showError('操作失败', data.error || '请检查输入信息')\n      }\n    } catch (error) {\n      console.error('Form submission failed:', error)\n      showError('操作失败', '网络错误，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n    const { name, value, type } = e.target\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'number' ? parseFloat(value) || 0 : \n              type === 'checkbox' ? (e.target as HTMLInputElement).checked : value\n    }))\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\">\n        <h2 className=\"text-xl font-bold mb-4\">\n          {card ? '编辑卡片' : '添加卡片'}\n        </h2>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* 卡片名称 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              卡片名称 *\n            </label>\n            <input\n              type=\"text\"\n              name=\"name\"\n              value={formData.name}\n              onChange={handleChange}\n              required\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              placeholder=\"例如：招商银行储蓄卡\"\n            />\n          </div>\n\n          {/* 卡片类型 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              卡片类型 *\n            </label>\n            <select\n              name=\"type\"\n              value={formData.type}\n              onChange={handleChange}\n              required\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n            >\n              {CARD_TYPES.map(type => (\n                <option key={type.value} value={type.value}>\n                  {type.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* 账号 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              账号/卡号\n            </label>\n            <input\n              type=\"text\"\n              name=\"accountNo\"\n              value={formData.accountNo}\n              onChange={handleChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              placeholder=\"例如：****1234\"\n            />\n          </div>\n\n          {/* 银行名称 */}\n          {(formData.type === 'DEBIT_CARD' || formData.type === 'CREDIT_CARD') && (\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                银行名称\n              </label>\n              <BankSelect\n                value={formData.bankName}\n                onChange={(bankName) => setFormData(prev => ({ ...prev, bankName }))}\n                placeholder=\"请选择银行\"\n              />\n            </div>\n          )}\n\n          {/* 余额 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              当前余额\n            </label>\n            <input\n              type=\"number\"\n              name=\"balance\"\n              value={formData.balance}\n              onChange={handleChange}\n              step=\"0.01\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              placeholder=\"0.00\"\n            />\n          </div>\n\n          {/* 描述 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              描述\n            </label>\n            <textarea\n              name=\"description\"\n              value={formData.description}\n              onChange={handleChange}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              placeholder=\"可选的描述信息\"\n            />\n          </div>\n\n          {/* 状态 */}\n          <div className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              name=\"isActive\"\n              checked={formData.isActive}\n              onChange={handleChange}\n              className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n            />\n            <label className=\"ml-2 block text-sm text-gray-700\">\n              启用此卡片\n            </label>\n          </div>\n\n          {/* 按钮 */}\n          <div className=\"flex space-x-3 pt-4\">\n            <Button\n              type=\"submit\"\n              disabled={loading}\n              className=\"flex-1\"\n            >\n              {loading ? '保存中...' : (card ? '更新' : '创建')}\n            </Button>\n            <Button\n              type=\"button\"\n              variant=\"secondary\"\n              onClick={onCancel}\n              disabled={loading}\n              className=\"flex-1\"\n            >\n              取消\n            </Button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAyBA,MAAM,aAAa;IACjB;QAAE,OAAO;QAAc,OAAO;IAAM;IACpC;QAAE,OAAO;QAAe,OAAO;IAAM;IACrC;QAAE,OAAO;QAAU,OAAO;IAAK;IAC/B;QAAE,OAAO;QAAU,OAAO;IAAM;IAChC;QAAE,OAAO;QAAQ,OAAO;IAAK;IAC7B;QAAE,OAAO;QAAS,OAAO;IAAK;CAC/B;AAEM,SAAS,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAiB;;IACnE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,MAAM;QACN,WAAW;QACX,UAAU;QACV,SAAS;QACT,aAAa;QACb,UAAU;IACZ;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,MAAM;gBACR,YAAY;oBACV,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,WAAW,KAAK,SAAS,IAAI;oBAC7B,UAAU,KAAK,QAAQ,IAAI;oBAC3B,SAAS,KAAK,OAAO;oBACrB,aAAa,KAAK,WAAW,IAAI;oBACjC,UAAU,KAAK,QAAQ;gBACzB;YACF;QACF;6BAAG;QAAC;KAAK;IAET,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,MAAM,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,GAAG;YAC7C,MAAM,SAAS,OAAO,QAAQ;YAE9B,MAAM,WAAW,MAAM,QAAQ,KAAK;gBAClC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,OAAO,WAAW;gBAC9B;YACF,OAAO;gBACL,UAAU,QAAQ,KAAK,KAAK,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,UAAU,QAAQ;QACpB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,WAAW,WAAW,UAAU,IACzC,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;YACzE,CAAC;IACH;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BACX,OAAO,SAAS;;;;;;8BAGnB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU;oCACV,QAAQ;oCACR,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU;oCACV,QAAQ;oCACR,WAAU;8CAET,WAAW,GAAG,CAAC,CAAA,qBACd,6LAAC;4CAAwB,OAAO,KAAK,KAAK;sDACvC,KAAK,KAAK;2CADA,KAAK,KAAK;;;;;;;;;;;;;;;;sCAQ7B,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,SAAS;oCACzB,UAAU;oCACV,WAAU;oCACV,aAAY;;;;;;;;;;;;wBAKf,CAAC,SAAS,IAAI,KAAK,gBAAgB,SAAS,IAAI,KAAK,aAAa,mBACjE,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC,yIAAA,CAAA,aAAU;oCACT,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,WAAa,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE;4CAAS,CAAC;oCAClE,aAAY;;;;;;;;;;;;sCAMlB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,OAAO;oCACvB,UAAU;oCACV,MAAK;oCACL,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,WAAW;oCAC3B,UAAU;oCACV,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,SAAS,SAAS,QAAQ;oCAC1B,UAAU;oCACV,WAAU;;;;;;8CAEZ,6LAAC;oCAAM,WAAU;8CAAmC;;;;;;;;;;;;sCAMtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,WAAY,OAAO,OAAO;;;;;;8CAEvC,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAjNgB;;QACM,0HAAA,CAAA,UAAO;QACQ,4HAAA,CAAA,WAAQ;;;KAF7B", "debugId": null}}, {"offset": {"line": 1444, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/cards/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { useToast } from '@/hooks/useToast'\nimport { useConfirm } from '@/hooks/useConfirm'\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute'\nimport { ClientOnly } from '@/components/ClientOnly'\nimport { Button } from '@/components/ui/Button'\nimport { CardGrid } from '@/components/cards/CardGrid'\nimport { CardForm } from '@/components/cards/CardForm'\n\ninterface Card {\n  id: string\n  name: string\n  type: string\n  accountNo?: string\n  bankName?: string\n  balance: number\n  isActive: boolean\n  createdAt: string\n  user: {\n    id: string\n    name: string\n  }\n  _count: {\n    transactions: number\n    billImports: number\n  }\n}\n\nfunction CardsPageContent() {\n  const { apiCall, isAuthenticated, loading: authLoading } = useAuth()\n  const { showSuccess, showError } = useToast()\n  const { confirm } = useConfirm()\n  const [cards, setCards] = useState<Card[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showCreateForm, setShowCreateForm] = useState(false)\n  const [editingCard, setEditingCard] = useState<Card | null>(null)\n\n  useEffect(() => {\n    // 只有在认证完成且用户已登录时才获取卡片\n    if (!authLoading && isAuthenticated) {\n      fetchCards()\n    } else if (!authLoading && !isAuthenticated) {\n      setLoading(false)\n    }\n  }, [authLoading, isAuthenticated])\n\n  const fetchCards = async () => {\n    try {\n      setLoading(true)\n      const response = await apiCall('/api/cards')\n      const data = await response.json()\n\n      if (data.success) {\n        setCards(data.data)\n      } else {\n        console.error('Failed to fetch cards:', data.error)\n      }\n    } catch (error) {\n      console.error('Failed to fetch cards:', error)\n      // 如果是认证错误，用户会被自动重定向到登录页面\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleEdit = (card: Card) => {\n    setEditingCard(card)\n    setShowCreateForm(true)\n  }\n\n  const handleDelete = async (cardId: string) => {\n    const confirmed = await confirm({\n      title: '删除卡片',\n      message: '确定要删除这张卡片吗？删除后相关的交易记录也会被删除，此操作不可撤销。',\n      confirmText: '删除',\n      cancelText: '取消',\n      type: 'danger'\n    })\n\n    if (!confirmed) return\n\n    try {\n      const response = await apiCall(`/api/cards/${cardId}`, {\n        method: 'DELETE',\n      })\n\n      const data = await response.json()\n      if (data.success) {\n        // 硬删除：从列表中移除卡片\n        setCards(prev => prev.filter(card => card.id !== cardId))\n        showSuccess('删除成功', '卡片及相关交易记录已删除')\n      } else {\n        showError('删除失败', data.error || '请稍后重试')\n      }\n    } catch (error) {\n      console.error('Delete failed:', error)\n      showError('删除失败', '网络错误，请稍后重试')\n    }\n  }\n\n  const handleToggleStatus = async (cardId: string, isActive: boolean) => {\n    try {\n      const response = await apiCall(`/api/cards/${cardId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ isActive }),\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        if (data.success) {\n          setCards(prev => prev.map(card =>\n            card.id === cardId ? { ...card, isActive } : card\n          ))\n          showSuccess('状态更新成功', `卡片已${isActive ? '启用' : '停用'}`)\n        }\n      } else {\n        showError('状态更新失败', '请稍后重试')\n      }\n    } catch (error) {\n      console.error('Toggle status failed:', error)\n      showError('状态更新失败', '网络错误，请稍后重试')\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"bg-white shadow\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">卡片管理</h1>\n            <div className=\"flex space-x-4\">\n              <Link\n                href=\"/dashboard\"\n                className=\"text-blue-600 hover:text-blue-800 transition-colors\"\n              >\n                返回仪表板\n              </Link>\n              <Button\n                onClick={() => {\n                  setEditingCard(null)\n                  setShowCreateForm(true)\n                }}\n              >\n                添加卡片\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* 统计卡片 */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总卡片数</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">{cards.length}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">活跃卡片</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {cards.filter(card => card.isActive).length}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总交易数</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {cards.reduce((sum, card) => sum + card._count.transactions, 0)}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-yellow-100 rounded-lg\">\n                <svg className=\"w-6 h-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总导入数</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {cards.reduce((sum, card) => sum + card._count.billImports, 0)}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 卡片网格 */}\n        <CardGrid\n          cards={cards}\n          onEdit={handleEdit}\n          onDelete={handleDelete}\n        />\n      </div>\n\n      {/* 创建/编辑表单模态框 */}\n      {showCreateForm && (\n        <CardForm\n          card={editingCard}\n          onSuccess={() => {\n            setShowCreateForm(false)\n            setEditingCard(null)\n            fetchCards() // 重新获取卡片列表\n          }}\n          onCancel={() => {\n            setShowCreateForm(false)\n            setEditingCard(null)\n          }}\n        />\n      )}\n    </div>\n  )\n}\n\nexport default function CardsPage() {\n  return (\n    <ProtectedRoute>\n      <ClientOnly fallback={\n        <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n            <p className=\"mt-4 text-gray-600\">加载中...</p>\n          </div>\n        </div>\n      }>\n        <CardsPageContent />\n      </ClientOnly>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAgCA,SAAS;;IACP,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACjE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,sBAAsB;YACtB,IAAI,CAAC,eAAe,iBAAiB;gBACnC;YACF,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB;gBAC3C,WAAW;YACb;QACF;qCAAG;QAAC;QAAa;KAAgB;IAEjC,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,QAAQ;YAC/B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS,KAAK,IAAI;YACpB,OAAO;gBACL,QAAQ,KAAK,CAAC,0BAA0B,KAAK,KAAK;YACpD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,yBAAyB;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,kBAAkB;IACpB;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,YAAY,MAAM,QAAQ;YAC9B,OAAO;YACP,SAAS;YACT,aAAa;YACb,YAAY;YACZ,MAAM;QACR;QAEA,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,CAAC,WAAW,EAAE,QAAQ,EAAE;gBACrD,QAAQ;YACV;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,eAAe;gBACf,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBACjD,YAAY,QAAQ;YACtB,OAAO;gBACL,UAAU,QAAQ,KAAK,KAAK,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,UAAU,QAAQ;QACpB;IACF;IAEA,MAAM,qBAAqB,OAAO,QAAgB;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,CAAC,WAAW,EAAE,QAAQ,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,SAAS;gCAAE,GAAG,IAAI;gCAAE;4BAAS,IAAI;oBAE/C,YAAY,UAAU,CAAC,GAAG,EAAE,WAAW,OAAO,MAAM;gBACtD;YACF,OAAO;gBACL,UAAU,UAAU;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,UAAU,UAAU;QACtB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;4CACP,eAAe;4CACf,kBAAkB;wCACpB;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQT,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAwC,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAKvE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAChF,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DACV,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAMnD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DACV,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,CAAC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;0CAMrE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DACV,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,CAAC,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQtE,6LAAC,0IAAA,CAAA,WAAQ;wBACP,OAAO;wBACP,QAAQ;wBACR,UAAU;;;;;;;;;;;;YAKb,gCACC,6LAAC,0IAAA,CAAA,WAAQ;gBACP,MAAM;gBACN,WAAW;oBACT,kBAAkB;oBAClB,eAAe;oBACf,aAAa,WAAW;;gBAC1B;gBACA,UAAU;oBACR,kBAAkB;oBAClB,eAAe;gBACjB;;;;;;;;;;;;AAKV;GAnOS;;QACoD,0HAAA,CAAA,UAAO;QAC/B,4HAAA,CAAA,WAAQ;QACvB,8HAAA,CAAA,aAAU;;;KAHvB;AAqOM,SAAS;IACtB,qBACE,6LAAC,+IAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC,mIAAA,CAAA,aAAU;YAAC,wBACV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;sBAItC,cAAA,6LAAC;;;;;;;;;;;;;;;AAIT;MAfwB", "debugId": null}}]}