{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { useRouter } from 'next/navigation'\n\ninterface User {\n  id: string\n  email: string\n  name: string\n  avatar?: string\n  familyId?: string\n  familyRole?: string\n  family?: {\n    id: string\n    name: string\n    role: string\n  }\n}\n\ninterface AuthState {\n  user: User | null\n  token: string | null\n  loading: boolean\n  isAuthenticated: boolean\n}\n\nexport function useAuth() {\n  const router = useRouter()\n  const [authState, setAuthState] = useState<AuthState>({\n    user: null,\n    token: null,\n    loading: true,\n    isAuthenticated: false,\n  })\n\n  // 定义logout函数（需要在useEffect之前定义以避免循环依赖）\n  const logout = useCallback(() => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('token')\n      localStorage.removeItem('user')\n    }\n\n    setAuthState({\n      user: null,\n      token: null,\n      loading: false,\n      isAuthenticated: false,\n    })\n\n    router.push('/auth/login')\n  }, [router])\n\n  useEffect(() => {\n    // 确保只在客户端执行\n    if (typeof window === 'undefined') {\n      setAuthState(prev => ({ ...prev, loading: false }))\n      return\n    }\n\n    // 从localStorage获取认证信息\n    const token = localStorage.getItem('token')\n    const userStr = localStorage.getItem('user')\n\n    if (token && userStr) {\n      try {\n        const user = JSON.parse(userStr)\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n      } catch (error) {\n        console.error('Failed to parse user data:', error)\n        logout()\n      }\n    } else {\n      setAuthState(prev => ({ ...prev, loading: false }))\n    }\n  }, [logout])\n\n  const login = async (email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        const { user, token } = data.data\n\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', token)\n          localStorage.setItem('user', JSON.stringify(user))\n        }\n\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n\n        return { success: true }\n      } else {\n        return { success: false, error: data.error }\n      }\n    } catch (error) {\n      console.error('Login error:', error)\n      return { success: false, error: '网络错误，请重试' }\n    }\n  }\n\n  const register = async (name: string, email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ name, email, password }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        const { user, token } = data.data\n\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', token)\n          localStorage.setItem('user', JSON.stringify(user))\n        }\n\n        setAuthState({\n          user,\n          token,\n          loading: false,\n          isAuthenticated: true,\n        })\n\n        return { success: true }\n      } else {\n        return { success: false, error: data.error }\n      }\n    } catch (error) {\n      console.error('Register error:', error)\n      return { success: false, error: '网络错误，请重试' }\n    }\n  }\n\n\n\n  const updateUser = (updatedUser: Partial<User>) => {\n    if (authState.user) {\n      const newUser = { ...authState.user, ...updatedUser }\n      if (typeof window !== 'undefined') {\n        localStorage.setItem('user', JSON.stringify(newUser))\n      }\n      setAuthState(prev => ({\n        ...prev,\n        user: newUser,\n      }))\n    }\n  }\n\n  const getAuthHeaders = useCallback(() => {\n    if (authState.token) {\n      return {\n        'Authorization': `Bearer ${authState.token}`,\n        'Content-Type': 'application/json',\n      }\n    }\n    return {\n      'Content-Type': 'application/json',\n    }\n  }, [authState.token])\n\n  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {\n    // 如果body是FormData，不要设置Content-Type，让浏览器自动设置\n    const isFormData = options.body instanceof FormData\n\n    const headers = {\n      ...(isFormData ? {} : getAuthHeaders()), // FormData时只设置Authorization\n      ...options.headers,\n    }\n\n    // 如果是FormData，只添加Authorization头\n    if (isFormData && authState.token) {\n      headers['Authorization'] = `Bearer ${authState.token}`\n    }\n\n    const response = await fetch(url, {\n      ...options,\n      headers,\n    })\n\n    if (response.status === 401) {\n      // Token过期或无效，自动登出\n      logout()\n      throw new Error('认证失败，请重新登录')\n    }\n\n    return response\n  }, [getAuthHeaders, logout])\n\n  return {\n    ...authState,\n    login,\n    register,\n    logout,\n    updateUser,\n    getAuthHeaders,\n    apiCall,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AA0BO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM;QACN,OAAO;QACP,SAAS;QACT,iBAAiB;IACnB;IAEA,sCAAsC;IACtC,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uCAAE;YACzB,wCAAmC;gBACjC,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;YAC1B;YAEA,aAAa;gBACX,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,iBAAiB;YACnB;YAEA,OAAO,IAAI,CAAC;QACd;sCAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,YAAY;YACZ,uCAAmC;;YAGnC;YAEA,sBAAsB;YACtB,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,UAAU,aAAa,OAAO,CAAC;YAErC,IAAI,SAAS,SAAS;gBACpB,IAAI;oBACF,MAAM,OAAO,KAAK,KAAK,CAAC;oBACxB,aAAa;wBACX;wBACA;wBACA,SAAS;wBACT,iBAAiB;oBACnB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C;gBACF;YACF,OAAO;gBACL;yCAAa,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,SAAS;wBAAM,CAAC;;YACnD;QACF;4BAAG;QAAC;KAAO;IAEX,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI;gBAEjC,wCAAmC;oBACjC,aAAa,OAAO,CAAC,SAAS;oBAC9B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;gBAC9C;gBAEA,aAAa;oBACX;oBACA;oBACA,SAAS;oBACT,iBAAiB;gBACnB;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK;gBAAC;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;IAEA,MAAM,WAAW,OAAO,MAAc,OAAe;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;oBAAO;gBAAS;YAC/C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI;gBAEjC,wCAAmC;oBACjC,aAAa,OAAO,CAAC,SAAS;oBAC9B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;gBAC9C;gBAEA,aAAa;oBACX;oBACA;oBACA,SAAS;oBACT,iBAAiB;gBACnB;gBAEA,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK;gBAAC;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;IAIA,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU,IAAI,EAAE;YAClB,MAAM,UAAU;gBAAE,GAAG,UAAU,IAAI;gBAAE,GAAG,WAAW;YAAC;YACpD,wCAAmC;gBACjC,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;YAC9C;YACA,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,MAAM;gBACR,CAAC;QACH;IACF;IAEA,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YACjC,IAAI,UAAU,KAAK,EAAE;gBACnB,OAAO;oBACL,iBAAiB,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE;oBAC5C,gBAAgB;gBAClB;YACF;YACA,OAAO;gBACL,gBAAgB;YAClB;QACF;8CAAG;QAAC,UAAU,KAAK;KAAC;IAEpB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wCAAE,OAAO,KAAa,UAAuB,CAAC,CAAC;YACvE,4CAA4C;YAC5C,MAAM,aAAa,QAAQ,IAAI,YAAY;YAE3C,MAAM,UAAU;gBACd,GAAI,aAAa,CAAC,IAAI,gBAAgB;gBACtC,GAAG,QAAQ,OAAO;YACpB;YAEA,gCAAgC;YAChC,IAAI,cAAc,UAAU,KAAK,EAAE;gBACjC,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,UAAU,KAAK,EAAE;YACxD;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,GAAG,OAAO;gBACV;YACF;YAEA,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,kBAAkB;gBAClB;gBACA,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;QACT;uCAAG;QAAC;QAAgB;KAAO;IAE3B,OAAO;QACL,GAAG,SAAS;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAjMgB;;QACC,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface ClientOnlyProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n}\n\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  const [hasMounted, setHasMounted] = useState(false)\n\n  useEffect(() => {\n    setHasMounted(true)\n  }, [])\n\n  if (!hasMounted) {\n    return <>{fallback}</>\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASO,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,cAAc;QAChB;+BAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;GAZgB;KAAA", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/hooks/useAuth'\nimport { ClientOnly } from '@/components/ClientOnly'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requireFamily?: boolean // 是否需要用户已加入家庭\n  requireFamilyAdmin?: boolean // 是否需要家庭管理员权限\n}\n\nconst LoadingSpinner = () => (\n  <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n    <div className=\"text-center\">\n      <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      <p className=\"mt-4 text-gray-600\">加载中...</p>\n    </div>\n  </div>\n)\n\nfunction ProtectedRouteContent({\n  children,\n  requireFamily = false,\n  requireFamilyAdmin = false\n}: ProtectedRouteProps) {\n  const router = useRouter()\n  const { user, loading, isAuthenticated } = useAuth()\n\n  useEffect(() => {\n    if (!loading) {\n      if (!isAuthenticated) {\n        router.push('/auth/login')\n        return\n      }\n\n      if (requireFamily && !user?.familyId) {\n        router.push('/family')\n        return\n      }\n\n      if (requireFamilyAdmin && user?.familyRole !== 'OWNER' && user?.familyRole !== 'ADMIN') {\n        router.push('/dashboard')\n        return\n      }\n    }\n  }, [loading, isAuthenticated, user, router, requireFamily, requireFamilyAdmin])\n\n  if (loading) {\n    return <LoadingSpinner />\n  }\n\n  if (!isAuthenticated) {\n    return null // 将重定向到登录页面\n  }\n\n  if (requireFamily && !user?.familyId) {\n    return null // 将重定向到家庭页面\n  }\n\n  if (requireFamilyAdmin && user?.familyRole !== 'OWNER' && user?.familyRole !== 'ADMIN') {\n    return null // 将重定向到仪表板\n  }\n\n  return <>{children}</>\n}\n\nexport function ProtectedRoute(props: ProtectedRouteProps) {\n  return (\n    <ClientOnly fallback={<LoadingSpinner />}>\n      <ProtectedRouteContent {...props} />\n    </ClientOnly>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAaA,MAAM,iBAAiB,kBACrB,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;;;;;;;;;;;;KAJlC;AASN,SAAS,sBAAsB,EAC7B,QAAQ,EACR,gBAAgB,KAAK,EACrB,qBAAqB,KAAK,EACN;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,CAAC,SAAS;gBACZ,IAAI,CAAC,iBAAiB;oBACpB,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,iBAAiB,CAAC,MAAM,UAAU;oBACpC,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,sBAAsB,MAAM,eAAe,WAAW,MAAM,eAAe,SAAS;oBACtF,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;0CAAG;QAAC;QAAS;QAAiB;QAAM;QAAQ;QAAe;KAAmB;IAE9E,IAAI,SAAS;QACX,qBAAO,6LAAC;;;;;IACV;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,KAAK,YAAY;;IAC1B;IAEA,IAAI,iBAAiB,CAAC,MAAM,UAAU;QACpC,OAAO,KAAK,YAAY;;IAC1B;IAEA,IAAI,sBAAsB,MAAM,eAAe,WAAW,MAAM,eAAe,SAAS;QACtF,OAAO,KAAK,WAAW;;IACzB;IAEA,qBAAO;kBAAG;;AACZ;GA5CS;;QAKQ,qIAAA,CAAA,YAAS;QACmB,0HAAA,CAAA,UAAO;;;MAN3C;AA8CF,SAAS,eAAe,KAA0B;IACvD,qBACE,6LAAC,mIAAA,CAAA,aAAU;QAAC,wBAAU,6LAAC;;;;;kBACrB,cAAA,6LAAC;YAAuB,GAAG,KAAK;;;;;;;;;;;AAGtC;MANgB", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/calendar/CalendarDay.tsx"], "sourcesContent": ["'use client'\n\ninterface DailyStats {\n  date: string\n  totalTransactions: number\n  totalIncome: number\n  totalExpense: number\n  netAmount: number\n  cardStats: {\n    cardId: string\n    cardName: string\n    cardType: string\n    transactionCount: number\n    income: number\n    expense: number\n    netAmount: number\n  }[]\n}\n\ninterface DayData {\n  day: number\n  dateStr: string\n  stats: DailyStats | null\n}\n\ninterface CalendarDayProps {\n  dayData: DayData | null\n  isSelected: boolean\n  onSelect: (date: string) => void\n}\n\nexport function CalendarDay({ dayData, isSelected, onSelect }: CalendarDayProps) {\n  if (!dayData) {\n    // 空白日期\n    return <div className=\"h-24 border-r border-b border-gray-200\"></div>\n  }\n\n  const { day, dateStr, stats } = dayData\n  const hasTransactions = stats && stats.totalTransactions > 0\n  const isToday = dateStr === new Date().toISOString().split('T')[0]\n\n  const formatCurrency = (amount: number) => {\n    if (amount === 0) return '¥0'\n    if (Math.abs(amount) >= 10000) {\n      return `¥${(amount / 10000).toFixed(1)}万`\n    }\n    return `¥${amount.toFixed(0)}`\n  }\n\n  const getNetAmountColor = (amount: number) => {\n    if (amount > 0) return 'text-green-600'\n    if (amount < 0) return 'text-red-600'\n    return 'text-gray-500'\n  }\n\n  return (\n    <div\n      className={`\n        h-24 border-r border-b border-gray-200 p-2 cursor-pointer transition-colors\n        ${isSelected ? 'bg-blue-50 border-blue-300' : 'hover:bg-gray-50'}\n        ${isToday ? 'bg-blue-100' : ''}\n      `}\n      onClick={() => onSelect(dateStr)}\n    >\n      {/* 日期数字 */}\n      <div className=\"flex justify-between items-start mb-1\">\n        <span\n          className={`\n            text-sm font-medium\n            ${isToday ? 'text-blue-600 font-bold' : 'text-gray-900'}\n            ${isSelected ? 'text-blue-600' : ''}\n          `}\n        >\n          {day}\n        </span>\n        \n        {/* 交易数量指示器 */}\n        {hasTransactions && (\n          <span className=\"inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-white bg-blue-600 rounded-full\">\n            {stats.totalTransactions}\n          </span>\n        )}\n      </div>\n\n      {/* 交易统计 */}\n      {hasTransactions && (\n        <div className=\"space-y-1\">\n          {/* 收入 */}\n          {stats.totalIncome > 0 && (\n            <div className=\"text-xs text-green-600\">\n              +{formatCurrency(stats.totalIncome)}\n            </div>\n          )}\n          \n          {/* 支出 */}\n          {stats.totalExpense > 0 && (\n            <div className=\"text-xs text-red-600\">\n              -{formatCurrency(stats.totalExpense)}\n            </div>\n          )}\n          \n          {/* 净收入 */}\n          {stats.netAmount !== 0 && (\n            <div className={`text-xs font-medium ${getNetAmountColor(stats.netAmount)}`}>\n              净: {formatCurrency(stats.netAmount)}\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* 卡片数量指示器 */}\n      {hasTransactions && stats.cardStats.length > 1 && (\n        <div className=\"mt-1\">\n          <div className=\"flex space-x-1\">\n            {stats.cardStats.slice(0, 3).map((cardStat, index) => (\n              <div\n                key={cardStat.cardId}\n                className=\"w-2 h-2 rounded-full bg-gray-400\"\n                title={cardStat.cardName}\n              />\n            ))}\n            {stats.cardStats.length > 3 && (\n              <div className=\"text-xs text-gray-500\">+{stats.cardStats.length - 3}</div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;AA+BO,SAAS,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAoB;IAC7E,IAAI,CAAC,SAAS;QACZ,OAAO;QACP,qBAAO,6LAAC;YAAI,WAAU;;;;;;IACxB;IAEA,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG;IAChC,MAAM,kBAAkB,SAAS,MAAM,iBAAiB,GAAG;IAC3D,MAAM,UAAU,YAAY,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAElE,MAAM,iBAAiB,CAAC;QACtB,IAAI,WAAW,GAAG,OAAO;QACzB,IAAI,KAAK,GAAG,CAAC,WAAW,OAAO;YAC7B,OAAO,CAAC,CAAC,EAAE,CAAC,SAAS,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C;QACA,OAAO,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC,IAAI;IAChC;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,GAAG,OAAO;QACvB,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC;;QAEV,EAAE,aAAa,+BAA+B,mBAAmB;QACjE,EAAE,UAAU,gBAAgB,GAAG;MACjC,CAAC;QACD,SAAS,IAAM,SAAS;;0BAGxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAW,CAAC;;YAEV,EAAE,UAAU,4BAA4B,gBAAgB;YACxD,EAAE,aAAa,kBAAkB,GAAG;UACtC,CAAC;kCAEA;;;;;;oBAIF,iCACC,6LAAC;wBAAK,WAAU;kCACb,MAAM,iBAAiB;;;;;;;;;;;;YAM7B,iCACC,6LAAC;gBAAI,WAAU;;oBAEZ,MAAM,WAAW,GAAG,mBACnB,6LAAC;wBAAI,WAAU;;4BAAyB;4BACpC,eAAe,MAAM,WAAW;;;;;;;oBAKrC,MAAM,YAAY,GAAG,mBACpB,6LAAC;wBAAI,WAAU;;4BAAuB;4BAClC,eAAe,MAAM,YAAY;;;;;;;oBAKtC,MAAM,SAAS,KAAK,mBACnB,6LAAC;wBAAI,WAAW,CAAC,oBAAoB,EAAE,kBAAkB,MAAM,SAAS,GAAG;;4BAAE;4BACvE,eAAe,MAAM,SAAS;;;;;;;;;;;;;YAOzC,mBAAmB,MAAM,SAAS,CAAC,MAAM,GAAG,mBAC3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,MAAM,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,sBAC1C,6LAAC;gCAEC,WAAU;gCACV,OAAO,SAAS,QAAQ;+BAFnB,SAAS,MAAM;;;;;wBAKvB,MAAM,SAAS,CAAC,MAAM,GAAG,mBACxB,6LAAC;4BAAI,WAAU;;gCAAwB;gCAAE,MAAM,SAAS,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;AAOhF;KAlGgB", "debugId": null}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/calendar/CalendarDayDetail.tsx"], "sourcesContent": ["'use client'\n\nimport { XMarkIcon } from '@heroicons/react/24/outline'\n\ninterface DailyStats {\n  date: string\n  totalTransactions: number\n  totalIncome: number\n  totalExpense: number\n  netAmount: number\n  cardStats: {\n    cardId: string\n    cardName: string\n    cardType: string\n    transactionCount: number\n    income: number\n    expense: number\n    netAmount: number\n  }[]\n}\n\ninterface CalendarDayDetailProps {\n  stats: DailyStats\n  onClose: () => void\n}\n\nconst CARD_TYPE_NAMES = {\n  WECHAT: '微信',\n  ALIPAY: '支付宝',\n  DEBIT_CARD: '借记卡',\n  CREDIT_CARD: '信用卡',\n  CASH: '现金',\n}\n\nexport function CalendarDayDetail({ stats, onClose }: CalendarDayDetailProps) {\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY',\n      minimumFractionDigits: 2,\n    }).format(amount)\n  }\n\n  const formatDate = (dateStr: string) => {\n    const date = new Date(dateStr)\n    return date.toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      weekday: 'long',\n    })\n  }\n\n  const getNetAmountColor = (amount: number) => {\n    if (amount > 0) return 'text-green-600'\n    if (amount < 0) return 'text-red-600'\n    return 'text-gray-500'\n  }\n\n  return (\n    <div className=\"border-t border-gray-200 bg-gray-50 p-6\">\n      {/* 头部 */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-medium text-gray-900\">\n          {formatDate(stats.date)}\n        </h3>\n        <button\n          onClick={onClose}\n          className=\"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-200\"\n        >\n          <XMarkIcon className=\"h-5 w-5\" />\n        </button>\n      </div>\n\n      {/* 日总计 */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-4 gap-4 mb-6\">\n        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n          <p className=\"text-sm text-gray-500\">交易笔数</p>\n          <p className=\"text-xl font-semibold text-blue-600\">{stats.totalTransactions}</p>\n        </div>\n        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n          <p className=\"text-sm text-gray-500\">总收入</p>\n          <p className=\"text-xl font-semibold text-green-600\">\n            {formatCurrency(stats.totalIncome)}\n          </p>\n        </div>\n        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n          <p className=\"text-sm text-gray-500\">总支出</p>\n          <p className=\"text-xl font-semibold text-red-600\">\n            {formatCurrency(stats.totalExpense)}\n          </p>\n        </div>\n        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n          <p className=\"text-sm text-gray-500\">净收入</p>\n          <p className={`text-xl font-semibold ${getNetAmountColor(stats.netAmount)}`}>\n            {formatCurrency(stats.netAmount)}\n          </p>\n        </div>\n      </div>\n\n      {/* 按卡片分组统计 */}\n      {stats.cardStats.length > 0 && (\n        <div>\n          <h4 className=\"text-md font-medium text-gray-900 mb-3\">按卡片统计</h4>\n          <div className=\"space-y-3\">\n            {stats.cardStats.map((cardStat) => (\n              <div\n                key={cardStat.cardId}\n                className=\"bg-white rounded-lg p-4 border border-gray-200\"\n              >\n                <div className=\"flex items-center justify-between mb-2\">\n                  <div>\n                    <h5 className=\"font-medium text-gray-900\">{cardStat.cardName}</h5>\n                    <p className=\"text-sm text-gray-500\">\n                      {CARD_TYPE_NAMES[cardStat.cardType as keyof typeof CARD_TYPE_NAMES] || cardStat.cardType}\n                    </p>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm text-gray-500\">{cardStat.transactionCount} 笔交易</p>\n                    <p className={`font-medium ${getNetAmountColor(cardStat.netAmount)}`}>\n                      {formatCurrency(cardStat.netAmount)}\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"grid grid-cols-2 gap-4 mt-3\">\n                  <div>\n                    <p className=\"text-xs text-gray-500\">收入</p>\n                    <p className=\"text-sm font-medium text-green-600\">\n                      {formatCurrency(cardStat.income)}\n                    </p>\n                  </div>\n                  <div>\n                    <p className=\"text-xs text-gray-500\">支出</p>\n                    <p className=\"text-sm font-medium text-red-600\">\n                      {formatCurrency(cardStat.expense)}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AA0BA,MAAM,kBAAkB;IACtB,QAAQ;IACR,QAAQ;IACR,YAAY;IACZ,aAAa;IACb,MAAM;AACR;AAEO,SAAS,kBAAkB,EAAE,KAAK,EAAE,OAAO,EAA0B;IAC1E,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;YACL,SAAS;QACX;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,GAAG,OAAO;QACvB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,WAAW,MAAM,IAAI;;;;;;kCAExB,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;0CACrC,6LAAC;gCAAE,WAAU;0CAAuC,MAAM,iBAAiB;;;;;;;;;;;;kCAE7E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;0CACrC,6LAAC;gCAAE,WAAU;0CACV,eAAe,MAAM,WAAW;;;;;;;;;;;;kCAGrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;0CACrC,6LAAC;gCAAE,WAAU;0CACV,eAAe,MAAM,YAAY;;;;;;;;;;;;kCAGtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;0CACrC,6LAAC;gCAAE,WAAW,CAAC,sBAAsB,EAAE,kBAAkB,MAAM,SAAS,GAAG;0CACxE,eAAe,MAAM,SAAS;;;;;;;;;;;;;;;;;;YAMpC,MAAM,SAAS,CAAC,MAAM,GAAG,mBACxB,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAI,WAAU;kCACZ,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,yBACpB,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6B,SAAS,QAAQ;;;;;;kEAC5D,6LAAC;wDAAE,WAAU;kEACV,eAAe,CAAC,SAAS,QAAQ,CAAiC,IAAI,SAAS,QAAQ;;;;;;;;;;;;0DAG5F,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;4DAAyB,SAAS,gBAAgB;4DAAC;;;;;;;kEAChE,6LAAC;wDAAE,WAAW,CAAC,YAAY,EAAE,kBAAkB,SAAS,SAAS,GAAG;kEACjE,eAAe,SAAS,SAAS;;;;;;;;;;;;;;;;;;kDAKxC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,6LAAC;wDAAE,WAAU;kEACV,eAAe,SAAS,MAAM;;;;;;;;;;;;0DAGnC,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,6LAAC;wDAAE,WAAU;kEACV,eAAe,SAAS,OAAO;;;;;;;;;;;;;;;;;;;+BA5BjC,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;AAuCpC;KAhHgB", "debugId": null}}, {"offset": {"line": 937, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/calendar/CalendarView.tsx"], "sourcesContent": ["'use client'\n\nimport { CalendarDay } from './CalendarDay'\nimport { CalendarDayDetail } from './CalendarDayDetail'\n\ninterface DailyStats {\n  date: string\n  totalTransactions: number\n  totalIncome: number\n  totalExpense: number\n  netAmount: number\n  cardStats: {\n    cardId: string\n    cardName: string\n    cardType: string\n    transactionCount: number\n    income: number\n    expense: number\n    netAmount: number\n  }[]\n}\n\ninterface CalendarViewProps {\n  year: number\n  month: number\n  calendarStats: { [date: string]: DailyStats }\n  selectedDate: string | null\n  onDateSelect: (date: string) => void\n}\n\nconst WEEKDAYS = ['日', '一', '二', '三', '四', '五', '六']\n\nexport function CalendarView({\n  year,\n  month,\n  calendarStats,\n  selectedDate,\n  onDateSelect,\n}: CalendarViewProps) {\n  // 生成日历网格\n  const generateCalendarDays = () => {\n    const firstDay = new Date(year, month - 1, 1)\n    const lastDay = new Date(year, month, 0)\n    const daysInMonth = lastDay.getDate()\n    const startingDayOfWeek = firstDay.getDay()\n    \n    const days = []\n    \n    // 添加上个月的空白天数\n    for (let i = 0; i < startingDayOfWeek; i++) {\n      days.push(null)\n    }\n    \n    // 添加当月的天数\n    for (let day = 1; day <= daysInMonth; day++) {\n      const dateStr = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`\n      days.push({\n        day,\n        dateStr,\n        stats: calendarStats[dateStr] || null,\n      })\n    }\n    \n    return days\n  }\n\n  const calendarDays = generateCalendarDays()\n  const selectedDayStats = selectedDate ? calendarStats[selectedDate] : null\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n      {/* 日历头部 - 星期 */}\n      <div className=\"grid grid-cols-7 border-b border-gray-200\">\n        {WEEKDAYS.map((weekday, index) => (\n          <div\n            key={weekday}\n            className={`p-4 text-center text-sm font-medium ${\n              index === 0 || index === 6 ? 'text-red-600' : 'text-gray-700'\n            } bg-gray-50`}\n          >\n            {weekday}\n          </div>\n        ))}\n      </div>\n\n      {/* 日历网格 */}\n      <div className=\"grid grid-cols-7\">\n        {calendarDays.map((dayData, index) => (\n          <CalendarDay\n            key={index}\n            dayData={dayData}\n            isSelected={dayData?.dateStr === selectedDate}\n            onSelect={onDateSelect}\n          />\n        ))}\n      </div>\n\n      {/* 选中日期的详细信息 */}\n      {selectedDayStats && (\n        <CalendarDayDetail\n          stats={selectedDayStats}\n          onClose={() => onDateSelect('')}\n        />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AA8BA,MAAM,WAAW;IAAC;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;CAAI;AAE7C,SAAS,aAAa,EAC3B,IAAI,EACJ,KAAK,EACL,aAAa,EACb,YAAY,EACZ,YAAY,EACM;IAClB,SAAS;IACT,MAAM,uBAAuB;QAC3B,MAAM,WAAW,IAAI,KAAK,MAAM,QAAQ,GAAG;QAC3C,MAAM,UAAU,IAAI,KAAK,MAAM,OAAO;QACtC,MAAM,cAAc,QAAQ,OAAO;QACnC,MAAM,oBAAoB,SAAS,MAAM;QAEzC,MAAM,OAAO,EAAE;QAEf,aAAa;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,mBAAmB,IAAK;YAC1C,KAAK,IAAI,CAAC;QACZ;QAEA,UAAU;QACV,IAAK,IAAI,MAAM,GAAG,OAAO,aAAa,MAAO;YAC3C,MAAM,UAAU,GAAG,KAAK,CAAC,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;YACjG,KAAK,IAAI,CAAC;gBACR;gBACA;gBACA,OAAO,aAAa,CAAC,QAAQ,IAAI;YACnC;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IACrB,MAAM,mBAAmB,eAAe,aAAa,CAAC,aAAa,GAAG;IAEtE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;wBAEC,WAAW,CAAC,oCAAoC,EAC9C,UAAU,KAAK,UAAU,IAAI,iBAAiB,gBAC/C,WAAW,CAAC;kCAEZ;uBALI;;;;;;;;;;0BAWX,6LAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,6LAAC,gJAAA,CAAA,cAAW;wBAEV,SAAS;wBACT,YAAY,SAAS,YAAY;wBACjC,UAAU;uBAHL;;;;;;;;;;YASV,kCACC,6LAAC,sJAAA,CAAA,oBAAiB;gBAChB,OAAO;gBACP,SAAS,IAAM,aAAa;;;;;;;;;;;;AAKtC;KA1EgB", "debugId": null}}, {"offset": {"line": 1042, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/calendar/CalendarFilters.tsx"], "sourcesContent": ["'use client'\n\nimport { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'\n\ninterface Card {\n  id: string\n  name: string\n  type: string\n}\n\ninterface CalendarFiltersProps {\n  cards: Card[]\n  selectedCardId: string\n  onCardChange: (cardId: string) => void\n  currentDate: Date\n  onPrevMonth: () => void\n  onNextMonth: () => void\n}\n\nconst CARD_TYPE_NAMES = {\n  WECHAT: '微信',\n  ALIPAY: '支付宝',\n  DEBIT_CARD: '借记卡',\n  CREDIT_CARD: '信用卡',\n  CASH: '现金',\n}\n\nconst MONTH_NAMES = [\n  '一月', '二月', '三月', '四月', '五月', '六月',\n  '七月', '八月', '九月', '十月', '十一月', '十二月'\n]\n\nexport function CalendarFilters({\n  cards,\n  selectedCardId,\n  onCardChange,\n  currentDate,\n  onPrevMonth,\n  onNextMonth,\n}: CalendarFiltersProps) {\n  const year = currentDate.getFullYear()\n  const month = currentDate.getMonth()\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n        {/* 月份导航 */}\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={onPrevMonth}\n            className=\"p-2 rounded-md border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <ChevronLeftIcon className=\"h-5 w-5 text-gray-600\" />\n          </button>\n          \n          <div className=\"text-center min-w-[120px]\">\n            <h2 className=\"text-xl font-semibold text-gray-900\">\n              {year}年 {MONTH_NAMES[month]}\n            </h2>\n          </div>\n          \n          <button\n            onClick={onNextMonth}\n            className=\"p-2 rounded-md border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <ChevronRightIcon className=\"h-5 w-5 text-gray-600\" />\n          </button>\n        </div>\n\n        {/* 卡片筛选器 */}\n        <div className=\"flex items-center space-x-3\">\n          <label className=\"text-sm font-medium text-gray-700\">\n            筛选卡片:\n          </label>\n          <select\n            value={selectedCardId}\n            onChange={(e) => onCardChange(e.target.value)}\n            className=\"rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n          >\n            <option value=\"\">所有卡片</option>\n            {cards.map((card) => (\n              <option key={card.id} value={card.id}>\n                {card.name} ({CARD_TYPE_NAMES[card.type as keyof typeof CARD_TYPE_NAMES] || card.type})\n              </option>\n            ))}\n          </select>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAmBA,MAAM,kBAAkB;IACtB,QAAQ;IACR,QAAQ;IACR,YAAY;IACZ,aAAa;IACb,MAAM;AACR;AAEA,MAAM,cAAc;IAClB;IAAM;IAAM;IAAM;IAAM;IAAM;IAC9B;IAAM;IAAM;IAAM;IAAM;IAAO;CAChC;AAEM,SAAS,gBAAgB,EAC9B,KAAK,EACL,cAAc,EACd,YAAY,EACZ,WAAW,EACX,WAAW,EACX,WAAW,EACU;IACrB,MAAM,OAAO,YAAY,WAAW;IACpC,MAAM,QAAQ,YAAY,QAAQ;IAElC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,gOAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;;;;;;;sCAG7B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;;oCACX;oCAAK;oCAAG,WAAW,CAAC,MAAM;;;;;;;;;;;;sCAI/B,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,kOAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,WAAU;sCAAoC;;;;;;sCAGrD,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4BAC5C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAG;;;;;;gCAChB,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wCAAqB,OAAO,KAAK,EAAE;;4CACjC,KAAK,IAAI;4CAAC;4CAAG,eAAe,CAAC,KAAK,IAAI,CAAiC,IAAI,KAAK,IAAI;4CAAC;;uCAD3E,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlC;KA1DgB", "debugId": null}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/components/calendar/CalendarStats.tsx"], "sourcesContent": ["'use client'\n\nimport { ArrowUpIcon, ArrowDownIcon, CurrencyYenIcon, DocumentTextIcon } from '@heroicons/react/24/outline'\n\ninterface MonthlyTotal {\n  totalTransactions: number\n  totalIncome: number\n  totalExpense: number\n  netAmount: number\n}\n\ninterface CalendarStatsProps {\n  monthlyTotal: MonthlyTotal\n  year: number\n  month: number\n}\n\nexport function CalendarStats({ monthlyTotal, year, month }: CalendarStatsProps) {\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY',\n      minimumFractionDigits: 2,\n    }).format(amount)\n  }\n\n  const stats = [\n    {\n      name: '交易笔数',\n      value: monthlyTotal.totalTransactions.toString(),\n      icon: DocumentTextIcon,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-100',\n    },\n    {\n      name: '总收入',\n      value: formatCurrency(monthlyTotal.totalIncome),\n      icon: ArrowUpIcon,\n      color: 'text-green-600',\n      bgColor: 'bg-green-100',\n    },\n    {\n      name: '总支出',\n      value: formatCurrency(monthlyTotal.totalExpense),\n      icon: ArrowDownIcon,\n      color: 'text-red-600',\n      bgColor: 'bg-red-100',\n    },\n    {\n      name: '净收入',\n      value: formatCurrency(monthlyTotal.netAmount),\n      icon: CurrencyYenIcon,\n      color: monthlyTotal.netAmount >= 0 ? 'text-green-600' : 'text-red-600',\n      bgColor: monthlyTotal.netAmount >= 0 ? 'bg-green-100' : 'bg-red-100',\n    },\n  ]\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n      <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n        {year}年{month}月统计\n      </h3>\n      \n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n        {stats.map((stat) => (\n          <div\n            key={stat.name}\n            className=\"relative overflow-hidden rounded-lg border border-gray-200 p-4\"\n          >\n            <div className=\"flex items-center\">\n              <div className={`flex-shrink-0 rounded-md p-3 ${stat.bgColor}`}>\n                <stat.icon className={`h-6 w-6 ${stat.color}`} />\n              </div>\n              <div className=\"ml-4 flex-1\">\n                <p className=\"text-sm font-medium text-gray-500\">{stat.name}</p>\n                <p className={`text-lg font-semibold ${stat.color}`}>\n                  {stat.value}\n                </p>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAFA;;;AAiBO,SAAS,cAAc,EAAE,YAAY,EAAE,IAAI,EAAE,KAAK,EAAsB;IAC7E,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,OAAO,aAAa,iBAAiB,CAAC,QAAQ;YAC9C,MAAM,kOAAA,CAAA,mBAAgB;YACtB,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO,eAAe,aAAa,WAAW;YAC9C,MAAM,wNAAA,CAAA,cAAW;YACjB,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO,eAAe,aAAa,YAAY;YAC/C,MAAM,4NAAA,CAAA,gBAAa;YACnB,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO,eAAe,aAAa,SAAS;YAC5C,MAAM,gOAAA,CAAA,kBAAe;YACrB,OAAO,aAAa,SAAS,IAAI,IAAI,mBAAmB;YACxD,SAAS,aAAa,SAAS,IAAI,IAAI,iBAAiB;QAC1D;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;;oBACX;oBAAK;oBAAE;oBAAM;;;;;;;0BAGhB,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wBAEC,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAC,6BAA6B,EAAE,KAAK,OAAO,EAAE;8CAC5D,cAAA,6LAAC,KAAK,IAAI;wCAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;8CAE/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAqC,KAAK,IAAI;;;;;;sDAC3D,6LAAC;4CAAE,WAAW,CAAC,sBAAsB,EAAE,KAAK,KAAK,EAAE;sDAChD,KAAK,KAAK;;;;;;;;;;;;;;;;;;uBAVZ,KAAK,IAAI;;;;;;;;;;;;;;;;AAmB1B;KApEgB", "debugId": null}}, {"offset": {"line": 1357, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/src/app/calendar/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useSearchParams } from 'next/navigation'\nimport { useAuth } from '@/hooks/useAuth'\nimport { useToast } from '@/hooks/useToast'\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute'\nimport { CalendarView } from '@/components/calendar/CalendarView'\nimport { CalendarFilters } from '@/components/calendar/CalendarFilters'\nimport { CalendarStats } from '@/components/calendar/CalendarStats'\n\ninterface Card {\n  id: string\n  name: string\n  type: string\n}\n\ninterface DailyStats {\n  date: string\n  totalTransactions: number\n  totalIncome: number\n  totalExpense: number\n  netAmount: number\n  cardStats: {\n    cardId: string\n    cardName: string\n    cardType: string\n    transactionCount: number\n    income: number\n    expense: number\n    netAmount: number\n  }[]\n}\n\ninterface CalendarData {\n  year: number\n  month: number\n  calendarStats: { [date: string]: DailyStats }\n  monthlyTotal: {\n    totalTransactions: number\n    totalIncome: number\n    totalExpense: number\n    netAmount: number\n  }\n  userCards: Card[]\n}\n\nfunction CalendarPageContent() {\n  const { apiCall, isAuthenticated, loading: authLoading } = useAuth()\n  const { showError } = useToast()\n  const searchParams = useSearchParams()\n\n  const [loading, setLoading] = useState(true)\n  const [calendarData, setCalendarData] = useState<CalendarData | null>(null)\n  const [currentDate, setCurrentDate] = useState(new Date())\n  const [selectedCardId, setSelectedCardId] = useState<string>('')\n  const [selectedDate, setSelectedDate] = useState<string | null>(null)\n\n  const fetchCalendarData = async (year: number, month: number, cardId?: string) => {\n    try {\n      setLoading(true)\n      const params = new URLSearchParams({\n        year: year.toString(),\n        month: month.toString(),\n      })\n      \n      if (cardId) {\n        params.append('cardId', cardId)\n      }\n      \n      const response = await apiCall(`/api/transactions/calendar?${params}`)\n      const data = await response.json()\n      \n      if (data.success) {\n        setCalendarData(data.data)\n      } else {\n        showError('获取日历数据失败', data.error)\n      }\n    } catch (error) {\n      console.error('获取日历数据失败:', error)\n      showError('获取日历数据失败', '网络错误，请稍后重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // 从URL参数中读取cardId\n  useEffect(() => {\n    const cardIdFromUrl = searchParams.get('cardId')\n    if (cardIdFromUrl) {\n      setSelectedCardId(cardIdFromUrl)\n    }\n  }, [searchParams])\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      const year = currentDate.getFullYear()\n      const month = currentDate.getMonth() + 1\n      fetchCalendarData(year, month, selectedCardId)\n    }\n  }, [currentDate, selectedCardId, isAuthenticated])\n\n  const handlePrevMonth = () => {\n    setCurrentDate(prev => {\n      const newDate = new Date(prev)\n      newDate.setMonth(prev.getMonth() - 1)\n      return newDate\n    })\n  }\n\n  const handleNextMonth = () => {\n    setCurrentDate(prev => {\n      const newDate = new Date(prev)\n      newDate.setMonth(prev.getMonth() + 1)\n      return newDate\n    })\n  }\n\n  const handleCardFilter = (cardId: string) => {\n    setSelectedCardId(cardId)\n  }\n\n  const handleDateSelect = (date: string) => {\n    setSelectedDate(selectedDate === date ? null : date)\n  }\n\n  if (authLoading || loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">\n            {authLoading ? '验证用户身份中...' : '加载日历数据中...'}\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <p className=\"text-gray-600\">请先登录以查看日历数据</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* 页面标题 */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">交易日历</h1>\n          <p className=\"mt-2 text-gray-600\">查看每日交易统计和趋势</p>\n        </div>\n\n        {/* 筛选器 */}\n        {calendarData && (\n          <CalendarFilters\n            cards={calendarData.userCards}\n            selectedCardId={selectedCardId}\n            onCardChange={handleCardFilter}\n            currentDate={currentDate}\n            onPrevMonth={handlePrevMonth}\n            onNextMonth={handleNextMonth}\n          />\n        )}\n\n        {/* 月度统计 */}\n        {calendarData && (\n          <CalendarStats\n            monthlyTotal={calendarData.monthlyTotal}\n            year={calendarData.year}\n            month={calendarData.month}\n          />\n        )}\n\n        {/* 日历视图 */}\n        {calendarData && (\n          <CalendarView\n            year={calendarData.year}\n            month={calendarData.month}\n            calendarStats={calendarData.calendarStats}\n            selectedDate={selectedDate}\n            onDateSelect={handleDateSelect}\n          />\n        )}\n      </div>\n    </div>\n  )\n}\n\nexport default function CalendarPage() {\n  return (\n    <ProtectedRoute>\n      <CalendarPageContent />\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AA+CA,SAAS;;IACP,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACjE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAEnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,MAAM,oBAAoB,OAAO,MAAc,OAAe;QAC5D,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,KAAK,QAAQ;gBACnB,OAAO,MAAM,QAAQ;YACvB;YAEA,IAAI,QAAQ;gBACV,OAAO,MAAM,CAAC,UAAU;YAC1B;YAEA,MAAM,WAAW,MAAM,QAAQ,CAAC,2BAA2B,EAAE,QAAQ;YACrE,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB,KAAK,IAAI;YAC3B,OAAO;gBACL,UAAU,YAAY,KAAK,KAAK;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,UAAU,YAAY;QACxB,SAAU;YACR,WAAW;QACb;IACF;IAEA,kBAAkB;IAClB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM,gBAAgB,aAAa,GAAG,CAAC;YACvC,IAAI,eAAe;gBACjB,kBAAkB;YACpB;QACF;wCAAG;QAAC;KAAa;IAEjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,iBAAiB;gBACnB,MAAM,OAAO,YAAY,WAAW;gBACpC,MAAM,QAAQ,YAAY,QAAQ,KAAK;gBACvC,kBAAkB,MAAM,OAAO;YACjC;QACF;wCAAG;QAAC;QAAa;QAAgB;KAAgB;IAEjD,MAAM,kBAAkB;QACtB,eAAe,CAAA;YACb,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,QAAQ,CAAC,KAAK,QAAQ,KAAK;YACnC,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB;QACtB,eAAe,CAAA;YACb,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,QAAQ,CAAC,KAAK,QAAQ,KAAK;YACnC,OAAO;QACT;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;IACpB;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB,iBAAiB,OAAO,OAAO;IACjD;IAEA,IAAI,eAAe,SAAS;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCACV,cAAc,eAAe;;;;;;;;;;;;;;;;;IAKxC;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;gBAInC,8BACC,6LAAC,oJAAA,CAAA,kBAAe;oBACd,OAAO,aAAa,SAAS;oBAC7B,gBAAgB;oBAChB,cAAc;oBACd,aAAa;oBACb,aAAa;oBACb,aAAa;;;;;;gBAKhB,8BACC,6LAAC,kJAAA,CAAA,gBAAa;oBACZ,cAAc,aAAa,YAAY;oBACvC,MAAM,aAAa,IAAI;oBACvB,OAAO,aAAa,KAAK;;;;;;gBAK5B,8BACC,6LAAC,iJAAA,CAAA,eAAY;oBACX,MAAM,aAAa,IAAI;oBACvB,OAAO,aAAa,KAAK;oBACzB,eAAe,aAAa,aAAa;oBACzC,cAAc;oBACd,cAAc;;;;;;;;;;;;;;;;;AAM1B;GAjJS;;QACoD,0HAAA,CAAA,UAAO;QAC5C,4HAAA,CAAA,WAAQ;QACT,qIAAA,CAAA,kBAAe;;;KAH7B;AAmJM,SAAS;IACtB,qBACE,6LAAC,+IAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC;;;;;;;;;;AAGP;MANwB", "debugId": null}}, {"offset": {"line": 1622, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1629, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/node_modules/%40heroicons/react/24/outline/esm/ChevronLeftIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ChevronLeftIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15.75 19.5 8.25 12l7.5-7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronLeftIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1671, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/node_modules/%40heroicons/react/24/outline/esm/ChevronRightIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ChevronRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronRightIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,iBAAiB,EACxB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1713, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/node_modules/%40heroicons/react/24/outline/esm/ArrowUpIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ArrowUpIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowUpIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,YAAY,EACnB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1755, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/node_modules/%40heroicons/react/24/outline/esm/ArrowDownIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ArrowDownIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowDownIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,cAAc,EACrB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1797, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/node_modules/%40heroicons/react/24/outline/esm/CurrencyYenIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction CurrencyYenIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m9 7.5 3 4.5m0 0 3-4.5M12 12v5.25M15 12H9m6 3H9m12-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CurrencyYenIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1839, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Data/projects/PersonalFin/personal-finance/node_modules/%40heroicons/react/24/outline/esm/DocumentTextIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction DocumentTextIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(DocumentTextIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,iBAAiB,EACxB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}