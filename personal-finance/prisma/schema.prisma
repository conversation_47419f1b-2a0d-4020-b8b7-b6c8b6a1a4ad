// Personal Finance Tracking System Database Schema
// 个人+家庭财务追踪系统数据库模型

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户模型 - 支持家庭成员管理
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String   // 加密后的密码
  name      String
  avatar    String?  // 头像URL
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 家庭关联
  familyId  String?
  family    Family? @relation(fields: [familyId], references: [id], onDelete: SetNull)

  // 在家庭中的角色
  familyRole FamilyRole @default(MEMBER)

  // 关联关系
  cards         Card[]
  transactions  Transaction[]
  accountBooks  AccountBook[]

  // 创建的家庭
  createdFamilies Family[] @relation("FamilyCreator")

  // 发送的邀请
  sentInvitations     FamilyInvitation[] @relation("InvitationSender")
  // 接收的邀请
  receivedInvitations FamilyInvitation[] @relation("InvitationReceiver")

  // 卡片权限
  cardPermissions     CardPermission[]
  grantedPermissions  CardPermission[] @relation("PermissionGranter")

  @@map("users")
}

// 家庭角色枚举
enum FamilyRole {
  OWNER   // 家庭创建者/所有者
  ADMIN   // 管理员
  MEMBER  // 普通成员
}

// 家庭模型
model Family {
  id          String   @id @default(cuid())
  name        String   // 家庭名称
  description String?  // 家庭描述
  avatar      String?  // 家庭头像
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 创建者
  creatorId String
  creator   User   @relation("FamilyCreator", fields: [creatorId], references: [id], onDelete: Cascade)

  // 家庭成员
  members     User[]
  invitations FamilyInvitation[]

  @@map("families")
}

// 家庭邀请模型
model FamilyInvitation {
  id        String            @id @default(cuid())
  email     String            // 被邀请人邮箱
  role      FamilyRole        @default(MEMBER)
  status    InvitationStatus  @default(PENDING)
  message   String?           // 邀请消息
  expiresAt DateTime          // 过期时间
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt

  // 家庭
  familyId String
  family   Family @relation(fields: [familyId], references: [id], onDelete: Cascade)

  // 邀请发送者
  senderId String
  sender   User   @relation("InvitationSender", fields: [senderId], references: [id], onDelete: Cascade)

  // 被邀请者（如果已注册）
  receiverId String?
  receiver   User?   @relation("InvitationReceiver", fields: [receiverId], references: [id], onDelete: SetNull)

  @@unique([familyId, email])
  @@map("family_invitations")
}

// 邀请状态枚举
enum InvitationStatus {
  PENDING   // 待处理
  ACCEPTED  // 已接受
  DECLINED  // 已拒绝
  EXPIRED   // 已过期
}

// 卡片类型枚举
enum CardType {
  DEBIT_CARD    // 借记卡/储蓄卡
  CREDIT_CARD   // 信用卡
  WECHAT        // 微信
  ALIPAY        // 支付宝
  CASH          // 现金
  OTHER         // 其他
}

// 卡片模型 - 支持多种支付方式
model Card {
  id          String   @id @default(cuid())
  name        String   // 卡片名称，如"招商银行储蓄卡"
  type        CardType
  accountNo   String?  // 账号/卡号（部分显示）
  bankName    String?  // 银行名称
  balance     Float    @default(0) // 当前余额
  isActive    Boolean  @default(true)
  description String?
  isShared    Boolean  @default(false) // 是否与家庭成员共享
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 卡片所有者
  userId       String
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  // 关联关系
  transactions Transaction[]
  billImports  BillImport[]

  // 卡片权限
  permissions CardPermission[]

  @@map("cards")
}

// 卡片权限模型 - 控制家庭成员对卡片的访问权限
model CardPermission {
  id         String           @id @default(cuid())
  permission CardPermissionType
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt

  // 卡片
  cardId String
  card   Card   @relation(fields: [cardId], references: [id], onDelete: Cascade)

  // 被授权用户
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // 授权者
  grantedById String
  grantedBy   User   @relation("PermissionGranter", fields: [grantedById], references: [id], onDelete: Cascade)

  @@unique([cardId, userId])
  @@map("card_permissions")
}

// 卡片权限类型枚举
enum CardPermissionType {
  VIEW      // 查看权限
  IMPORT    // 导入账单权限
  EDIT      // 编辑权限
  MANAGE    // 管理权限（包括删除、授权等）
}

// 交易类型枚举
enum TransactionType {
  INCOME    // 收入
  EXPENSE   // 支出
  TRANSFER  // 转账
}

// 交易状态枚举
enum TransactionStatus {
  PENDING     // 待确认
  CONFIRMED   // 已确认
  CANCELLED   // 已取消
  LINKED      // 已关联（避免重复记账）
}

// 交易记录模型
model Transaction {
  id              String            @id @default(cuid())
  externalId      String?           // 外部交易ID（用于去重）
  type            TransactionType
  status          TransactionStatus @default(CONFIRMED)
  amount          Float             // 金额
  balance         Float?            // 交易后余额（银行账单中的余额信息）
  description     String            // 交易描述
  category        String?           // 分类
  subcategory     String?           // 子分类
  transactionDate DateTime          // 交易时间
  location        String?           // 交易地点
  notes           String?           // 备注
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // 关联关系
  userId    String
  user      User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  cardId    String?
  card      Card?  @relation(fields: [cardId], references: [id], onDelete: Cascade)

  // 账单导入关联
  billImportId String?
  billImport   BillImport? @relation(fields: [billImportId], references: [id], onDelete: SetNull)

  // 交易关联（用于处理微信关联银行卡支付等）
  linkedTransactionId String?
  linkedTransaction   Transaction? @relation("TransactionLink", fields: [linkedTransactionId], references: [id])
  linkingTransactions Transaction[] @relation("TransactionLink")

  // 账本关联
  accountBookEntries AccountBookEntry[]

  // 对账关联
  sourceReconciliations ReconciliationRecord[] @relation("SourceReconciliation")
  targetReconciliations ReconciliationRecord[] @relation("TargetReconciliation")

  @@unique([externalId, cardId]) // 同一卡片的外部ID唯一
  @@map("transactions")
}

// 账单导入状态枚举
enum BillImportStatus {
  PENDING     // 待处理
  PROCESSING  // 处理中
  COMPLETED   // 已完成
  FAILED      // 失败
}

// 账单导入模型
model BillImport {
  id          String           @id @default(cuid())
  fileName    String           // 原始文件名
  fileType    String           // 文件类型 (csv, xlsx, pdf等)
  fileSize    Int              // 文件大小
  status      BillImportStatus @default(PENDING)
  totalCount  Int              @default(0) // 总记录数
  successCount Int             @default(0) // 成功导入数
  errorCount  Int              @default(0) // 错误数
  errorLog    String?          // 错误日志
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  // 关联关系
  cardId       String
  card         Card          @relation(fields: [cardId], references: [id], onDelete: Cascade)
  transactions Transaction[]

  @@map("bill_imports")
}

// 账本类型枚举
enum AccountBookType {
  DEFAULT    // 默认账本（手工记账）
  CUSTOM     // 自定义账本
  AGGREGATE  // 聚合账本
}

// 账本模型
model AccountBook {
  id          String          @id @default(cuid())
  name        String          // 账本名称
  type        AccountBookType
  description String?         // 描述
  isActive    Boolean         @default(true)
  filterRules Json?           // 筛选规则（JSON格式）
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  // 关联关系
  userId   String
  user     User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  entries  AccountBookEntry[]

  @@map("account_books")
}

// 账本条目模型（多对多关系表）
model AccountBookEntry {
  id            String      @id @default(cuid())
  createdAt     DateTime    @default(now())

  // 关联关系
  accountBookId String
  accountBook   AccountBook @relation(fields: [accountBookId], references: [id], onDelete: Cascade)
  transactionId String
  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  @@unique([accountBookId, transactionId])
  @@map("account_book_entries")
}

// 分类模型
model Category {
  id          String   @id @default(cuid())
  name        String   // 分类名称
  parentId    String?  // 父分类ID（支持层级分类）
  icon        String?  // 图标
  color       String?  // 颜色
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 自关联
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")

  @@map("categories")
}

// 对账状态枚举
enum ReconciliationStatus {
  PENDING    // 待对账
  MATCHED    // 已匹配
  UNMATCHED  // 未匹配
  MANUAL     // 手工处理
}

// 对账记录模型
model ReconciliationRecord {
  id                String                @id @default(cuid())
  status            ReconciliationStatus  @default(PENDING)
  matchScore        Float?                // 匹配分数 (0-1)
  matchReason       String?               // 匹配原因
  notes             String?               // 备注
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt

  // 关联的交易记录
  sourceTransactionId String?
  sourceTransaction   Transaction? @relation("SourceReconciliation", fields: [sourceTransactionId], references: [id])
  targetTransactionId String?
  targetTransaction   Transaction? @relation("TargetReconciliation", fields: [targetTransactionId], references: [id])

  @@map("reconciliation_records")
}

// 系统配置模型
model SystemConfig {
  id        String   @id @default(cuid())
  key       String   @unique // 配置键
  value     String   // 配置值
  type      String   // 值类型 (string, number, boolean, json)
  category  String   // 配置分类
  description String? // 描述
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_configs")
}

// 审计日志模型
model AuditLog {
  id        String   @id @default(cuid())
  action    String   // 操作类型
  entity    String   // 实体类型
  entityId  String   // 实体ID
  oldData   Json?    // 旧数据
  newData   Json?    // 新数据
  userId    String?  // 操作用户
  ipAddress String?  // IP地址
  userAgent String?  // 用户代理
  createdAt DateTime @default(now())

  @@map("audit_logs")
}
