"use client";import{useFocusRing as P}from"@react-aria/focus";import{useHover as c}from"@react-aria/interactions";import{useMemo as g}from"react";import{useId as v}from'../../hooks/use-id.js';import{useDisabled as A}from'../../internal/disabled.js';import{useProvidedId as _}from'../../internal/id.js';import{forwardRefWithAs as R,mergeProps as D,useRender as E}from'../../utils/render.js';import{useDescribedBy as F}from'../description/description.js';import{useLabelledBy as U}from'../label/label.js';let x="input";function h(p,s){let a=v(),l=_(),i=A(),{id:d=l||`headlessui-input-${a}`,disabled:e=i||!1,autoFocus:o=!1,invalid:t=!1,...u}=p,f=U(),m=F(),{isFocused:r,focusProps:T}=P({autoFocus:o}),{isHovered:n,hoverProps:b}=c({isDisabled:e}),y=D({ref:s,id:d,"aria-labelledby":f,"aria-describedby":m,"aria-invalid":t?"true":void 0,disabled:e||void 0,autoFocus:o},T,b),I=g(()=>({disabled:e,invalid:t,hover:n,focus:r,autofocus:o}),[e,t,n,r,o]);return E()({ourProps:y,theirProps:u,slot:I,defaultTag:x,name:"Input"})}let S=R(h);export{S as Input};
