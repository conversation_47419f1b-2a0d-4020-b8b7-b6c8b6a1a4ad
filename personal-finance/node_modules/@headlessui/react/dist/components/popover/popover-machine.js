var S=Object.defineProperty;var f=(t,n,e)=>n in t?S(t,n,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[n]=e;var p=(t,n,e)=>(f(t,typeof n!="symbol"?n+"":n,e),e);import{Machine as m}from'../../machine.js';import{stackMachines as P}from'../../machines/stack-machine.js';import*as s from'../../utils/dom.js';import{getFocusableElements as b}from'../../utils/focus-management.js';import{match as y}from'../../utils/match.js';var I=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(I||{}),M=(l=>(l[l.OpenPopover=0]="OpenPopover",l[l.ClosePopover=1]="ClosePopover",l[l.SetButton=2]="SetButton",l[l.SetButtonId=3]="SetButtonId",l[l.SetPanel=4]="SetPanel",l[l.SetPanelId=5]="SetPanelId",l))(M||{});let T={[0]:t=>t.popoverState===0?t:{...t,popoverState:0,__demoMode:!1},[1](t){return t.popoverState===1?t:{...t,popoverState:1,__demoMode:!1}},[2](t,n){return t.button===n.button?t:{...t,button:n.button}},[3](t,n){return t.buttonId===n.buttonId?t:{...t,buttonId:n.buttonId}},[4](t,n){return t.panel===n.panel?t:{...t,panel:n.panel}},[5](t,n){return t.panelId===n.panelId?t:{...t,panelId:n.panelId}}};class i extends m{constructor(e){super(e);p(this,"actions",{close:()=>this.send({type:1}),refocusableClose:e=>{this.actions.close();let o=(()=>e?s.isHTMLElement(e)?e:"current"in e&&s.isHTMLElement(e.current)?e.current:this.state.button:this.state.button)();o==null||o.focus()},open:()=>this.send({type:0}),setButtonId:e=>this.send({type:3,buttonId:e}),setButton:e=>this.send({type:2,button:e}),setPanelId:e=>this.send({type:5,panelId:e}),setPanel:e=>this.send({type:4,panel:e})});p(this,"selectors",{isPortalled:e=>{if(!e.button||!e.panel)return!1;for(let r of document.querySelectorAll("body > *"))if(Number(r==null?void 0:r.contains(e.button))^Number(r==null?void 0:r.contains(e.panel)))return!0;let o=b(),u=o.indexOf(e.button),a=(u+o.length-1)%o.length,l=(u+1)%o.length,d=o[a],c=o[l];return!e.panel.contains(d)&&!e.panel.contains(c)}});{let o=this.state.id,u=P.get(null);this.on(0,()=>u.actions.push(o)),this.on(1,()=>u.actions.pop(o))}}static new({id:e,__demoMode:o=!1}){return new i({id:e,__demoMode:o,popoverState:o?0:1,buttons:{current:[]},button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:{current:null},afterPanelSentinel:{current:null},afterButtonSentinel:{current:null}})}reduce(e,o){return y(o.type,T,e,o)}}export{M as ActionTypes,i as PopoverMachine,I as PopoverStates};
