var p=Object.defineProperty;var h=(t,e,r)=>e in t?p(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var f=(t,e,r)=>(h(t,typeof e!="symbol"?e+"":e,r),r),b=(t,e,r)=>{if(!e.has(t))throw TypeError("Cannot "+r)};var n=(t,e,r)=>(b(t,e,"read from private field"),r?r.call(t):e.get(t)),c=(t,e,r)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,r)},u=(t,e,r,s)=>(b(t,e,"write to private field"),s?s.call(t,r):e.set(t,r),r);var i,a,o;import{DefaultMap as v}from'./utils/default-map.js';import{disposables as S}from'./utils/disposables.js';class E{constructor(e){c(this,i,{});c(this,a,new v(()=>new Set));c(this,o,new Set);f(this,"disposables",S());u(this,i,e)}dispose(){this.disposables.dispose()}get state(){return n(this,i)}subscribe(e,r){let s={selector:e,callback:r,current:e(n(this,i))};return n(this,o).add(s),this.disposables.add(()=>{n(this,o).delete(s)})}on(e,r){return n(this,a).get(e).add(r),this.disposables.add(()=>{n(this,a).get(e).delete(r)})}send(e){let r=this.reduce(n(this,i),e);if(r!==n(this,i)){u(this,i,r);for(let s of n(this,o)){let l=s.selector(n(this,i));j(s.current,l)||(s.current=l,s.callback(l))}for(let s of n(this,a).get(e.type))s(n(this,i),e)}}}i=new WeakMap,a=new WeakMap,o=new WeakMap;function j(t,e){return Object.is(t,e)?!0:typeof t!="object"||t===null||typeof e!="object"||e===null?!1:Array.isArray(t)&&Array.isArray(e)?t.length!==e.length?!1:d(t[Symbol.iterator](),e[Symbol.iterator]()):t instanceof Map&&e instanceof Map||t instanceof Set&&e instanceof Set?t.size!==e.size?!1:d(t.entries(),e.entries()):y(t)&&y(e)?d(Object.entries(t)[Symbol.iterator](),Object.entries(e)[Symbol.iterator]()):!1}function d(t,e){do{let r=t.next(),s=e.next();if(r.done&&s.done)return!0;if(r.done||s.done||!Object.is(r.value,s.value))return!1}while(!0)}function y(t){if(Object.prototype.toString.call(t)!=="[object Object]")return!1;let e=Object.getPrototypeOf(t);return e===null||Object.getPrototypeOf(e)===null}function x(t){let[e,r]=t(),s=S();return(...l)=>{e(...l),s.dispose(),s.microTask(r)}}export{E as Machine,x as batch,j as shallowEqual};
