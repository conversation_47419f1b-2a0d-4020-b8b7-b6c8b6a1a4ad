import*as g from'./dom.js';let a=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function o(e){var l,n;let i=(l=e.innerText)!=null?l:"",t=e.cloneNode(!0);if(!g.isHTMLElement(t))return i;let u=!1;for(let f of t.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))f.remove(),u=!0;let r=u?(n=t.innerText)!=null?n:"":i;return a.test(r)&&(r=r.replace(a,"")),r}function F(e){let i=e.getAttribute("aria-label");if(typeof i=="string")return i.trim();let t=e.getAttribute("aria-labelledby");if(t){let u=t.split(" ").map(r=>{let l=document.getElementById(r);if(l){let n=l.getAttribute("aria-label");return typeof n=="string"?n.trim():o(l).trim()}return null}).filter(Boolean);if(u.length>0)return u.join(", ")}return o(e).trim()}export{F as getTextValue};
