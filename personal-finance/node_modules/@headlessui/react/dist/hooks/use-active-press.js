import{useRef as a,useState as m}from"react";import{getOwnerDocument as d}from'../utils/owner.js';import{useDisposables as g}from'./use-disposables.js';import{useEvent as u}from'./use-event.js';function E(e){let t=e.width/2,n=e.height/2;return{top:e.clientY-n,right:e.clientX+t,bottom:e.clientY+n,left:e.clientX-t}}function P(e,t){return!(!e||!t||e.right<t.left||e.left>t.right||e.bottom<t.top||e.top>t.bottom)}function w({disabled:e=!1}={}){let t=a(null),[n,l]=m(!1),r=g(),o=u(()=>{t.current=null,l(!1),r.dispose()}),f=u(s=>{if(r.dispose(),t.current===null){t.current=s.currentTarget,l(!0);{let i=d(s.currentTarget);r.addEventListener(i,"pointerup",o,!1),r.addEventListener(i,"pointermove",c=>{if(t.current){let p=E(c);l(P(p,t.current.getBoundingClientRect()))}},!1),r.addEventListener(i,"pointercancel",o,!1)}}});return{pressed:n,pressProps:e?{}:{onPointerDown:f,onPointerUp:o,onClick:o}}}export{w as useActivePress};
