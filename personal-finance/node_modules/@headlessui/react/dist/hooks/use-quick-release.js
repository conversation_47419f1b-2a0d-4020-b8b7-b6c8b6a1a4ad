import{useRef as d}from"react";import*as l from'../utils/dom.js';import{useDocumentEvent as c}from'./use-document-event.js';var m=(e=>(e[e.Ignore=0]="Ignore",e[e.Select=1]="Select",e[e.Close=2]="Close",e))(m||{});const g={Ignore:{kind:0},Select:r=>({kind:1,target:r}),Close:{kind:2}},E=200;function k(r,{trigger:n,action:s,close:e,select:a}){let o=d(null);c(r&&n!==null,"pointerdown",t=>{l.isNode(t==null?void 0:t.target)&&n!=null&&n.contains(t.target)&&(o.current=new Date)}),c(r&&n!==null,"pointerup",t=>{if(o.current===null||!l.isHTMLorSVGElement(t.target))return;let i=s(t),u=new Date().getTime()-o.current.getTime();switch(o.current=null,i.kind){case 0:return;case 1:{u>E&&(a(i.target),e());break}case 2:{e();break}}},{capture:!0})}export{g as Action,k as useQuickRelease};
