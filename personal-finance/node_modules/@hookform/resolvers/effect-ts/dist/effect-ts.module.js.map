{"version": 3, "file": "effect-ts.module.js", "sources": ["../src/effect-ts.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { Effect, Schema } from 'effect';\nimport { ArrayFormatter, decodeUnknown } from 'effect/ParseResult';\nimport { ParseOptions } from 'effect/SchemaAST';\nimport {\n  type FieldError,\n  FieldValues,\n  Resolver,\n  appendErrors,\n} from 'react-hook-form';\n\nexport function effectTsResolver<Input extends FieldValues, Context, Output>(\n  schema: Schema.Schema<Output, Input>,\n  schemaOptions?: ParseOptions,\n  resolverOptions?: {\n    mode?: 'async' | 'sync';\n    raw?: false;\n  },\n): Resolver<Input, Context, Output>;\n\nexport function effectTsResolver<Input extends FieldValues, Context, Output>(\n  schema: Schema.Schema<Output, Input>,\n  schemaOptions: ParseOptions | undefined,\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver for react-hook-form using Effect.ts schema validation\n * @param {Schema.Schema<TFieldValues, I>} schema - The Effect.ts schema to validate against\n * @param {ParseOptions} [schemaOptions] - Optional Effect.ts validation options\n * @returns {Resolver<Schema.Schema.Type<typeof schema>>} A resolver function compatible with react-hook-form\n * @example\n * const schema = Schema.Struct({\n *   name: Schema.String,\n *   age: Schema.Number\n * });\n *\n * useForm({\n *   resolver: effectTsResolver(schema)\n * });\n */\nexport function effectTsResolver<Input extends FieldValues, Context, Output>(\n  schema: Schema.Schema<Output, Input>,\n  schemaOptions: ParseOptions = { errors: 'all', onExcessProperty: 'ignore' },\n): Resolver<Input, Context, Output | Input> {\n  return (values, _, options) => {\n    return decodeUnknown(\n      schema,\n      schemaOptions,\n    )(values).pipe(\n      Effect.catchAll((parseIssue) =>\n        Effect.flip(ArrayFormatter.formatIssue(parseIssue)),\n      ),\n      Effect.mapError((issues) => {\n        const validateAllFieldCriteria =\n          !options.shouldUseNativeValidation && options.criteriaMode === 'all';\n\n        const errors = issues.reduce(\n          (acc, error) => {\n            const key = error.path.join('.');\n\n            if (!acc[key]) {\n              acc[key] = { message: error.message, type: error._tag };\n            }\n\n            if (validateAllFieldCriteria) {\n              const types = acc[key].types;\n              const messages = types && types[String(error._tag)];\n\n              acc[key] = appendErrors(\n                key,\n                validateAllFieldCriteria,\n                acc,\n                error._tag,\n                messages\n                  ? ([] as string[]).concat(messages as string[], error.message)\n                  : error.message,\n              ) as FieldError;\n            }\n\n            return acc;\n          },\n          {} as Record<string, FieldError>,\n        );\n\n        return toNestErrors(errors, options);\n      }),\n      Effect.tap(() =>\n        Effect.sync(\n          () =>\n            options.shouldUseNativeValidation &&\n            validateFieldsNatively({}, options),\n        ),\n      ),\n      Effect.match({\n        onFailure: (errors) => ({ errors, values: {} }),\n        onSuccess: (result) => ({ errors: {}, values: result }),\n      }),\n      Effect.runPromise,\n    );\n  };\n}\n"], "names": ["effectTsResolver", "schema", "schemaOptions", "errors", "onExcessProperty", "values", "_", "options", "decodeUnknown", "pipe", "Effect", "catchAll", "parseIssue", "flip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formatIssue", "mapError", "issues", "validateAllFieldCriteria", "shouldUseNativeValidation", "criteriaMode", "reduce", "acc", "error", "key", "path", "join", "message", "type", "_tag", "types", "messages", "String", "appendErrors", "concat", "toNestErrors", "tap", "sync", "validateFieldsNatively", "match", "onFailure", "onSuccess", "result", "runPromise"], "mappings": "qOA4CgB,SAAAA,EACdC,EACAC,GAEA,YAFAA,IAAAA,IAAAA,EAA8B,CAAEC,OAAQ,MAAOC,iBAAkB,WAE1D,SAACC,EAAQC,EAAGC,GACjB,OAAOC,EACLP,EACAC,EAFKM,CAGLH,GAAQI,KACRC,EAAOC,SAAS,SAACC,GACf,OAAAF,EAAOG,KAAKC,EAAeC,YAAYH,GAAY,GAErDF,EAAOM,SAAS,SAACC,GACf,IAAMC,GACHX,EAAQY,2BAAsD,QAAzBZ,EAAQa,aAE1CjB,EAASc,EAAOI,OACpB,SAACC,EAAKC,GACJ,IAAMC,EAAMD,EAAME,KAAKC,KAAK,KAM5B,GAJKJ,EAAIE,KACPF,EAAIE,GAAO,CAAEG,QAASJ,EAAMI,QAASC,KAAML,EAAMM,OAG/CX,EAA0B,CAC5B,IAAMY,EAAQR,EAAIE,GAAKM,MACjBC,EAAWD,GAASA,EAAME,OAAOT,EAAMM,OAE7CP,EAAIE,GAAOS,EACTT,EACAN,EACAI,EACAC,EAAMM,KACNE,EACK,GAAgBG,OAAOH,EAAsBR,EAAMI,SACpDJ,EAAMI,QAEd,CAEA,OAAOL,CACT,EACA,CAAgC,GAGlC,OAAOa,EAAahC,EAAQI,EAC9B,GACAG,EAAO0B,IAAI,WAAA,OACT1B,EAAO2B,KACL,kBACE9B,EAAQY,2BACRmB,EAAuB,GAAI/B,EAAQ,EACtC,GAEHG,EAAO6B,MAAM,CACXC,UAAW,SAACrC,GAAM,MAAM,CAAEA,OAAAA,EAAQE,OAAQ,GAAI,EAC9CoC,UAAW,SAACC,GAAY,MAAA,CAAEvC,OAAQ,CAAA,EAAIE,OAAQqC,EAAQ,IAExDhC,EAAOiC,WAEX,CACF"}