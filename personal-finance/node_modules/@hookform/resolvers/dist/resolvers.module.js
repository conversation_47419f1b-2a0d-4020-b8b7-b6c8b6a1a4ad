import{get as r,set as e}from"react-hook-form";var t=function(e,t,i){if(e&&"reportValidity"in e){var n=r(i,t);e.setCustomValidity(n&&n.message||""),e.reportValidity()}},i=function(r,e){var i=function(i){var n=e.fields[i];n&&n.ref&&"reportValidity"in n.ref?t(n.ref,i,r):n&&n.refs&&n.refs.forEach(function(e){return t(e,i,r)})};for(var n in e.fields)i(n)},n=function(t,n){n.shouldUseNativeValidation&&i(t,n);var a={};for(var f in t){var s=r(n.fields,f),c=Object.assign(t[f]||{},{ref:s&&s.ref});if(o(n.names||Object.keys(t),f)){var u=Object.assign({},r(a,f));e(u,"root",c),e(a,f,u)}else e(a,f,c)}return a},o=function(r,e){var t=a(e);return r.some(function(r){return a(r).match("^"+t+"\\.\\d+")})};function a(r){return r.replace(/\]|\[/g,"")}export{n as toNestErrors,i as validateFieldsNatively};
//# sourceMappingURL=resolvers.module.js.map
