!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers"],r):r((e||self)["hookformResolversfluentvalidation-ts"]={},e.hookformResolvers)}(this,function(e,r){function o(e,r,t){void 0===t&&(t=[]);var n=function(){var n=[].concat(t,[i]),s=e[i];Array.isArray(s)?s.forEach(function(e,t){o(e,r,[].concat(n,[t]))}):"object"==typeof s&&null!==s?o(s,r,n):"string"==typeof s&&(r[n.join(".")]={type:"validation",message:s})};for(var i in e)n()}function t(e,r){var t={};return o(e,t),t}e.fluentAsyncValidationResolver=function(e){return function(o,n,i){try{return Promise.resolve(e.validateAsync(o)).then(function(e){var n=0===Object.keys(e).length;return i.shouldUseNativeValidation&&r.validateFieldsNatively({},i),n?{values:o,errors:{}}:{values:{},errors:r.toNestErrors(t(e),i)}})}catch(e){return Promise.reject(e)}}},e.fluentValidationResolver=function(e){return function(o,n,i){try{var s=e.validate(o),a=0===Object.keys(s).length;return i.shouldUseNativeValidation&&r.validateFieldsNatively({},i),Promise.resolve(a?{values:o,errors:{}}:{values:{},errors:r.toNestErrors(t(s),i)})}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=fluentvalidation-ts.umd.js.map
