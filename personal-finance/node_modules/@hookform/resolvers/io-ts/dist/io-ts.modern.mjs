import{toNestErrors as e,validateFieldsNatively as t}from"@hookform/resolvers";import*as r from"fp-ts/Either";import{pipe as o,flow as a,absurd as n,identity as s,not as p}from"fp-ts/function";import*as i from"fp-ts/Option";import*as m from"fp-ts/ReadonlyArray";import*as f from"fp-ts/ReadonlyRecord";import*as l from"fp-ts/Semigroup";import{TaggedUnionType as c,UnionType as u,IntersectionType as d,ExactType as y,RefinementType as g}from"io-ts";function v(){return v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)({}).hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e},v.apply(null,arguments)}const h=e=>e.reduce((e,t,a)=>o(t,r.fold(e=>`${a>0?".":""}${e}`,e=>`[${e}]`),t=>`${e}${t}`),""),O=["path"],b=[c,u,d,y,g],x=e=>{const t=o(f=e.context,m.filterMapWithIndex((e,t)=>{const r=e-1,o=-1===r?void 0:f[r];return void 0===o||b.some(e=>o.type instanceof e)?i.none:i.some(t)}),m.map(({key:e})=>e),m.map(e=>o(e,e=>parseInt(e,10),r.fromPredicate(p(Number.isNaN),()=>e))),m.toArray,h);var f;return{message:o(e.message,r.fromNullable(e.context),r.mapLeft(a(m.last,i.map(e=>`expected ${e.type.name} but got ${JSON.stringify(e.actual)}`),i.getOrElseW(()=>n("Error context is missing name")))),r.getOrElseW(s)),type:o(e.context,m.last,i.map(e=>e.type.name),i.getOrElse(()=>"unknown")),path:t}},N=e=>o(e,m.map(e=>({[e.path]:{type:e.type,message:e.message}})),e=>l.fold({concat:(e,t)=>Object.assign({},t,e)})({},e)),$={concat:(e,t)=>v({},t,{types:v({},e.types,{[e.type]:e.message,[t.type]:t.message})})},E=e=>o(f.fromFoldableMap($,m.Foldable)(e,e=>[e.path,e]),f.map(e=>function(e,t){if(null==e)return{};var r={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(-1!==t.indexOf(o))continue;r[o]=e[o]}return r}(e,O)));function j(a){return(n,s,p)=>{return o(n,a.decode,r.mapLeft((i=!p.shouldUseNativeValidation&&"all"===p.criteriaMode,e=>{const t=i?E:N;return o(e,m.map(x),t)})),r.mapLeft(t=>e(t,p)),r.fold(e=>({values:{},errors:e}),e=>(p.shouldUseNativeValidation&&t({},p),{values:e,errors:{}})));var i}}export{j as ioTsResolver};
//# sourceMappingURL=io-ts.modern.mjs.map
