import{toNestErrors as e,validateFieldsNatively as s}from"@hookform/resolvers";import{getDotPath as t}from"@standard-schema/utils";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},r.apply(null,arguments)}function a(a,n,o={}){return async(n,i,l)=>{let u=a["~standard"].validate(n);if(u instanceof Promise&&(u=await u),u.issues){const s=function(e,s){const a={};for(let n=0;n<e.length;n++){const o=e[n],i=t(o);if(i&&(a[i]||(a[i]={message:o.message,type:""}),s)){const e=a[i].types||{};a[i].types=r({},e,{[Object.keys(e).length]:o.message})}}return a}(u.issues,!l.shouldUseNativeValidation&&"all"===l.criteriaMode);return{values:{},errors:e(s,l)}}return l.shouldUseNativeValidation&&s({},l),{values:o.raw?Object.assign({},n):u.value,errors:{}}}}export{a as standardSchemaResolver};
//# sourceMappingURL=standard-schema.modern.mjs.map
