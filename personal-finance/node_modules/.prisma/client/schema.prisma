// Personal Finance Tracking System Database Schema
// 个人+家庭财务追踪系统数据库模型

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户模型 - 支持家庭成员管理
model User {
  id        String   @id @default(cuid())
  name      String
  email     String?  @unique
  role      UserRole @default(MEMBER)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  cards        Card[]
  transactions Transaction[]
  accountBooks AccountBook[]

  @@map("users")
}

// 用户角色枚举
enum UserRole {
  ADMIN // 管理员
  MEMBER // 家庭成员
}

// 卡片类型枚举
enum CardType {
  BANK_CARD // 银行储蓄卡
  CREDIT_CARD // 信用卡
  WECHAT // 微信
  ALIPAY // 支付宝
  CASH // 现金
  OTHER // 其他
}

// 卡片模型 - 支持多种支付方式
model Card {
  id          String   @id @default(cuid())
  name        String // 卡片名称，如"招商银行储蓄卡"
  type        CardType
  accountNo   String? // 账号/卡号（部分显示）
  bankName    String? // 银行名称
  isActive    Boolean  @default(true)
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  userId       String
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions Transaction[]
  billImports  BillImport[]

  @@map("cards")
}

// 交易类型枚举
enum TransactionType {
  INCOME // 收入
  EXPENSE // 支出
  TRANSFER // 转账
}

// 交易状态枚举
enum TransactionStatus {
  PENDING // 待确认
  CONFIRMED // 已确认
  CANCELLED // 已取消
  LINKED // 已关联（避免重复记账）
}

// 交易记录模型
model Transaction {
  id              String            @id @default(cuid())
  externalId      String? // 外部交易ID（用于去重）
  type            TransactionType
  status          TransactionStatus @default(CONFIRMED)
  amount          Float // 金额
  description     String // 交易描述
  category        String? // 分类
  subcategory     String? // 子分类
  transactionDate DateTime // 交易时间
  location        String? // 交易地点
  notes           String? // 备注
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // 关联关系
  userId String
  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  cardId String?
  card   Card?   @relation(fields: [cardId], references: [id], onDelete: SetNull)

  // 账单导入关联
  billImportId String?
  billImport   BillImport? @relation(fields: [billImportId], references: [id], onDelete: SetNull)

  // 交易关联（用于处理微信关联银行卡支付等）
  linkedTransactionId String?
  linkedTransaction   Transaction?  @relation("TransactionLink", fields: [linkedTransactionId], references: [id])
  linkingTransactions Transaction[] @relation("TransactionLink")

  // 账本关联
  accountBookEntries AccountBookEntry[]

  // 对账关联
  sourceReconciliations ReconciliationRecord[] @relation("SourceReconciliation")
  targetReconciliations ReconciliationRecord[] @relation("TargetReconciliation")

  @@unique([externalId, cardId]) // 同一卡片的外部ID唯一
  @@map("transactions")
}

// 账单导入状态枚举
enum BillImportStatus {
  PENDING // 待处理
  PROCESSING // 处理中
  COMPLETED // 已完成
  FAILED // 失败
}

// 账单导入模型
model BillImport {
  id           String           @id @default(cuid())
  fileName     String // 原始文件名
  fileType     String // 文件类型 (csv, xlsx, pdf等)
  fileSize     Int // 文件大小
  status       BillImportStatus @default(PENDING)
  totalCount   Int              @default(0) // 总记录数
  successCount Int              @default(0) // 成功导入数
  errorCount   Int              @default(0) // 错误数
  errorLog     String? // 错误日志
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  // 关联关系
  cardId       String
  card         Card          @relation(fields: [cardId], references: [id], onDelete: Cascade)
  transactions Transaction[]

  @@map("bill_imports")
}

// 账本类型枚举
enum AccountBookType {
  DEFAULT // 默认账本（手工记账）
  CUSTOM // 自定义账本
  AGGREGATE // 聚合账本
}

// 账本模型
model AccountBook {
  id          String          @id @default(cuid())
  name        String // 账本名称
  type        AccountBookType
  description String? // 描述
  isActive    Boolean         @default(true)
  filterRules Json? // 筛选规则（JSON格式）
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  // 关联关系
  userId  String
  user    User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  entries AccountBookEntry[]

  @@map("account_books")
}

// 账本条目模型（多对多关系表）
model AccountBookEntry {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())

  // 关联关系
  accountBookId String
  accountBook   AccountBook @relation(fields: [accountBookId], references: [id], onDelete: Cascade)
  transactionId String
  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  @@unique([accountBookId, transactionId])
  @@map("account_book_entries")
}

// 分类模型
model Category {
  id        String   @id @default(cuid())
  name      String // 分类名称
  parentId  String? // 父分类ID（支持层级分类）
  icon      String? // 图标
  color     String? // 颜色
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 自关联
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")

  @@map("categories")
}

// 对账状态枚举
enum ReconciliationStatus {
  PENDING // 待对账
  MATCHED // 已匹配
  UNMATCHED // 未匹配
  MANUAL // 手工处理
}

// 对账记录模型
model ReconciliationRecord {
  id          String               @id @default(cuid())
  status      ReconciliationStatus @default(PENDING)
  matchScore  Float? // 匹配分数 (0-1)
  matchReason String? // 匹配原因
  notes       String? // 备注
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt

  // 关联的交易记录
  sourceTransactionId String?
  sourceTransaction   Transaction? @relation("SourceReconciliation", fields: [sourceTransactionId], references: [id])
  targetTransactionId String?
  targetTransaction   Transaction? @relation("TargetReconciliation", fields: [targetTransactionId], references: [id])

  @@map("reconciliation_records")
}

// 系统配置模型
model SystemConfig {
  id          String   @id @default(cuid())
  key         String   @unique // 配置键
  value       String // 配置值
  type        String // 值类型 (string, number, boolean, json)
  category    String // 配置分类
  description String? // 描述
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("system_configs")
}

// 审计日志模型
model AuditLog {
  id        String   @id @default(cuid())
  action    String // 操作类型
  entity    String // 实体类型
  entityId  String // 实体ID
  oldData   Json? // 旧数据
  newData   Json? // 新数据
  userId    String? // 操作用户
  ipAddress String? // IP地址
  userAgent String? // 用户代理
  createdAt DateTime @default(now())

  @@map("audit_logs")
}
