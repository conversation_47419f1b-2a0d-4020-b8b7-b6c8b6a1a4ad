{"mappings": ";;;;;AAAA;;;;;;;;;;CAUC;;;;AAgCM,SAAS,0CAAU,KAAqB;IAC7C,IAAI,YAAC,QAAQ,cAAE,UAAU,kBAAE,cAAc,EAAC,GAAG;IAC7C,IAAI,aAAC,SAAS,kBAAE,cAAc,cAAE,UAAU,EAAC,GAAG,CAAA,GAAA,yCAAW,EAAE;IAC3D,IAAI,QAAQ,CAAA,GAAA,YAAI,EAAE,QAAQ,CAAC,IAAI,CAAC;IAEhC,qBAAO,CAAA,GAAA,YAAI,EAAE,YAAY,CAAC,OAAO,CAAA,GAAA,iBAAS,EAAE,MAAM,KAAK,EAAS;QAC9D,GAAG,UAAU;QACb,WAAW,CAAA,GAAA,WAAG,EAAE;YACd,CAAC,cAAc,GAAG,EAAE;YACpB,CAAC,kBAAkB,GAAG,EAAE;QAC1B;IACF;AACF", "sources": ["packages/@react-aria/focus/src/FocusRing.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport clsx from 'clsx';\nimport {mergeProps} from '@react-aria/utils';\nimport React, {ReactElement} from 'react';\nimport {useFocusRing} from './useFocusRing';\n\nexport interface FocusRingProps {\n  /** Child element to apply CSS classes to. */\n  children: ReactElement,\n  /** CSS class to apply when the element is focused. */\n  focusClass?: string,\n  /** CSS class to apply when the element has keyboard focus. */\n  focusRingClass?: string,\n  /**\n   * Whether to show the focus ring when something\n   * inside the container element has focus (true), or\n   * only if the container itself has focus (false).\n   * @default false\n   */\n  within?: boolean,\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\n/**\n * A utility component that applies a CSS class when an element has keyboard focus.\n * Focus rings are visible only when the user is interacting with a keyboard,\n * not with a mouse, touch, or other input methods.\n */\nexport function FocusRing(props: FocusRingProps): React.ReactElement<unknown, string | React.JSXElementConstructor<any>> {\n  let {children, focusClass, focusRingClass} = props;\n  let {isFocused, isFocusVisible, focusProps} = useFocusRing(props);\n  let child = React.Children.only(children);\n\n  return React.cloneElement(child, mergeProps(child.props as any, {\n    ...focusProps,\n    className: clsx({\n      [focusClass || '']: isFocused,\n      [focusRingClass || '']: isFocusVisible\n    })\n  }));\n}\n"], "names": [], "version": 3, "file": "FocusRing.module.js.map"}