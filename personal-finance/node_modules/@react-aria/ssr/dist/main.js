var $97d95f6660b1bb14$exports = require("./SSRProvider.main.js");


function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "SSRProvider", () => $97d95f6660b1bb14$exports.SSRProvider);
$parcel$export(module.exports, "useSSRSafeId", () => $97d95f6660b1bb14$exports.useSSRSafeId);
$parcel$export(module.exports, "useIsSSR", () => $97d95f6660b1bb14$exports.useIsSSR);
/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 


//# sourceMappingURL=main.js.map
