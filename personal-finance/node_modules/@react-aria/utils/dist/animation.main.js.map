{"mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;AAMM,SAAS,0CAAkB,GAAkC,EAAE,UAAmB,IAAI;IAC3F,IAAI,CAAC,YAAY,YAAY,GAAG,CAAA,GAAA,qBAAO,EAAE;IACzC,IAAI,mBAAmB,cAAc;IAErC,4CAA4C;IAC5C,kIAAkI;IAClI,4IAA4I;IAC5I,EAAE;IACF,mJAAmJ;IACnJ,+FAA+F;IAC/F,CAAA,GAAA,yCAAc,EAAE;QACd,IAAI,oBAAoB,IAAI,OAAO,IAAI,mBAAmB,IAAI,OAAO,EAAE;YACrE,KAAK,IAAI,aAAa,IAAI,OAAO,CAAC,aAAa,GAC7C,IAAI,qBAAqB,eACvB,UAAU,MAAM;QAGtB;IACF,GAAG;QAAC;QAAK;KAAiB;IAE1B,mCAAa,KAAK,kBAAkB,CAAA,GAAA,wBAAU,EAAE,IAAM,YAAY,QAAQ,EAAE;IAC5E,OAAO;AACT;AAEO,SAAS,0CAAiB,GAAkC,EAAE,MAAe;IAClF,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qBAAO,EAAiC,SAAS,SAAS;IAE1F,OAAQ;QACN,KAAK;YACH,qDAAqD;YACrD,IAAI,CAAC,QACH,aAAa;YAEf;QACF,KAAK;QACL,KAAK;YACH,4EAA4E;YAC5E,2BAA2B;YAC3B,IAAI,QACF,aAAa;YAEf;IACJ;IAEA,IAAI,YAAY,cAAc;IAC9B,mCACE,KACA,WACA,CAAA,GAAA,wBAAU,EAAE;QACV,yEAAyE;QACzE,aAAa,CAAA,QAAS,UAAU,YAAY,WAAW;IACzD,GAAG,EAAE;IAGP,OAAO;AACT;AAEA,SAAS,mCAAa,GAAkC,EAAE,QAAiB,EAAE,KAAiB;IAC5F,CAAA,GAAA,yCAAc,EAAE;QACd,IAAI,YAAY,IAAI,OAAO,EAAE;YAC3B,IAAI,CAAE,CAAA,mBAAmB,IAAI,OAAO,AAAD,GAAI;gBACrC,QAAQ;gBACR;gBACA;YACF;YAEA,IAAI,aAAa,IAAI,OAAO,CAAC,aAAa;YAC1C,IAAI,WAAW,MAAM,KAAK,GAAG;gBAC3B;gBACA;YACF;YAEA,IAAI,WAAW;YACf,QAAQ,GAAG,CAAC,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,GAAG,IAAI,CAAC;gBAChD,IAAI,CAAC,UACH,CAAA,GAAA,yBAAQ,EAAE;oBACR;gBACF;YAEJ,GAAG,KAAK,CAAC,KAAO;YAEhB,OAAO;gBACL,WAAW;YACb;QACF;IACF,GAAG;QAAC;QAAK;QAAU;KAAM;AAC3B", "sources": ["packages/@react-aria/utils/src/animation.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {flushSync} from 'react-dom';\nimport {RefObject, useCallback, useState} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\n\nexport function useEnterAnimation(ref: RefObject<HTMLElement | null>, isReady: boolean = true): boolean {\n  let [isEntering, setEntering] = useState(true);\n  let isAnimationReady = isEntering && isReady;\n\n  // There are two cases for entry animations:\n  // 1. CSS @keyframes. The `animation` property is set during the isEntering state, and it is removed after the animation finishes.\n  // 2. CSS transitions. The initial styles are applied during the isEntering state, and removed immediately, causing the transition to occur.\n  //\n  // In the second case, cancel any transitions that were triggered prior to the isEntering = false state (when the transition is supposed to start).\n  // This can happen when isReady starts as false (e.g. popovers prior to placement calculation).\n  useLayoutEffect(() => {\n    if (isAnimationReady && ref.current && 'getAnimations' in ref.current) {\n      for (let animation of ref.current.getAnimations()) {\n        if (animation instanceof CSSTransition) {\n          animation.cancel();\n        }\n      }\n    }\n  }, [ref, isAnimationReady]);\n\n  useAnimation(ref, isAnimationReady, useCallback(() => setEntering(false), []));\n  return isAnimationReady;\n}\n\nexport function useExitAnimation(ref: RefObject<HTMLElement | null>, isOpen: boolean): boolean {\n  let [exitState, setExitState] = useState<'closed' | 'open' | 'exiting'>(isOpen ? 'open' : 'closed');\n\n  switch (exitState) {\n    case 'open':\n      // If isOpen becomes false, set the state to exiting.\n      if (!isOpen) {\n        setExitState('exiting');\n      }\n      break;\n    case 'closed':\n    case 'exiting':\n      // If we are exiting and isOpen becomes true, the animation was interrupted.\n      // Reset the state to open.\n      if (isOpen) {\n        setExitState('open');\n      }\n      break;\n  }\n\n  let isExiting = exitState === 'exiting';\n  useAnimation(\n    ref,\n    isExiting,\n    useCallback(() => {\n      // Set the state to closed, which will cause the element to be unmounted.\n      setExitState(state => state === 'exiting' ? 'closed' : state);\n    }, [])\n  );\n\n  return isExiting;\n}\n\nfunction useAnimation(ref: RefObject<HTMLElement | null>, isActive: boolean, onEnd: () => void): void {\n  useLayoutEffect(() => {\n    if (isActive && ref.current) {\n      if (!('getAnimations' in ref.current)) {\n        // JSDOM\n        onEnd();\n        return;\n      }\n\n      let animations = ref.current.getAnimations();\n      if (animations.length === 0) {\n        onEnd();\n        return;\n      }\n\n      let canceled = false;\n      Promise.all(animations.map(a => a.finished)).then(() => {\n        if (!canceled) {\n          flushSync(() => {\n            onEnd();\n          });\n        }\n      }).catch(() => {});\n\n      return () => {\n        canceled = true;\n      };\n    }\n  }, [ref, isActive, onEnd]);\n}\n"], "names": [], "version": 3, "file": "animation.main.js.map"}