{"mappings": "AAAA;;;;;;;;;;CAUC,GAOM,SAAS,0CAAa,GAAG,IAA4D;IAC1F,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC,EAAE,EAC9B,OAAO,IAAI,CAAC,EAAE;IAGhB,OAAO,CAAC;QACN,IAAI,aAAa;QAEjB,MAAM,WAAW,KAAK,GAAG,CAAC,CAAA;YACxB,MAAM,UAAU,6BAAO,KAAK;YAC5B,eAAA,aAAe,OAAO,WAAW;YACjC,OAAO;QACT;QAEA,IAAI,YACF,OAAO;YACL,SAAS,OAAO,CAAC,CAAC,SAAS;gBACzB,IAAI,OAAO,YAAY,YACrB;qBAEA,6BAAO,IAAI,CAAC,EAAE,EAAE;YAEpB;QACF;IAEJ;AACF;AAEA,SAAS,6BAAU,GAAoD,EAAE,KAAQ;IAC/E,IAAI,OAAO,QAAQ,YACjB,OAAO,IAAI;SACN,IAAI,OAAO,MAChB,IAAI,OAAO,GAAG;AAElB", "sources": ["packages/@react-aria/utils/src/mergeRefs.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject, Ref} from 'react';\n\n/**\n * Merges multiple refs into one. Works with either callback or object refs.\n */\nexport function mergeRefs<T>(...refs: Array<Ref<T> | MutableRefObject<T> | null | undefined>): Ref<T> {\n  if (refs.length === 1 && refs[0]) {\n    return refs[0];\n  }\n\n  return (value: T | null) => {\n    let hasCleanup = false;\n\n    const cleanups = refs.map(ref => {\n      const cleanup = setRef(ref, value);\n      hasCleanup ||= typeof cleanup == 'function';\n      return cleanup;\n    });\n\n    if (hasCleanup) {\n      return () => {\n        cleanups.forEach((cleanup, i) => {\n          if (typeof cleanup === 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        });\n      };\n    }\n  };\n}\n\nfunction setRef<T>(ref: Ref<T> | MutableRefObject<T> | null | undefined, value: T) {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref != null) {\n    ref.current = value;\n  }\n}\n"], "names": [], "version": 3, "file": "mergeRefs.module.js.map"}