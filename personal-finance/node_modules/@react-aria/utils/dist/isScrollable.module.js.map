{"mappings": "AAAA;;;;;;;;;;CAUC,GAEM,SAAS,0CAAa,IAAoB,EAAE,gBAA0B;IAC3E,IAAI,CAAC,MACH,OAAO;IAET,IAAI,QAAQ,OAAO,gBAAgB,CAAC;IACpC,IAAI,eAAe,gBAAgB,IAAI,CAAC,MAAM,QAAQ,GAAG,MAAM,SAAS,GAAG,MAAM,SAAS;IAE1F,IAAI,gBAAgB,kBAClB,eAAe,KAAK,YAAY,KAAK,KAAK,YAAY,IAAI,KAAK,WAAW,KAAK,KAAK,WAAW;IAGjG,OAAO;AACT", "sources": ["packages/@react-aria/utils/src/isScrollable.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport function isScrollable(node: Element | null, checkForOverflow?: boolean): boolean {\n  if (!node) {\n    return false;\n  }\n  let style = window.getComputedStyle(node);\n  let isScrollable = /(auto|scroll)/.test(style.overflow + style.overflowX + style.overflowY);\n\n  if (isScrollable && checkForOverflow) {\n    isScrollable = node.scrollHeight !== node.clientHeight || node.scrollWidth !== node.clientWidth;\n  }\n\n  return isScrollable;\n}\n"], "names": [], "version": 3, "file": "isScrollable.module.js.map"}