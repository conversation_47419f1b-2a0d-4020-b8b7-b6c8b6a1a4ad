{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAcM,SAAS,0CAAe,UAAuB,EAAE,OAAoB;IAC1E,IAAI,UAAU,qCAAe,YAAY,SAAS;IAClD,IAAI,UAAU,qCAAe,YAAY,SAAS;IAClD,IAAI,QAAQ,QAAQ,WAAW;IAC/B,IAAI,SAAS,QAAQ,YAAY;IACjC,IAAI,IAAI,WAAW,UAAU;IAC7B,IAAI,IAAI,WAAW,SAAS;IAE5B,8EAA8E;IAC9E,IAAI,kBACF,cAAc,mBACd,eAAe,oBACf,gBAAgB,sBAChB,kBAAkB,uBAClB,mBAAmB,qBACnB,iBAAiB,EAClB,GAAG,iBAAiB;IAErB,IAAI,kBAAkB,IAAI,SAAS,iBAAiB;IACpD,IAAI,kBAAkB,IAAI,SAAS,gBAAgB;IACnD,gFAAgF;IAChF,IAAI,OAAO,kBAAkB,WAAW,WAAW;IACnD,IAAI,OAAO,kBAAkB,WAAW,YAAY;IAEpD,2EAA2E;IAC3E,WAAW;IACX,IAAI,yBAAyB,SAAS,kBAAkB,OAAO;IAC/D,IAAI,4BAA4B,SAAS,qBAAqB,OAAO;IACrE,IAAI,2BAA2B,SAAS,oBAAoB,OAAO;IACnE,IAAI,0BAA0B,SAAS,mBAAmB,OAAO;IAEjE,IAAI,WAAW,IAAI,yBACjB,IAAI,UAAU,SAAS,iBAAiB,MAAM;SACzC,IAAI,UAAU,QAAQ,OAAO,0BAClC,KAAK,UAAU,QAAQ,OAAO;IAEhC,IAAI,WAAW,kBAAkB,wBAC/B,IAAI,UAAU,SAAS,gBAAgB,MAAM;SACxC,IAAI,UAAU,SAAS,OAAO,2BACnC,KAAK,UAAU,SAAS,OAAO;IAGjC,WAAW,UAAU,GAAG;IACxB,WAAW,SAAS,GAAG;AACzB;AAEA;;;CAGC,GACD,SAAS,qCAAe,QAAqB,EAAE,KAAkB,EAAE,IAAkB;IACnF,MAAM,OAAO,SAAS,SAAS,eAAe;IAC9C,IAAI,MAAM;IACV,MAAO,MAAM,YAAY,CAAE;QACzB,OAAO,KAAK,CAAC,KAAK;QAClB,IAAI,MAAM,YAAY,KAAK,UAEzB;aACK,IAAI,MAAM,YAAY,CAAC,QAAQ,CAAC,WAAW;YAChD,8DAA8D;YAC9D,iEAAiE;YACjE,2DAA2D;YAC3D,OAAO,QAAQ,CAAC,KAAK;YACrB;QACF;QACA,QAAQ,MAAM,YAAY;IAC5B;IACA,OAAO;AACT;AAOO,SAAS,0CAAmB,aAA6B,EAAE,IAA6B;IAC7F,IAAI,iBAAiB,SAAS,QAAQ,CAAC,gBAAgB;QACrD,IAAI,OAAO,SAAS,gBAAgB,IAAI,SAAS,eAAe;QAChE,IAAI,oBAAoB,OAAO,gBAAgB,CAAC,MAAM,QAAQ,KAAK;QACnE,4JAA4J;QAC5J,IAAI,CAAC,mBAAmB;gBAGtB,0HAA0H;YAC1H,6JAA6J;YAC7J;YAJA,IAAI,EAAC,MAAM,YAAY,EAAE,KAAK,WAAW,EAAC,GAAG,cAAc,qBAAqB;YAIhF,0BAAA,qCAAA,gCAAA,cAAe,cAAc,cAA7B,oDAAA,mCAAA,eAAgC;gBAAC,OAAO;YAAS;YACjD,IAAI,EAAC,MAAM,OAAO,EAAE,KAAK,MAAM,EAAC,GAAG,cAAc,qBAAqB;YACtE,kDAAkD;YAClD,IAAI,AAAC,KAAK,GAAG,CAAC,eAAe,WAAW,KAAO,KAAK,GAAG,CAAC,cAAc,UAAU,GAAI;oBAClF,wCAAA,yBACA;gBADA,iBAAA,4BAAA,0BAAA,KAAM,iBAAiB,cAAvB,+CAAA,yCAAA,wBAAyB,cAAc,cAAvC,6DAAA,4CAAA,yBAA0C;oBAAC,OAAO;oBAAU,QAAQ;gBAAQ;iBAC5E,iCAAA,cAAc,cAAc,cAA5B,qDAAA,oCAAA,eAA+B;oBAAC,OAAO;gBAAS;YAClD;QACF,OAAO;YACL,IAAI,gBAAgB,CAAA,GAAA,yCAAe,EAAE;YACrC,+JAA+J;YAC/J,KAAK,IAAI,gBAAgB,cACvB,0CAAe,cAA6B;QAEhD;IACF;AACF", "sources": ["packages/@react-aria/utils/src/scrollIntoView.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getScrollParents} from './getScrollParents';\n\ninterface ScrollIntoViewportOpts {\n  /** The optional containing element of the target to be centered in the viewport. */\n  containingElement?: Element | null\n}\n\n/**\n * Scrolls `scrollView` so that `element` is visible.\n * Similar to `element.scrollIntoView({block: 'nearest'})` (not supported in Edge),\n * but doesn't affect parents above `scrollView`.\n */\nexport function scrollIntoView(scrollView: HTMLElement, element: HTMLElement): void {\n  let offsetX = relativeOffset(scrollView, element, 'left');\n  let offsetY = relativeOffset(scrollView, element, 'top');\n  let width = element.offsetWidth;\n  let height = element.offsetHeight;\n  let x = scrollView.scrollLeft;\n  let y = scrollView.scrollTop;\n\n  // Account for top/left border offsetting the scroll top/Left + scroll padding\n  let {\n    borderTopWidth,\n    borderLeftWidth,\n    scrollPaddingTop,\n    scrollPaddingRight,\n    scrollPaddingBottom,\n    scrollPaddingLeft\n  } = getComputedStyle(scrollView);\n\n  let borderAdjustedX = x + parseInt(borderLeftWidth, 10);\n  let borderAdjustedY = y + parseInt(borderTopWidth, 10);\n  // Ignore end/bottom border via clientHeight/Width instead of offsetHeight/Width\n  let maxX = borderAdjustedX + scrollView.clientWidth;\n  let maxY = borderAdjustedY + scrollView.clientHeight;\n\n  // Get scroll padding values as pixels - defaults to 0 if no scroll padding\n  // is used.\n  let scrollPaddingTopNumber = parseInt(scrollPaddingTop, 10) || 0;\n  let scrollPaddingBottomNumber = parseInt(scrollPaddingBottom, 10) || 0;\n  let scrollPaddingRightNumber = parseInt(scrollPaddingRight, 10) || 0;\n  let scrollPaddingLeftNumber = parseInt(scrollPaddingLeft, 10) || 0;\n\n  if (offsetX <= x + scrollPaddingLeftNumber) {\n    x = offsetX - parseInt(borderLeftWidth, 10) - scrollPaddingLeftNumber;\n  } else if (offsetX + width > maxX - scrollPaddingRightNumber) {\n    x += offsetX + width - maxX + scrollPaddingRightNumber;\n  }\n  if (offsetY <= borderAdjustedY + scrollPaddingTopNumber) {\n    y = offsetY - parseInt(borderTopWidth, 10) - scrollPaddingTopNumber;\n  } else if (offsetY + height > maxY - scrollPaddingBottomNumber) {\n    y += offsetY + height - maxY + scrollPaddingBottomNumber;\n  }\n\n  scrollView.scrollLeft = x;\n  scrollView.scrollTop = y;\n}\n\n/**\n * Computes the offset left or top from child to ancestor by accumulating\n * offsetLeft or offsetTop through intervening offsetParents.\n */\nfunction relativeOffset(ancestor: HTMLElement, child: HTMLElement, axis: 'left'|'top') {\n  const prop = axis === 'left' ? 'offsetLeft' : 'offsetTop';\n  let sum = 0;\n  while (child.offsetParent) {\n    sum += child[prop];\n    if (child.offsetParent === ancestor) {\n      // Stop once we have found the ancestor we are interested in.\n      break;\n    } else if (child.offsetParent.contains(ancestor)) {\n      // If the ancestor is not `position:relative`, then we stop at\n      // _its_ offset parent, and we subtract off _its_ offset, so that\n      // we end up with the proper offset from child to ancestor.\n      sum -= ancestor[prop];\n      break;\n    }\n    child = child.offsetParent as HTMLElement;\n  }\n  return sum;\n}\n\n/**\n * Scrolls the `targetElement` so it is visible in the viewport. Accepts an optional `opts.containingElement`\n * that will be centered in the viewport prior to scrolling the targetElement into view. If scrolling is prevented on\n * the body (e.g. targetElement is in a popover), this will only scroll the scroll parents of the targetElement up to but not including the body itself.\n */\nexport function scrollIntoViewport(targetElement: Element | null, opts?: ScrollIntoViewportOpts): void {\n  if (targetElement && document.contains(targetElement)) {\n    let root = document.scrollingElement || document.documentElement;\n    let isScrollPrevented = window.getComputedStyle(root).overflow === 'hidden';\n    // If scrolling is not currently prevented then we aren’t in a overlay nor is a overlay open, just use element.scrollIntoView to bring the element into view\n    if (!isScrollPrevented) {\n      let {left: originalLeft, top: originalTop} = targetElement.getBoundingClientRect();\n\n      // use scrollIntoView({block: 'nearest'}) instead of .focus to check if the element is fully in view or not since .focus()\n      // won't cause a scroll if the element is already focused and doesn't behave consistently when an element is partially out of view horizontally vs vertically\n      targetElement?.scrollIntoView?.({block: 'nearest'});\n      let {left: newLeft, top: newTop} = targetElement.getBoundingClientRect();\n      // Account for sub pixel differences from rounding\n      if ((Math.abs(originalLeft - newLeft) > 1) || (Math.abs(originalTop - newTop) > 1)) {\n        opts?.containingElement?.scrollIntoView?.({block: 'center', inline: 'center'});\n        targetElement.scrollIntoView?.({block: 'nearest'});\n      }\n    } else {\n      let scrollParents = getScrollParents(targetElement);\n      // If scrolling is prevented, we don't want to scroll the body since it might move the overlay partially offscreen and the user can't scroll it back into view.\n      for (let scrollParent of scrollParents) {\n        scrollIntoView(scrollParent as HTMLElement, targetElement as HTMLElement);\n      }\n    }\n  }\n}\n"], "names": [], "version": 3, "file": "scrollIntoView.module.js.map"}