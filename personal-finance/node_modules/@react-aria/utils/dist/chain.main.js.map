{"mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAED;;CAEC,GACM,SAAS,0CAAM,GAAG,SAAgB;IACvC,OAAO,CAAC,GAAG;QACT,KAAK,IAAI,YAAY,UACnB,IAAI,OAAO,aAAa,YACtB,YAAY;IAGlB;AACF", "sources": ["packages/@react-aria/utils/src/chain.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n/**\n * Calls all functions in the order they were chained with the same arguments.\n */\nexport function chain(...callbacks: any[]): (...args: any[]) => void {\n  return (...args: any[]) => {\n    for (let callback of callbacks) {\n      if (typeof callback === 'function') {\n        callback(...args);\n      }\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "chain.main.js.map"}