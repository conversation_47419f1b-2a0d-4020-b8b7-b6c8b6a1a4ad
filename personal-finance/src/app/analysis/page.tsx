'use client'

import { useState, useEffect } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { ClientOnly } from '@/components/ClientOnly'
import { useAuth } from '@/hooks/useAuth'
import { useToast } from '@/hooks/useToast'
import {
  ChartBarIcon,
  CalendarDaysIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  UserGroupIcon,
  TagIcon
} from '@heroicons/react/24/outline'

interface Transaction {
  id: string
  type: string
  amount: number
  description: string
  transactionType?: string
  counterParty?: string
  category?: string
  transactionDate: string
  createdAt: string
}

interface Card {
  id: string
  name: string
  type: string
  balance: number
}

interface AnalysisData {
  totalTransactions: number
  totalIncome: number
  totalExpense: number
  netAmount: number
  categoryStats: Array<{
    category: string
    count: number
    amount: number
    percentage: number
  }>
  transactionTypeStats: Array<{
    type: string
    count: number
    amount: number
    percentage: number
  }>
  counterPartyStats: Array<{
    counterParty: string
    count: number
    amount: number
    percentage: number
  }>
  monthlyTrend: Array<{
    month: string
    income: number
    expense: number
    net: number
  }>
  recentTransactions: Transaction[]
}

export default function AnalysisPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { apiCall, isAuthenticated, loading: authLoading } = useAuth()
  const { showError } = useToast()
  const cardId = searchParams.get('cardId')

  const [loading, setLoading] = useState(true)
  const [card, setCard] = useState<Card | null>(null)
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null)
  const [dateRange, setDateRange] = useState('3months') // 3months, 6months, 1year, all

  useEffect(() => {
    if (!cardId) {
      router.push('/cards')
      return
    }

    if (!authLoading && isAuthenticated) {
      fetchCardAndAnalysis()
    } else if (!authLoading && !isAuthenticated) {
      setLoading(false)
    }
  }, [cardId, dateRange, authLoading, isAuthenticated])

  const fetchCardAndAnalysis = async () => {
    try {
      setLoading(true)
      
      // 获取卡片信息
      const cardResponse = await apiCall(`/api/cards/${cardId}`)
      if (!cardResponse.ok) {
        showError('获取卡片信息失败', '请稍后重试')
        router.push('/cards')
        return
      }
      
      const cardData = await cardResponse.json()
      setCard(cardData.data)

      // 获取分析数据
      const analysisResponse = await apiCall(`/api/analysis/${cardId}?range=${dateRange}`)
      if (!analysisResponse.ok) {
        showError('获取分析数据失败', '请稍后重试')
        return
      }
      
      const analysis = await analysisResponse.json()
      setAnalysisData(analysis.data)
      
    } catch (error) {
      console.error('Fetch failed:', error)
      showError('加载失败', '网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(amount)
  }

  const getDateRangeLabel = (range: string) => {
    const labels: Record<string, string> = {
      '3months': '最近3个月',
      '6months': '最近6个月',
      '1year': '最近1年',
      'all': '全部时间'
    }
    return labels[range] || range
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">分析数据加载中...</p>
        </div>
      </div>
    )
  }

  if (!card || !analysisData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">数据加载失败</p>
          <button 
            onClick={() => router.push('/cards')}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            返回卡片列表
          </button>
        </div>
      </div>
    )
  }

  return (
    <ProtectedRoute>
      <ClientOnly>
        <div className="min-h-screen bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* 页面头部 */}
            <div className="mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">账单分析</h1>
                  <p className="mt-2 text-gray-600">{card.name} - 多维度数据分析</p>
                </div>
                <div className="flex items-center space-x-4">
                  <select
                    value={dateRange}
                    onChange={(e) => setDateRange(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="3months">最近3个月</option>
                    <option value="6months">最近6个月</option>
                    <option value="1year">最近1年</option>
                    <option value="all">全部时间</option>
                  </select>
                  <button
                    onClick={() => router.push('/cards')}
                    className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    返回卡片
                  </button>
                </div>
              </div>
            </div>

            {/* 概览统计 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <ChartBarIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总交易数</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {analysisData.totalTransactions}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <ArrowTrendingUpIcon className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总收入</p>
                    <p className="text-2xl font-semibold text-green-600">
                      {formatCurrency(analysisData.totalIncome)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-red-100 rounded-lg">
                    <ArrowTrendingDownIcon className="h-6 w-6 text-red-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">总支出</p>
                    <p className="text-2xl font-semibold text-red-600">
                      {formatCurrency(analysisData.totalExpense)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <CurrencyDollarIcon className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">净收支</p>
                    <p className={`text-2xl font-semibold ${
                      analysisData.netAmount >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {formatCurrency(analysisData.netAmount)}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* 分析图表区域 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              {/* 分类统计 */}
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center mb-4">
                  <TagIcon className="h-5 w-5 text-gray-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">分类统计</h3>
                </div>
                <div className="space-y-4">
                  {analysisData.categoryStats.slice(0, 5).map((stat, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm font-medium text-gray-700">
                            {stat.category || '未分类'}
                          </span>
                          <span className="text-sm text-gray-500">
                            {stat.count}笔 ({stat.percentage.toFixed(1)}%)
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${stat.percentage}%` }}
                          ></div>
                        </div>
                        <div className="text-right mt-1">
                          <span className="text-sm font-medium text-gray-900">
                            {formatCurrency(Math.abs(stat.amount))}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 交易类型统计 */}
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center mb-4">
                  <ChartBarIcon className="h-5 w-5 text-gray-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">交易类型统计</h3>
                </div>
                <div className="space-y-4">
                  {analysisData.transactionTypeStats.slice(0, 5).map((stat, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm font-medium text-gray-700">
                            {stat.type || '未知类型'}
                          </span>
                          <span className="text-sm text-gray-500">
                            {stat.count}笔 ({stat.percentage.toFixed(1)}%)
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-600 h-2 rounded-full" 
                            style={{ width: `${stat.percentage}%` }}
                          ></div>
                        </div>
                        <div className="text-right mt-1">
                          <span className="text-sm font-medium text-gray-900">
                            {formatCurrency(Math.abs(stat.amount))}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 交易对方统计 */}
            <div className="bg-white rounded-lg shadow p-6 mb-8">
              <div className="flex items-center mb-4">
                <UserGroupIcon className="h-5 w-5 text-gray-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">交易对方统计</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {analysisData.counterPartyStats.slice(0, 6).map((stat, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700 truncate">
                        {stat.counterParty || '未知对方'}
                      </span>
                      <span className="text-sm text-gray-500">
                        {stat.count}笔
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                      <div 
                        className="bg-purple-600 h-2 rounded-full" 
                        style={{ width: `${stat.percentage}%` }}
                      ></div>
                    </div>
                    <div className="text-right">
                      <span className="text-sm font-medium text-gray-900">
                        {formatCurrency(Math.abs(stat.amount))}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </ClientOnly>
    </ProtectedRoute>
  )
}
