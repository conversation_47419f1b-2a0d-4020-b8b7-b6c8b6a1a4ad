'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { But<PERSON> } from '@/components/ui/Button'
import { ArrowLeftIcon } from '@heroicons/react/24/outline'

interface Transaction {
  id: string
  externalId?: string
  type: string
  status: string
  amount: number
  description: string
  category?: string
  subcategory?: string
  transactionDate: string
  location?: string
  notes?: string
  createdAt: string
  user: {
    id: string
    name: string
  }
  card: {
    id: string
    name: string
    type: string
  }
}

interface TransactionListResponse {
  success: boolean
  data: Transaction[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

function TransactionsPageContent() {
  const { apiCall, isAuthenticated, loading: authLoading } = useAuth()
  const searchParams = useSearchParams()
  const cardId = searchParams.get('cardId')

  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [cardName, setCardName] = useState<string>('')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })

  const fetchTransactions = useCallback(async (page: number = 1) => {
    try {
      setLoading(true)
      const response = await apiCall(`/api/transactions?cardId=${cardId}&page=${page}&limit=${pagination.limit}`)
      const data: TransactionListResponse = await response.json()

      if (data.success) {
        setTransactions(data.data)
        setPagination(data.pagination)
      } else {
        setError('获取交易记录失败')
      }
    } catch (error) {
      console.error('获取交易记录失败:', error)
      setError('获取交易记录失败')
    } finally {
      setLoading(false)
    }
  }, [apiCall, cardId, pagination.limit])

  const fetchCardInfo = useCallback(async () => {
    try {
      const response = await apiCall(`/api/cards/${cardId}`)
      const data = await response.json()
      
      if (data.success) {
        setCardName(data.data.name)
      }
    } catch (error) {
      console.error('获取卡片信息失败:', error)
    }
  }, [apiCall, cardId])

  useEffect(() => {
    // 只有在认证完成且用户已登录时才获取数据
    if (!authLoading && isAuthenticated && cardId) {
      fetchTransactions(1) // 重置到第一页
      fetchCardInfo()
    } else if (!authLoading && !isAuthenticated) {
      setError('未授权访问')
      setLoading(false)
    } else if (!cardId) {
      setError('未指定卡片ID')
      setLoading(false)
    }
  }, [cardId, fetchCardInfo, authLoading, isAuthenticated]) // 移除fetchTransactions依赖避免循环

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      fetchTransactions(newPage)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  const getTransactionTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      INCOME: '收入',
      EXPENSE: '支出',
      TRANSFER: '转账',
    }
    return labels[type] || type
  }

  const getAmountColor = (type: string, amount: number) => {
    if (type === 'INCOME' || amount > 0) {
      return 'text-green-600'
    } else {
      return 'text-red-600'
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 text-lg mb-4">{error}</div>
        <Link href="/cards">
          <Button>返回卡片列表</Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面头部 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Link href="/cards">
            <Button variant="outline" size="sm">
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              返回卡片
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">交易记录</h1>
            {cardName && (
              <p className="text-gray-600 mt-1">{cardName}</p>
            )}
          </div>
        </div>
        
        <div className="text-sm text-gray-500">
          共 {pagination.total} 笔交易，第 {pagination.page} / {pagination.totalPages} 页
        </div>
      </div>

      {/* 交易列表 */}
      {transactions.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg mb-4">暂无交易记录</div>
          <p className="text-gray-400">导入账单后，交易记录将显示在这里</p>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    交易时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    类型
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    描述
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    分类
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    金额
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    地点
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {transactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(transaction.transactionDate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        transaction.type === 'INCOME' 
                          ? 'bg-green-100 text-green-800'
                          : transaction.type === 'EXPENSE'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {getTransactionTypeLabel(transaction.type)}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      <div className="max-w-xs truncate" title={transaction.description}>
                        {transaction.description}
                      </div>
                      {transaction.notes && (
                        <div className="text-xs text-gray-500 mt-1 max-w-xs truncate" title={transaction.notes}>
                          {transaction.notes}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {transaction.category || '-'}
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium text-right ${getAmountColor(transaction.type, transaction.amount)}`}>
                      {formatCurrency(transaction.amount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {transaction.location || '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* 分页组件 */}
      {pagination.totalPages > 1 && (
        <div className="mt-8 flex justify-center">
          <div className="flex items-center space-x-2">
            {/* 上一页按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
            >
              上一页
            </Button>

            {/* 页码按钮 */}
            {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
              let pageNum: number
              if (pagination.totalPages <= 5) {
                pageNum = i + 1
              } else if (pagination.page <= 3) {
                pageNum = i + 1
              } else if (pagination.page >= pagination.totalPages - 2) {
                pageNum = pagination.totalPages - 4 + i
              } else {
                pageNum = pagination.page - 2 + i
              }

              return (
                <Button
                  key={pageNum}
                  variant={pagination.page === pageNum ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePageChange(pageNum)}
                  className="min-w-[40px]"
                >
                  {pageNum}
                </Button>
              )
            })}

            {/* 下一页按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.totalPages}
            >
              下一页
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

export default function TransactionsPage() {
  return (
    <ProtectedRoute>
      <TransactionsPageContent />
    </ProtectedRoute>
  )
}
