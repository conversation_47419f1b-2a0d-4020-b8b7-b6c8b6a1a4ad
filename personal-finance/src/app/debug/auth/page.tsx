'use client'

import { useAuth } from '@/hooks/useAuth'
import { useEffect, useState } from 'react'

export default function AuthDebugPage() {
  const { user, token, isAuthenticated, loading } = useAuth()
  const [localStorageData, setLocalStorageData] = useState<any>({})

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setLocalStorageData({
        token: localStorage.getItem('token'),
        user: localStorage.getItem('user'),
      })
    }
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-8">认证状态调试</h1>
        
        <div className="space-y-6">
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">useAuth状态</h2>
            <div className="space-y-2">
              <p><strong>Loading:</strong> {loading ? 'true' : 'false'}</p>
              <p><strong>Is Authenticated:</strong> {isAuthenticated ? 'true' : 'false'}</p>
              <p><strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'null'}</p>
              <p><strong>Token:</strong> {token ? `${token.substring(0, 20)}...` : 'null'}</p>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">localStorage数据</h2>
            <div className="space-y-2">
              <p><strong>Token:</strong> {localStorageData.token ? `${localStorageData.token.substring(0, 20)}...` : 'null'}</p>
              <p><strong>User:</strong> {localStorageData.user || 'null'}</p>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">测试API调用</h2>
            <TestApiCall />
          </div>
        </div>
      </div>
    </div>
  )
}

function TestApiCall() {
  const { apiCall } = useAuth()
  const [result, setResult] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const testCall = async () => {
    setLoading(true)
    try {
      const response = await apiCall('/api/cards')
      const data = await response.json()
      setResult(JSON.stringify(data, null, 2))
    } catch (error) {
      setResult(`Error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <button
        onClick={testCall}
        disabled={loading}
        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400"
      >
        {loading ? '测试中...' : '测试API调用'}
      </button>
      
      {result && (
        <div className="mt-4">
          <h3 className="font-medium text-gray-900 mb-2">API响应:</h3>
          <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-auto">
            {result}
          </pre>
        </div>
      )}
    </div>
  )
}
