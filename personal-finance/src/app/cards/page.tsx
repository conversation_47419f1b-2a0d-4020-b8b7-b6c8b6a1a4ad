'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { useToast } from '@/hooks/useToast'
import { useConfirm } from '@/hooks/useConfirm'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { ClientOnly } from '@/components/ClientOnly'
import { Button } from '@/components/ui/Button'
import { CardGrid } from '@/components/cards/CardGrid'
import { CardForm } from '@/components/cards/CardForm'

interface Card {
  id: string
  name: string
  type: string
  accountNo?: string
  bankName?: string
  balance: number
  isActive: boolean
  createdAt: string
  user: {
    id: string
    name: string
  }
  _count: {
    transactions: number
    billImports: number
  }
}

function CardsPageContent() {
  const { apiCall, isAuthenticated, loading: authLoading } = useAuth()
  const { showSuccess, showError } = useToast()
  const { confirm } = useConfirm()
  const router = useRouter()
  const [cards, setCards] = useState<Card[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingCard, setEditingCard] = useState<Card | null>(null)

  useEffect(() => {
    // 只有在认证完成且用户已登录时才获取卡片
    if (!authLoading && isAuthenticated) {
      fetchCards()
    } else if (!authLoading && !isAuthenticated) {
      setLoading(false)
    }
  }, [authLoading, isAuthenticated])

  const fetchCards = async () => {
    try {
      setLoading(true)
      const response = await apiCall('/api/cards')
      const data = await response.json()

      if (data.success) {
        setCards(data.data)
      } else {
        console.error('Failed to fetch cards:', data.error)
      }
    } catch (error) {
      console.error('Failed to fetch cards:', error)
      // 如果是认证错误，用户会被自动重定向到登录页面
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (card: Card) => {
    setEditingCard(card)
    setShowCreateForm(true)
  }

  const handleViewCalendar = (cardId: string) => {
    router.push(`/calendar?cardId=${cardId}`)
  }

  const handleDelete = async (cardId: string) => {
    const confirmed = await confirm({
      title: '删除卡片',
      message: '确定要删除这张卡片吗？删除后相关的交易记录也会被删除，此操作不可撤销。',
      confirmText: '删除',
      cancelText: '取消',
      type: 'danger'
    })

    if (!confirmed) return

    try {
      const response = await apiCall(`/api/cards/${cardId}`, {
        method: 'DELETE',
      })

      const data = await response.json()
      if (data.success) {
        // 硬删除：从列表中移除卡片
        setCards(prev => prev.filter(card => card.id !== cardId))
        showSuccess('删除成功', '卡片及相关交易记录已删除')
      } else {
        showError('删除失败', data.error || '请稍后重试')
      }
    } catch (error) {
      console.error('Delete failed:', error)
      showError('删除失败', '网络错误，请稍后重试')
    }
  }

  const handleToggleStatus = async (cardId: string, isActive: boolean) => {
    try {
      const response = await apiCall(`/api/cards/${cardId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive }),
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setCards(prev => prev.map(card =>
            card.id === cardId ? { ...card, isActive } : card
          ))
          showSuccess('状态更新成功', `卡片已${isActive ? '启用' : '停用'}`)
        }
      } else {
        showError('状态更新失败', '请稍后重试')
      }
    } catch (error) {
      console.error('Toggle status failed:', error)
      showError('状态更新失败', '网络错误，请稍后重试')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">卡片管理</h1>
            <div className="flex space-x-4">
              <Link
                href="/dashboard"
                className="text-blue-600 hover:text-blue-800 transition-colors"
              >
                返回仪表板
              </Link>
              <Button
                onClick={() => {
                  setEditingCard(null)
                  setShowCreateForm(true)
                }}
              >
                添加卡片
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总卡片数</p>
                <p className="text-2xl font-semibold text-gray-900">{cards.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">活跃卡片</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {cards.filter(card => card.isActive).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总交易数</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {cards.reduce((sum, card) => sum + card._count.transactions, 0)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总导入数</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {cards.reduce((sum, card) => sum + card._count.billImports, 0)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 卡片网格 */}
        <CardGrid
          cards={cards}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onViewCalendar={handleViewCalendar}
        />
      </div>

      {/* 创建/编辑表单模态框 */}
      {showCreateForm && (
        <CardForm
          card={editingCard}
          onSuccess={() => {
            setShowCreateForm(false)
            setEditingCard(null)
            fetchCards() // 重新获取卡片列表
          }}
          onCancel={() => {
            setShowCreateForm(false)
            setEditingCard(null)
          }}
        />
      )}
    </div>
  )
}

export default function CardsPage() {
  return (
    <ProtectedRoute>
      <ClientOnly fallback={
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        </div>
      }>
        <CardsPageContent />
      </ClientOnly>
    </ProtectedRoute>
  )
}
