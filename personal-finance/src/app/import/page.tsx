'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { ClientOnly } from '@/components/ClientOnly'
import Link from 'next/link'

interface Card {
  id: string
  name: string
  type: string
  accountNo?: string
  bankName?: string
  balance: number
  isActive: boolean
}

interface ImportResult {
  billImportId: string
  success: boolean
  totalCount: number
  successCount: number
  errorCount: number
  duplicateCount: number
  errors: string[]
}

function ImportPageContent() {
  const { apiCall, user } = useAuth()
  const [cards, setCards] = useState<Card[]>([])
  const [selectedCardId, setSelectedCardId] = useState('')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [loading, setLoading] = useState(false)
  const [importing, setImporting] = useState(false)
  const [result, setResult] = useState<ImportResult | null>(null)
  const [supportedTypes, setSupportedTypes] = useState<string[]>([])

  useEffect(() => {
    fetchCards()
    fetchSupportedTypes()
  }, [])

  const fetchCards = async () => {
    try {
      setLoading(true)
      const response = await apiCall('/api/cards')
      const data = await response.json()

      if (data.success) {
        setCards(data.data.filter((card: Card) => card.isActive))
      }
    } catch (error) {
      console.error('获取卡片失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchSupportedTypes = async () => {
    try {
      const response = await apiCall('/api/bills/import')
      const data = await response.json()

      if (data.success) {
        setSupportedTypes(data.data.supportedFileTypes)
      }
    } catch (error) {
      console.error('获取支持的文件类型失败:', error)
    }
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // 验证文件类型
      const extension = file.name.toLowerCase().split('.').pop()
      if (extension && supportedTypes.includes(extension)) {
        setSelectedFile(file)
        setResult(null)
      } else {
        alert(`不支持的文件类型。支持的类型: ${supportedTypes.join(', ')}`)
        event.target.value = ''
      }
    }
  }

  const handleImport = async () => {
    if (!selectedFile || !selectedCardId) {
      alert('请选择文件和卡片')
      return
    }

    try {
      setImporting(true)
      setResult(null)

      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('cardId', selectedCardId)

      const response = await apiCall('/api/bills/import', {
        method: 'POST',
        body: formData,
      })

      const data = await response.json()

      if (data.success) {
        setResult(data.data)
        setSelectedFile(null)
        setSelectedCardId('')
        // 重置文件输入
        const fileInput = document.getElementById('file-input') as HTMLInputElement
        if (fileInput) fileInput.value = ''
      } else {
        alert(`导入失败: ${data.error}`)
      }
    } catch (error) {
      console.error('导入失败:', error)
      alert('导入失败，请重试')
    } finally {
      setImporting(false)
    }
  }

  const getCardTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      DEBIT_CARD: '借记卡',
      CREDIT_CARD: '信用卡',
      WECHAT: '微信',
      ALIPAY: '支付宝',
      CASH: '现金',
      OTHER: '其他',
    }
    return labels[type] || type
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">账单导入</h1>
            <div className="flex space-x-4">
              <Link
                href="/dashboard"
                className="text-blue-600 hover:text-blue-800 transition-colors"
              >
                返回仪表板
              </Link>
            </div>
          </div>
          <p className="mt-2 text-gray-600">
            支持导入微信、支付宝、招商银行借记卡和信用卡账单
          </p>
        </div>

        {/* 导入表单 */}
        <div className="bg-white shadow rounded-lg p-6 mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">选择文件和卡片</h2>

          <div className="space-y-6">
            {/* 卡片选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择卡片
              </label>
              <select
                value={selectedCardId}
                onChange={(e) => setSelectedCardId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">请选择卡片</option>
                {cards.map((card) => (
                  <option key={card.id} value={card.id}>
                    {card.name} ({getCardTypeLabel(card.type)})
                    {card.accountNo && ` - ${card.accountNo}`}
                  </option>
                ))}
              </select>
            </div>

            {/* 文件选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择账单文件
              </label>
              <input
                id="file-input"
                type="file"
                accept={supportedTypes.map(type => `.${type}`).join(',')}
                onChange={handleFileSelect}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="mt-1 text-sm text-gray-500">
                支持的文件类型: {supportedTypes.join(', ')}
              </p>
            </div>

            {/* 导入按钮 */}
            <div>
              <button
                onClick={handleImport}
                disabled={!selectedFile || !selectedCardId || importing}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {importing ? '导入中...' : '开始导入'}
              </button>
            </div>
          </div>
        </div>

          {/* 导入结果 */}
          {result && (
            <div className="mt-6 bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">导入结果</h3>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{result.totalCount}</div>
                  <div className="text-sm text-gray-500">总记录数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{result.successCount}</div>
                  <div className="text-sm text-gray-500">成功导入</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">{result.duplicateCount}</div>
                  <div className="text-sm text-gray-500">重复跳过</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{result.errorCount}</div>
                  <div className="text-sm text-gray-500">导入失败</div>
                </div>
              </div>

              {result.errors.length > 0 && (
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-2">错误信息</h4>
                  <div className="bg-red-50 border border-red-200 rounded-md p-3">
                    <ul className="text-sm text-red-700 space-y-1">
                      {result.errors.map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              <div className="mt-6 flex justify-center">
                <Link
                  href="/dashboard"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  查看交易记录
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default function ImportPage() {
  return (
    <ProtectedRoute>
      <ClientOnly fallback={
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        </div>
      }>
        <ImportPageContent />
      </ClientOnly>
    </ProtectedRoute>
  )
}
