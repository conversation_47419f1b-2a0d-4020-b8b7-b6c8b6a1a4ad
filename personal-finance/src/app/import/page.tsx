'use client'

import { useState } from 'react'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { ClientOnly } from '@/components/ClientOnly'
import Link from 'next/link'

function ImportPageContent() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedFile(file)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">账单导入</h1>
            <Link
              href="/dashboard"
              className="text-blue-600 hover:text-blue-800 transition-colors"
            >
              返回仪表板
            </Link>
          </div>
          <p className="mt-2 text-gray-600">
            支持导入微信、支付宝、招商银行借记卡和信用卡账单
          </p>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">选择文件和卡片</h2>

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择账单文件
              </label>
              <input
                type="file"
                accept=".csv,.xlsx,.xls"
                onChange={handleFileSelect}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="mt-1 text-sm text-gray-500">
                支持的文件类型: csv, xlsx, xls
              </p>
            </div>

            <div>
              <button
                disabled={!selectedFile}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                开始导入
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function ImportPage() {
  return (
    <ProtectedRoute>
      <ClientOnly fallback={
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        </div>
      }>
        <ImportPageContent />
      </ClientOnly>
    </ProtectedRoute>
  )
}
