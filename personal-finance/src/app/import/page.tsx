'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSearchParams } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { ClientOnly } from '@/components/ClientOnly'
import Link from 'next/link'

interface Card {
  id: string
  name: string
  type: string
  accountNo?: string
  bankName?: string
  balance: number
  isActive: boolean
}

interface ImportResult {
  billImportId: string
  success: boolean
  totalCount: number
  successCount: number
  errorCount: number
  duplicateCount: number
  errors: string[]
}

function ImportPageContent() {
  const { apiCall, isAuthenticated, loading: authLoading } = useAuth()
  const searchParams = useSearchParams()
  const cardId = searchParams.get('cardId')

  const [card, setCard] = useState<Card | null>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [importing, setImporting] = useState(false)
  const [result, setResult] = useState<ImportResult | null>(null)
  const [supportedTypes, setSupportedTypes] = useState<string[]>([])
  const [loading, setLoading] = useState(true)

  const fetchCard = useCallback(async (id: string) => {
    try {
      const response = await apiCall(`/api/cards/${id}`)
      const data = await response.json()

      if (data.success) {
        setCard(data.data)
      } else {
        console.error('Failed to fetch card:', data.error)
      }
    } catch (error) {
      console.error('Failed to fetch card:', error)
    } finally {
      setLoading(false)
    }
  }, [apiCall])

  const fetchSupportedTypes = useCallback(async () => {
    try {
      const response = await apiCall('/api/bills/import')
      const data = await response.json()

      if (data.success) {
        setSupportedTypes(data.data.supportedFileTypes)
      }
    } catch (error) {
      console.error('获取支持的文件类型失败:', error)
    }
  }, [apiCall])

  useEffect(() => {
    // 只有在认证完成且用户已登录时才获取数据
    if (!authLoading && isAuthenticated) {
      fetchSupportedTypes()
      if (cardId) {
        fetchCard(cardId)
      } else {
        setLoading(false)
      }
    } else if (!authLoading && !isAuthenticated) {
      setLoading(false)
    }
  }, [cardId, fetchCard, fetchSupportedTypes, authLoading, isAuthenticated])

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // 验证文件类型
      const extension = file.name.toLowerCase().split('.').pop()
      if (extension && supportedTypes.includes(extension)) {
        setSelectedFile(file)
        setResult(null)
      } else {
        alert(`不支持的文件类型。支持的类型: ${supportedTypes.join(', ')}`)
        event.target.value = ''
      }
    }
  }

  const handleImport = async () => {
    if (!selectedFile || !cardId) {
      alert('请选择文件')
      return
    }

    try {
      setImporting(true)
      setResult(null)

      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('cardId', cardId)

      const response = await apiCall('/api/bills/import', {
        method: 'POST',
        body: formData,
      })

      const data = await response.json()

      if (data.success) {
        setResult(data.data)
        setSelectedFile(null)
        // 重置文件输入
        const fileInput = document.getElementById('file-input') as HTMLInputElement
        if (fileInput) fileInput.value = ''
      } else {
        alert(`导入失败: ${data.error}`)
      }
    } catch (error) {
      console.error('导入失败:', error)
      alert('导入失败，请重试')
    } finally {
      setImporting(false)
    }
  }

  const getCardTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      DEBIT_CARD: '借记卡',
      CREDIT_CARD: '信用卡',
      WECHAT: '微信',
      ALIPAY: '支付宝',
      CASH: '现金',
      OTHER: '其他',
    }
    return labels[type] || type
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (!cardId) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">账单导入</h1>
            <p className="text-gray-600 mb-8">请从卡片管理页面选择要导入账单的卡片</p>
            <Link
              href="/cards"
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              前往卡片管理
            </Link>
          </div>
        </div>
      </div>
    )
  }

  if (!card) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">卡片不存在</h1>
            <p className="text-gray-600 mb-8">指定的卡片不存在或您没有权限访问</p>
            <Link
              href="/cards"
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              返回卡片管理
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">账单导入</h1>
            <Link
              href="/cards"
              className="text-blue-600 hover:text-blue-800 transition-colors"
            >
              返回卡片管理
            </Link>
          </div>
          <p className="mt-2 text-gray-600">
            为 {card.name} ({getCardTypeLabel(card.type)}) 导入账单
          </p>
        </div>

        <div className="bg-white shadow rounded-lg p-6 mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">选择账单文件</h2>

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择账单文件
              </label>
              <input
                id="file-input"
                type="file"
                accept={supportedTypes.map(type => `.${type}`).join(',')}
                onChange={handleFileSelect}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
              />
              <p className="mt-1 text-sm text-gray-500">
                支持的文件类型: {supportedTypes.join(', ')}
              </p>
            </div>

            <div>
              <button
                onClick={handleImport}
                disabled={!selectedFile || importing}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {importing ? '导入中...' : '开始导入'}
              </button>
            </div>
          </div>
        </div>

        {/* 导入结果 */}
        {result && (
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">导入结果</h2>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{result.totalCount}</div>
                <div className="text-sm text-gray-500">总记录数</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{result.successCount}</div>
                <div className="text-sm text-gray-500">成功导入</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{result.duplicateCount}</div>
                <div className="text-sm text-gray-500">重复跳过</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{result.errorCount}</div>
                <div className="text-sm text-gray-500">导入失败</div>
              </div>
            </div>

            {result.errors.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-2">错误信息</h4>
                <div className="bg-red-50 border border-red-200 rounded-md p-3">
                  <ul className="text-sm text-red-700 space-y-1">
                    {result.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            <div className="mt-6 flex justify-center">
              <Link
                href="/dashboard"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                查看交易记录
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default function ImportPage() {
  return (
    <ProtectedRoute>
      <ClientOnly fallback={
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        </div>
      }>
        <ImportPageContent />
      </ClientOnly>
    </ProtectedRoute>
  )
}
