import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { createSuccessResponse, handleApiError } from '@/lib/api-response'
import { createUserSchema } from '@/lib/validations/user'

// GET /api/users - 获取用户列表
export async function GET() {
  try {
    const users = await prisma.user.findMany({
      orderBy: { createdAt: 'desc' },
      include: {
        _count: {
          select: {
            cards: true,
            transactions: true,
            accountBooks: true,
          },
        },
      },
    })

    return createSuccessResponse(users)
  } catch (error) {
    return handleApiError(error)
  }
}

// POST /api/users - 创建新用户
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = createUserSchema.parse(body)

    const user = await prisma.user.create({
      data: validatedData,
      include: {
        _count: {
          select: {
            cards: true,
            transactions: true,
            accountBooks: true,
          },
        },
      },
    })

    return createSuccessResponse(user, '用户创建成功')
  } catch (error) {
    return handleApiError(error)
  }
}
