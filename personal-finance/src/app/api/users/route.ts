import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { createSuccessResponse, handleApiError } from '@/lib/api-response'
import { requireAuth } from '@/lib/auth'

// GET /api/users - 获取用户列表（需要认证）
export const GET = requireAuth(async (_request: NextRequest, user) => {
  try {
    // 如果用户有家庭，只返回家庭成员
    let whereClause = {}
    if (user.familyId) {
      whereClause = { familyId: user.familyId }
    } else {
      // 如果没有家庭，只返回当前用户
      whereClause = { id: user.id }
    }

    const users = await prisma.user.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        name: true,
        email: true,
        familyRole: true,
        isActive: true,
        createdAt: true,
        _count: {
          select: {
            cards: true,
            transactions: true,
            accountBooks: true,
          },
        },
      },
    })

    return createSuccessResponse(users)
  } catch (error) {
    return handleApiError(error)
  }
})

// 注意：用户创建现在通过 /api/auth/register 进行
// 这个端点不再支持直接创建用户
