import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { createSuccessResponse, handleApiError, ApiError } from '@/lib/api-response'
import { updateUserSchema } from '@/lib/validations/user'

// GET /api/users/[id] - 获取单个用户
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      include: {
        cards: {
          where: { isActive: true },
          orderBy: { createdAt: 'desc' },
        },
        _count: {
          select: {
            cards: true,
            transactions: true,
            accountBooks: true,
          },
        },
      },
    })

    if (!user) {
      throw new ApiError('用户不存在', 404)
    }

    return createSuccessResponse(user)
  } catch (error) {
    return handleApiError(error)
  }
}

// PUT /api/users/[id] - 更新用户
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const validatedData = updateUserSchema.parse(body)

    const user = await prisma.user.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        _count: {
          select: {
            cards: true,
            transactions: true,
            accountBooks: true,
          },
        },
      },
    })

    return createSuccessResponse(user, '用户更新成功')
  } catch (error) {
    return handleApiError(error)
  }
}

// DELETE /api/users/[id] - 删除用户
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.user.delete({
      where: { id: params.id },
    })

    return createSuccessResponse(null, '用户删除成功')
  } catch (error) {
    return handleApiError(error)
  }
}
