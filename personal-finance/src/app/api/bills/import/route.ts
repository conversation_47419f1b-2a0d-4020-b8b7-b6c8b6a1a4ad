import { NextRequest } from 'next/server'
import { createSuccessResponse, handleApiError, ApiError } from '@/lib/api-response'
import { billImportService } from '@/lib/services/bill-import-service'
import { getUserFromRequest } from '@/lib/auth'

// POST /api/bills/import - 导入账单文件
export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const user = await getUserFromRequest(request)
    if (!user) {
      throw new ApiError('未授权访问', 401)
    }

    const formData = await request.formData()

    const file = formData.get('file') as File
    const cardId = formData.get('cardId') as string

    if (!file) {
      throw new ApiError('请选择要上传的文件', 400)
    }

    if (!cardId) {
      throw new ApiError('请指定卡片ID', 400)
    }

    // 验证文件大小（10MB限制）
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      throw new ApiError('文件大小不能超过10MB', 400)
    }

    // 验证文件类型
    const supportedTypes = billImportService.getSupportedFileTypes()
    const fileExtension = file.name.toLowerCase().split('.').pop()
    
    if (!fileExtension || !supportedTypes.includes(fileExtension)) {
      throw new ApiError(
        `不支持的文件类型。支持的类型: ${supportedTypes.join(', ')}`,
        400
      )
    }

    // 读取文件内容
    const buffer = Buffer.from(await file.arrayBuffer())

    // 导入账单
    const result = await billImportService.importBill({
      cardId,
      userId: user.id,
      file: buffer,
      fileName: file.name,
      fileSize: file.size,
    })

    return createSuccessResponse(result, '账单导入完成')
  } catch (error) {
    return handleApiError(error)
  }
}

// GET /api/bills/import - 获取支持的解析器信息
export async function GET() {
  try {
    const info = {
      supportedFileTypes: billImportService.getSupportedFileTypes(),
      parsers: billImportService.getParserInfo(),
      maxFileSize: '10MB',
    }

    return createSuccessResponse(info)
  } catch (error) {
    return handleApiError(error)
  }
}
