import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { createSuccessResponse, handleApiError } from '@/lib/api-response'
import { createTransactionSchema, transactionQuerySchema } from '@/lib/validations/transaction'
import { getUserFromRequest } from '@/lib/auth'

// GET /api/transactions - 获取交易记录列表
export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const user = await getUserFromRequest(request)
    if (!user) {
      return new Response(JSON.stringify({ success: false, error: '未授权访问' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    const { searchParams } = new URL(request.url)
    const query = transactionQuerySchema.parse(Object.fromEntries(searchParams))

    const where: any = {
      userId: user.id, // 只查询当前用户的交易记录
    }
    if (query.type) where.type = query.type
    if (query.status) where.status = query.status
    if (query.cardId) where.cardId = query.cardId
    if (query.category) where.category = query.category
    if (query.startDate || query.endDate) {
      where.transactionDate = {}
      if (query.startDate) where.transactionDate.gte = new Date(query.startDate)
      if (query.endDate) where.transactionDate.lte = new Date(query.endDate)
    }
    if (query.search) {
      where.OR = [
        { description: { contains: query.search } },
        { notes: { contains: query.search } },
        { location: { contains: query.search } },
      ]
    }

    const [transactions, total] = await Promise.all([
      prisma.transaction.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
            },
          },
          card: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
          linkedTransaction: {
            select: {
              id: true,
              description: true,
              amount: true,
            },
          },
        },
        orderBy: { transactionDate: 'desc' },
        skip: (query.page - 1) * query.limit,
        take: query.limit,
      }),
      prisma.transaction.count({ where }),
    ])

    const pagination = {
      page: query.page,
      limit: query.limit,
      total,
      totalPages: Math.ceil(total / query.limit),
    }

    return createSuccessResponse(transactions, undefined, pagination)
  } catch (error) {
    return handleApiError(error)
  }
}

// POST /api/transactions - 创建新交易记录
export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const user = await getUserFromRequest(request)
    if (!user) {
      return new Response(JSON.stringify({ success: false, error: '未授权访问' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    const body = await request.json()
    const validatedData = createTransactionSchema.parse({
      ...body,
      userId: user.id, // 确保交易属于当前用户
    })

    // 转换日期字符串为Date对象
    const transactionData = {
      ...validatedData,
      transactionDate: new Date(validatedData.transactionDate),
    }

    const transaction = await prisma.transaction.create({
      data: transactionData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        card: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
    })

    return createSuccessResponse(transaction, '交易记录创建成功')
  } catch (error) {
    return handleApiError(error)
  }
}
