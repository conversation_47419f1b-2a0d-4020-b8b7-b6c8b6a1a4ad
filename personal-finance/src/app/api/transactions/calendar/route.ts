import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { createSuccessResponse, handleApiError } from '@/lib/api-response'
import { getUserFromRequest } from '@/lib/auth'

interface DailyStats {
  date: string
  totalTransactions: number
  totalIncome: number
  totalExpense: number
  netAmount: number
  cardStats: {
    cardId: string
    cardName: string
    cardType: string
    transactionCount: number
    income: number
    expense: number
    netAmount: number
  }[]
}

interface CalendarStats {
  [date: string]: DailyStats
}

export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const user = await getUserFromRequest(request)
    if (!user) {
      return new Response(JSON.stringify({ success: false, error: '未授权访问' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    const { searchParams } = new URL(request.url)

    // 获取查询参数
    const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString())
    const month = parseInt(searchParams.get('month') || (new Date().getMonth() + 1).toString())
    const cardId = searchParams.get('cardId') // 可选：按特定卡片筛选

    // 验证参数
    if (year < 2000 || year > 3000) {
      return new Response(JSON.stringify({ success: false, error: '年份参数无效' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }
    if (month < 1 || month > 12) {
      return new Response(JSON.stringify({ success: false, error: '月份参数无效' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }
    
    // 计算月份的开始和结束日期
    const startDate = new Date(year, month - 1, 1)
    const endDate = new Date(year, month, 0, 23, 59, 59, 999)
    
    // 构建查询条件
    const whereCondition: any = {
      userId: user.id,
      transactionDate: {
        gte: startDate,
        lte: endDate,
      },
    }
    
    if (cardId) {
      whereCondition.cardId = cardId
    }
    
    // 查询交易记录
    const transactions = await prisma.transaction.findMany({
      where: whereCondition,
      include: {
        card: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
      orderBy: {
        transactionDate: 'asc',
      },
    })
    
    // 按日期分组统计
    const calendarStats: CalendarStats = {}
    
    transactions.forEach(transaction => {
      const dateKey = transaction.transactionDate.toISOString().split('T')[0]
      
      if (!calendarStats[dateKey]) {
        calendarStats[dateKey] = {
          date: dateKey,
          totalTransactions: 0,
          totalIncome: 0,
          totalExpense: 0,
          netAmount: 0,
          cardStats: [],
        }
      }
      
      const dayStats = calendarStats[dateKey]
      dayStats.totalTransactions++
      
      if (transaction.type === 'INCOME') {
        dayStats.totalIncome += transaction.amount
      } else {
        dayStats.totalExpense += transaction.amount
      }
      
      dayStats.netAmount = dayStats.totalIncome - dayStats.totalExpense
      
      // 按卡片统计
      let cardStat = dayStats.cardStats.find(cs => cs.cardId === transaction.cardId)
      if (!cardStat) {
        cardStat = {
          cardId: transaction.cardId || '',
          cardName: transaction.card?.name || '未知卡片',
          cardType: transaction.card?.type || 'UNKNOWN',
          transactionCount: 0,
          income: 0,
          expense: 0,
          netAmount: 0,
        }
        dayStats.cardStats.push(cardStat)
      }
      
      cardStat.transactionCount++
      if (transaction.type === 'INCOME') {
        cardStat.income += transaction.amount
      } else {
        cardStat.expense += transaction.amount
      }
      cardStat.netAmount = cardStat.income - cardStat.expense
    })
    
    // 获取用户的所有卡片（用于筛选器）
    const userCards = await prisma.card.findMany({
      where: {
        userId: user.id,
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        type: true,
      },
      orderBy: {
        name: 'asc',
      },
    })
    
    // 计算月度汇总
    const monthlyTotal = {
      totalTransactions: transactions.length,
      totalIncome: transactions
        .filter(t => t.type === 'INCOME')
        .reduce((sum, t) => sum + t.amount, 0),
      totalExpense: transactions
        .filter(t => t.type === 'EXPENSE')
        .reduce((sum, t) => sum + t.amount, 0),
    }
    monthlyTotal.netAmount = monthlyTotal.totalIncome - monthlyTotal.totalExpense
    
    return createSuccessResponse({
      year,
      month,
      calendarStats,
      monthlyTotal,
      userCards,
    })
    
  } catch (error) {
    console.error('Calendar stats API error:', error)
    return handleApiError(error)
  }
}
