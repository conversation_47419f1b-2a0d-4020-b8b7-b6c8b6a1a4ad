import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { createSuccessResponse, handleApiError, ApiError } from '@/lib/api-response'
import { registerSchema } from '@/lib/validations/user'
import { hashPassword, generateToken, isValidEmail, isValidPassword } from '@/lib/auth'

// POST /api/auth/register - 用户注册
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = registerSchema.parse(body)

    // 验证邮箱格式
    if (!isValidEmail(validatedData.email)) {
      throw new ApiError('邮箱格式不正确', 400)
    }

    // 验证密码强度
    const passwordValidation = isValidPassword(validatedData.password)
    if (!passwordValidation.valid) {
      throw new ApiError(passwordValidation.message || '密码格式不正确', 400)
    }

    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email },
    })

    if (existingUser) {
      throw new ApiError('该邮箱已被注册', 400)
    }

    // 加密密码
    const hashedPassword = await hashPassword(validatedData.password)

    // 创建用户
    const user = await prisma.user.create({
      data: {
        email: validatedData.email,
        password: hashedPassword,
        name: validatedData.name,
      },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        familyId: true,
        familyRole: true,
        createdAt: true,
      },
    })

    // 生成JWT令牌
    const token = generateToken({
      userId: user.id,
      email: user.email,
      familyId: user.familyId || undefined,
      familyRole: user.familyRole,
    })

    return createSuccessResponse(
      {
        user,
        token,
      },
      '注册成功'
    )
  } catch (error) {
    return handleApiError(error)
  }
}
