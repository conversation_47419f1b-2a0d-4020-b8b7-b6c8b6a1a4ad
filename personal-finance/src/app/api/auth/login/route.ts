import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { createSuccessResponse, handleApiError, ApiError } from '@/lib/api-response'
import { loginSchema } from '@/lib/validations/user'
import { verifyPassword, generateToken } from '@/lib/auth'

// POST /api/auth/login - 用户登录
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = loginSchema.parse(body)

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email: validatedData.email },
      include: {
        family: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    if (!user) {
      throw new ApiError('邮箱或密码错误', 401)
    }

    if (!user.isActive) {
      throw new ApiError('账户已被禁用', 401)
    }

    // 验证密码
    const isPasswordValid = await verifyPassword(validatedData.password, user.password)
    if (!isPasswordValid) {
      throw new ApiError('邮箱或密码错误', 401)
    }

    // 生成JWT令牌
    const token = generateToken({
      userId: user.id,
      email: user.email,
      familyId: user.familyId || undefined,
      familyRole: user.familyRole,
    })

    // 返回用户信息（不包含密码）
    const userResponse = {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      familyId: user.familyId,
      familyRole: user.familyRole,
      family: user.family,
      createdAt: user.createdAt,
    }

    return createSuccessResponse(
      {
        user: userResponse,
        token,
      },
      '登录成功'
    )
  } catch (error) {
    return handleApiError(error)
  }
}
