import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { createSuccessResponse, handleApiError, ApiError } from '@/lib/api-response'
import { updateCardSchema } from '@/lib/validations/card'
import { getUserFromRequest } from '@/lib/auth'

// GET /api/cards/[id] - 获取单个卡片
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户认证
    const user = await getUserFromRequest(request)
    if (!user) {
      return new Response(JSON.stringify({ success: false, error: '未授权访问' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    const { id } = await params
    const card = await prisma.card.findFirst({
      where: {
        id,
        userId: user.id, // 确保只能访问自己的卡片
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        transactions: {
          take: 10,
          orderBy: { transactionDate: 'desc' },
          select: {
            id: true,
            type: true,
            amount: true,
            description: true,
            transactionDate: true,
            status: true,
          },
        },
        _count: {
          select: {
            transactions: true,
            billImports: true,
          },
        },
      },
    })

    if (!card) {
      throw new ApiError('卡片不存在', 404)
    }

    return createSuccessResponse(card)
  } catch (error) {
    return handleApiError(error)
  }
}

// PUT /api/cards/[id] - 更新卡片
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户认证
    const user = await getUserFromRequest(request)
    if (!user) {
      return new Response(JSON.stringify({ success: false, error: '未授权访问' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    const { id } = await params
    // 检查卡片是否存在且属于当前用户
    const existingCard = await prisma.card.findFirst({
      where: {
        id,
        userId: user.id,
      },
    })

    if (!existingCard) {
      throw new ApiError('卡片不存在', 404)
    }

    const body = await request.json()
    const validatedData = updateCardSchema.parse(body)

    const card = await prisma.card.update({
      where: { id },
      data: validatedData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            transactions: true,
            billImports: true,
          },
        },
      },
    })

    return createSuccessResponse(card, '卡片更新成功')
  } catch (error) {
    return handleApiError(error)
  }
}

// DELETE /api/cards/[id] - 删除卡片
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户认证
    const user = await getUserFromRequest(request)
    if (!user) {
      return new Response(JSON.stringify({ success: false, error: '未授权访问' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    const { id } = await params
    // 检查卡片是否存在且属于当前用户
    const existingCard = await prisma.card.findFirst({
      where: {
        id,
        userId: user.id,
      },
      include: {
        _count: {
          select: {
            transactions: true,
            billImports: true,
          },
        },
      },
    })

    if (!existingCard) {
      throw new ApiError('卡片不存在', 404)
    }

    // 检查是否有关联的交易记录
    if (existingCard._count.transactions > 0) {
      throw new ApiError('无法删除有交易记录的卡片，请先删除相关交易记录', 400)
    }

    // 软删除：将卡片标记为不活跃
    const card = await prisma.card.update({
      where: { id },
      data: { isActive: false },
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            transactions: true,
            billImports: true,
          },
        },
      },
    })

    return createSuccessResponse(card, '卡片删除成功')
  } catch (error) {
    return handleApiError(error)
  }
}
