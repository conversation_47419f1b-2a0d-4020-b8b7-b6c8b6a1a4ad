import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { createSuccessResponse, handleApiError, ApiError } from '@/lib/api-response'
import { updateCardSchema } from '@/lib/validations/card'

// GET /api/cards/[id] - 获取单个卡片
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const card = await prisma.card.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        transactions: {
          take: 10,
          orderBy: { transactionDate: 'desc' },
          select: {
            id: true,
            type: true,
            amount: true,
            description: true,
            transactionDate: true,
            status: true,
          },
        },
        _count: {
          select: {
            transactions: true,
            billImports: true,
          },
        },
      },
    })

    if (!card) {
      throw new ApiError('卡片不存在', 404)
    }

    return createSuccessResponse(card)
  } catch (error) {
    return handleApiError(error)
  }
}

// PUT /api/cards/[id] - 更新卡片
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const validatedData = updateCardSchema.parse(body)

    const card = await prisma.card.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            transactions: true,
            billImports: true,
          },
        },
      },
    })

    return createSuccessResponse(card, '卡片更新成功')
  } catch (error) {
    return handleApiError(error)
  }
}

// DELETE /api/cards/[id] - 删除卡片
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.card.delete({
      where: { id: params.id },
    })

    return createSuccessResponse(null, '卡片删除成功')
  } catch (error) {
    return handleApiError(error)
  }
}
