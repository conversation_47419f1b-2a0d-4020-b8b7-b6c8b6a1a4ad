import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { createSuccessResponse, handleApiError } from '@/lib/api-response'
import { createCardSchema } from '@/lib/validations/card'
import { getUserFromRequest } from '@/lib/auth'

// GET /api/cards - 获取卡片列表
export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const user = await getUserFromRequest(request)
    if (!user) {
      return new Response(JSON.stringify({ success: false, error: '未授权访问' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const isActive = searchParams.get('isActive')

    const where: any = {
      userId: user.id, // 只返回当前用户的卡片
    }
    if (type) where.type = type
    if (isActive !== null) where.isActive = isActive === 'true'

    const cards = await prisma.card.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            transactions: true,
            billImports: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    })

    return createSuccessResponse(cards)
  } catch (error) {
    return handleApiError(error)
  }
}

// POST /api/cards - 创建新卡片
export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const user = await getUserFromRequest(request)
    if (!user) {
      return new Response(JSON.stringify({ success: false, error: '未授权访问' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    const body = await request.json()
    const validatedData = createCardSchema.parse({
      ...body,
      userId: user.id, // 确保卡片属于当前用户
    })

    const card = await prisma.card.create({
      data: validatedData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            transactions: true,
            billImports: true,
          },
        },
      },
    })

    return createSuccessResponse(card, '卡片创建成功')
  } catch (error) {
    return handleApiError(error)
  }
}
