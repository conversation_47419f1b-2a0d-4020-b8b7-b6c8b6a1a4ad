import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getUserFromRequest } from '@/lib/auth'
import { createSuccessResponse, createErrorResponse } from '@/lib/api-response'

export async function GET(
  request: NextRequest,
  { params }: { params: { cardId: string } }
) {
  try {
    const user = await getUserFromRequest(request)
    if (!user) {
      return createErrorResponse('未授权', 401)
    }

    const { cardId } = params
    const { searchParams } = new URL(request.url)
    const range = searchParams.get('range') || '3months'

    // 验证卡片是否存在且属于当前用户
    const card = await prisma.card.findFirst({
      where: {
        id: cardId,
        userId: user.userId,
      },
    })

    if (!card) {
      return createErrorResponse('卡片不存在', 404)
    }

    // 计算日期范围
    const now = new Date()
    let startDate: Date | undefined

    switch (range) {
      case '3months':
        startDate = new Date(now.getFullYear(), now.getMonth() - 3, 1)
        break
      case '6months':
        startDate = new Date(now.getFullYear(), now.getMonth() - 6, 1)
        break
      case '1year':
        startDate = new Date(now.getFullYear() - 1, now.getMonth(), 1)
        break
      case 'all':
        startDate = undefined
        break
      default:
        startDate = new Date(now.getFullYear(), now.getMonth() - 3, 1)
    }

    // 构建查询条件
    const whereCondition: any = {
      cardId,
      userId: user.userId,
    }

    if (startDate) {
      whereCondition.transactionDate = {
        gte: startDate,
      }
    }

    // 获取所有交易记录
    const transactions = await prisma.transaction.findMany({
      where: whereCondition,
      orderBy: {
        transactionDate: 'desc',
      },
    })

    // 计算基础统计
    const totalTransactions = transactions.length
    const totalIncome = transactions
      .filter(t => t.type === 'INCOME')
      .reduce((sum, t) => sum + t.amount, 0)
    const totalExpense = transactions
      .filter(t => t.type === 'EXPENSE')
      .reduce((sum, t) => sum + t.amount, 0)
    const netAmount = totalIncome - totalExpense

    // 分类统计
    const categoryMap = new Map<string, { count: number; amount: number }>()
    transactions.forEach(t => {
      const category = t.category || '未分类'
      const existing = categoryMap.get(category) || { count: 0, amount: 0 }
      categoryMap.set(category, {
        count: existing.count + 1,
        amount: existing.amount + t.amount,
      })
    })

    const categoryStats = Array.from(categoryMap.entries())
      .map(([category, data]) => ({
        category,
        count: data.count,
        amount: data.amount,
        percentage: totalTransactions > 0 ? (data.count / totalTransactions) * 100 : 0,
      }))
      .sort((a, b) => b.count - a.count)

    // 交易类型统计
    const transactionTypeMap = new Map<string, { count: number; amount: number }>()
    transactions.forEach(t => {
      const type = t.transactionType || '未知类型'
      const existing = transactionTypeMap.get(type) || { count: 0, amount: 0 }
      transactionTypeMap.set(type, {
        count: existing.count + 1,
        amount: existing.amount + t.amount,
      })
    })

    const transactionTypeStats = Array.from(transactionTypeMap.entries())
      .map(([type, data]) => ({
        type,
        count: data.count,
        amount: data.amount,
        percentage: totalTransactions > 0 ? (data.count / totalTransactions) * 100 : 0,
      }))
      .sort((a, b) => b.count - a.count)

    // 交易对方统计
    const counterPartyMap = new Map<string, { count: number; amount: number }>()
    transactions.forEach(t => {
      if (t.counterParty) {
        const counterParty = t.counterParty
        const existing = counterPartyMap.get(counterParty) || { count: 0, amount: 0 }
        counterPartyMap.set(counterParty, {
          count: existing.count + 1,
          amount: existing.amount + t.amount,
        })
      }
    })

    const counterPartyStats = Array.from(counterPartyMap.entries())
      .map(([counterParty, data]) => ({
        counterParty,
        count: data.count,
        amount: data.amount,
        percentage: totalTransactions > 0 ? (data.count / totalTransactions) * 100 : 0,
      }))
      .sort((a, b) => b.count - a.count)

    // 月度趋势分析
    const monthlyMap = new Map<string, { income: number; expense: number }>()
    transactions.forEach(t => {
      const date = new Date(t.transactionDate)
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
      const existing = monthlyMap.get(monthKey) || { income: 0, expense: 0 }
      
      if (t.type === 'INCOME') {
        existing.income += t.amount
      } else if (t.type === 'EXPENSE') {
        existing.expense += t.amount
      }
      
      monthlyMap.set(monthKey, existing)
    })

    const monthlyTrend = Array.from(monthlyMap.entries())
      .map(([month, data]) => ({
        month,
        income: data.income,
        expense: data.expense,
        net: data.income - data.expense,
      }))
      .sort((a, b) => a.month.localeCompare(b.month))

    // 最近交易记录
    const recentTransactions = transactions.slice(0, 10).map(t => ({
      id: t.id,
      type: t.type,
      amount: t.amount,
      description: t.description,
      transactionType: t.transactionType,
      counterParty: t.counterParty,
      category: t.category,
      transactionDate: t.transactionDate.toISOString(),
      createdAt: t.createdAt.toISOString(),
    }))

    const analysisData = {
      totalTransactions,
      totalIncome,
      totalExpense,
      netAmount,
      categoryStats,
      transactionTypeStats,
      counterPartyStats,
      monthlyTrend,
      recentTransactions,
    }

    return createSuccessResponse(analysisData)
  } catch (error) {
    console.error('Analysis API error:', error)
    return createErrorResponse('服务器错误', 500)
  }
}
