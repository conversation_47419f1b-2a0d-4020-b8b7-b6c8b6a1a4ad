import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { createSuccessResponse, handleApiError, ApiError } from '@/lib/api-response'
import { createFamilySchema } from '@/lib/validations/user'
import { requireAuth } from '@/lib/auth'
import { FamilyRole } from '@prisma/client'

// GET /api/families - 获取用户的家庭信息
export const GET = requireAuth(async (request: NextRequest, user) => {
  try {
    if (!user.familyId) {
      return createSuccessResponse(null, '您还没有加入任何家庭')
    }

    const family = await prisma.family.findUnique({
      where: { id: user.familyId },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        members: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
            familyRole: true,
            createdAt: true,
          },
          orderBy: {
            familyRole: 'asc',
          },
        },
        invitations: {
          where: {
            status: 'PENDING',
            expiresAt: {
              gt: new Date(),
            },
          },
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        _count: {
          select: {
            members: true,
          },
        },
      },
    })

    if (!family) {
      throw new ApiError('家庭不存在', 404)
    }

    return createSuccessResponse(family)
  } catch (error) {
    return handleApiError(error)
  }
})

// POST /api/families - 创建新家庭
export const POST = requireAuth(async (request: NextRequest, user) => {
  try {
    // 检查用户是否已经在家庭中
    if (user.familyId) {
      throw new ApiError('您已经在一个家庭中，请先退出当前家庭', 400)
    }

    const body = await request.json()
    const validatedData = createFamilySchema.parse(body)

    // 创建家庭
    const family = await prisma.family.create({
      data: {
        name: validatedData.name,
        description: validatedData.description,
        avatar: validatedData.avatar,
        creatorId: user.id,
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        _count: {
          select: {
            members: true,
          },
        },
      },
    })

    // 将创建者加入家庭并设为所有者
    await prisma.user.update({
      where: { id: user.id },
      data: {
        familyId: family.id,
        familyRole: FamilyRole.OWNER,
      },
    })

    return createSuccessResponse(family, '家庭创建成功')
  } catch (error) {
    return handleApiError(error)
  }
})
