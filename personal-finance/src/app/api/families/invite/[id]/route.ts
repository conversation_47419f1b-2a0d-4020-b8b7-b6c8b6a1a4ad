import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { createSuccessResponse, handleApiError, ApiError } from '@/lib/api-response'
import { respondInvitationSchema } from '@/lib/validations/user'
import { requireAuth } from '@/lib/auth'
import { InvitationStatus } from '@prisma/client'

// POST /api/families/invite/[id] - 响应家庭邀请
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return requireAuth(async (request: NextRequest, user) => {
    try {
      const body = await request.json()
      const validatedData = respondInvitationSchema.parse(body)

      // 查找邀请
      const invitation = await prisma.familyInvitation.findUnique({
        where: { id: params.id },
        include: {
          family: true,
        },
      })

      if (!invitation) {
        throw new ApiError('邀请不存在', 404)
      }

      // 验证邀请是否属于当前用户
      if (invitation.email !== user.email) {
        throw new ApiError('无权处理此邀请', 403)
      }

      // 检查邀请状态
      if (invitation.status !== InvitationStatus.PENDING) {
        throw new ApiError('邀请已被处理', 400)
      }

      // 检查邀请是否过期
      if (invitation.expiresAt < new Date()) {
        await prisma.familyInvitation.update({
          where: { id: invitation.id },
          data: { status: InvitationStatus.EXPIRED },
        })
        throw new ApiError('邀请已过期', 400)
      }

      if (validatedData.action === 'accept') {
        // 接受邀请
        await prisma.$transaction(async (tx) => {
          // 更新邀请状态
          await tx.familyInvitation.update({
            where: { id: invitation.id },
            data: { status: InvitationStatus.ACCEPTED },
          })

          // 将用户加入家庭
          await tx.user.update({
            where: { id: user.id },
            data: {
              familyId: invitation.familyId,
              familyRole: invitation.role,
            },
          })
        })

        return createSuccessResponse(
          { familyId: invitation.familyId, familyName: invitation.family.name },
          '成功加入家庭'
        )
      } else {
        // 拒绝邀请
        await prisma.familyInvitation.update({
          where: { id: invitation.id },
          data: { status: InvitationStatus.DECLINED },
        })

        return createSuccessResponse(null, '已拒绝邀请')
      }
    } catch (error) {
      return handleApiError(error)
    }
  })(request)
}

// GET /api/families/invite/[id] - 获取邀请详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const invitation = await prisma.familyInvitation.findUnique({
      where: { id: params.id },
      include: {
        family: {
          select: {
            id: true,
            name: true,
            description: true,
            avatar: true,
            _count: {
              select: {
                members: true,
              },
            },
          },
        },
        sender: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
      },
    })

    if (!invitation) {
      throw new ApiError('邀请不存在', 404)
    }

    // 检查邀请是否过期
    if (invitation.expiresAt < new Date() && invitation.status === InvitationStatus.PENDING) {
      await prisma.familyInvitation.update({
        where: { id: invitation.id },
        data: { status: InvitationStatus.EXPIRED },
      })
      invitation.status = InvitationStatus.EXPIRED
    }

    return createSuccessResponse(invitation)
  } catch (error) {
    return handleApiError(error)
  }
}
