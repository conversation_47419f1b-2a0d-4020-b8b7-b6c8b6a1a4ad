import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { createSuccessResponse, handleApiError, ApiError } from '@/lib/api-response'
import { inviteFamilyMemberSchema } from '@/lib/validations/user'
import { requireFamilyAdmin } from '@/lib/auth'
import { InvitationStatus } from '@prisma/client'

// POST /api/families/invite - 邀请家庭成员
export const POST = requireFamilyAdmin(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const validatedData = inviteFamilyMemberSchema.parse(body)

    // 检查被邀请人是否已经在家庭中
    const existingMember = await prisma.user.findFirst({
      where: {
        email: validatedData.email,
        familyId: user.familyId,
      },
    })

    if (existingMember) {
      throw new ApiError('该用户已经是家庭成员', 400)
    }

    // 检查是否已有待处理的邀请
    const existingInvitation = await prisma.familyInvitation.findFirst({
      where: {
        familyId: user.familyId!,
        email: validatedData.email,
        status: InvitationStatus.PENDING,
        expiresAt: {
          gt: new Date(),
        },
      },
    })

    if (existingInvitation) {
      throw new ApiError('已向该邮箱发送过邀请，请等待回复', 400)
    }

    // 查找被邀请的用户（如果已注册）
    const invitedUser = await prisma.user.findUnique({
      where: { email: validatedData.email },
    })

    // 计算过期时间
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + parseInt(process.env.INVITATION_EXPIRES_HOURS || '72'))

    // 创建邀请
    const invitation = await prisma.familyInvitation.create({
      data: {
        email: validatedData.email,
        role: validatedData.role,
        message: validatedData.message,
        expiresAt,
        familyId: user.familyId!,
        senderId: user.id,
        receiverId: invitedUser?.id,
      },
      include: {
        family: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
        sender: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    // TODO: 发送邀请邮件
    // await sendInvitationEmail(invitation)

    return createSuccessResponse(invitation, '邀请发送成功')
  } catch (error) {
    return handleApiError(error)
  }
})

// GET /api/families/invite - 获取家庭的所有邀请
export const GET = requireFamilyAdmin(async (request: NextRequest, user) => {
  try {
    const invitations = await prisma.familyInvitation.findMany({
      where: {
        familyId: user.familyId!,
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        receiver: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return createSuccessResponse(invitations)
  } catch (error) {
    return handleApiError(error)
  }
})
