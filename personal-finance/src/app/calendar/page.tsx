'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { useToast } from '@/hooks/useToast'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { CalendarView } from '@/components/calendar/CalendarView'
import { CalendarFilters } from '@/components/calendar/CalendarFilters'
import { CalendarStats } from '@/components/calendar/CalendarStats'

interface Card {
  id: string
  name: string
  type: string
}

interface DailyStats {
  date: string
  totalTransactions: number
  totalIncome: number
  totalExpense: number
  netAmount: number
  cardStats: {
    cardId: string
    cardName: string
    cardType: string
    transactionCount: number
    income: number
    expense: number
    netAmount: number
  }[]
}

interface CalendarData {
  year: number
  month: number
  calendarStats: { [date: string]: DailyStats }
  monthlyTotal: {
    totalTransactions: number
    totalIncome: number
    totalExpense: number
    netAmount: number
  }
  userCards: Card[]
}

function CalendarPageContent() {
  const { apiCall, isAuthenticated, loading: authLoading } = useAuth()
  const { showError } = useToast()
  const searchParams = useSearchParams()

  const [loading, setLoading] = useState(true)
  const [calendarData, setCalendarData] = useState<CalendarData | null>(null)
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedCardId, setSelectedCardId] = useState<string>('')
  const [selectedDate, setSelectedDate] = useState<string | null>(null)

  const fetchCalendarData = async (year: number, month: number, cardId?: string) => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        year: year.toString(),
        month: month.toString(),
      })
      
      if (cardId) {
        params.append('cardId', cardId)
      }
      
      const response = await apiCall(`/api/transactions/calendar?${params}`)
      const data = await response.json()
      
      if (data.success) {
        setCalendarData(data.data)
      } else {
        showError('获取日历数据失败', data.error)
      }
    } catch (error) {
      console.error('获取日历数据失败:', error)
      showError('获取日历数据失败', '网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  // 从URL参数中读取cardId
  useEffect(() => {
    const cardIdFromUrl = searchParams.get('cardId')
    if (cardIdFromUrl) {
      setSelectedCardId(cardIdFromUrl)
    }
  }, [searchParams])

  useEffect(() => {
    if (isAuthenticated) {
      const year = currentDate.getFullYear()
      const month = currentDate.getMonth() + 1
      fetchCalendarData(year, month, selectedCardId)
    }
  }, [currentDate, selectedCardId, isAuthenticated])

  const handlePrevMonth = () => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      newDate.setMonth(prev.getMonth() - 1)
      return newDate
    })
  }

  const handleNextMonth = () => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      newDate.setMonth(prev.getMonth() + 1)
      return newDate
    })
  }

  const handleCardFilter = (cardId: string) => {
    setSelectedCardId(cardId)
  }

  const handleDateSelect = (date: string) => {
    setSelectedDate(selectedDate === date ? null : date)
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">
            {authLoading ? '验证用户身份中...' : '加载日历数据中...'}
          </p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">请先登录以查看日历数据</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">交易日历</h1>
          <p className="mt-2 text-gray-600">查看每日交易统计和趋势</p>
        </div>

        {/* 筛选器 */}
        {calendarData && (
          <CalendarFilters
            cards={calendarData.userCards}
            selectedCardId={selectedCardId}
            onCardChange={handleCardFilter}
            currentDate={currentDate}
            onPrevMonth={handlePrevMonth}
            onNextMonth={handleNextMonth}
          />
        )}

        {/* 月度统计 */}
        {calendarData && (
          <CalendarStats
            monthlyTotal={calendarData.monthlyTotal}
            year={calendarData.year}
            month={calendarData.month}
          />
        )}

        {/* 日历视图 */}
        {calendarData && (
          <CalendarView
            year={calendarData.year}
            month={calendarData.month}
            calendarStats={calendarData.calendarStats}
            selectedDate={selectedDate}
            onDateSelect={handleDateSelect}
          />
        )}
      </div>
    </div>
  )
}

export default function CalendarPage() {
  return (
    <ProtectedRoute>
      <CalendarPageContent />
    </ProtectedRoute>
  )
}
