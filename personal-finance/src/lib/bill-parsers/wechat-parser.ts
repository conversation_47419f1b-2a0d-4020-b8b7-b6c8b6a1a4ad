import { TransactionType } from '@prisma/client'
import { BaseBillParser } from './base-parser'
import { ParseResult, ParsedTransaction } from './types'

interface WeChatMetadata {
  nickname: string
  startDate: string
  endDate: string
  exportType: string
  exportTime: string
  summary: {
    totalCount: number
    incomeCount: number
    incomeAmount: number
    expenseCount: number
    expenseAmount: number
    neutralCount: number
    neutralAmount: number
  }
}

interface WeChatTransaction {
  timestamp: string
  type: string
  counterparty: string
  goods: string
  direction: 'income' | 'expense' | 'neutral'
  amount: number
  paymentMethod: string
  status: string
  transactionId: string
  merchantOrderId: string
  memo: string
}

interface WeChatBill {
  metadata: WeChatMetadata
  transactions: WeChatTransaction[]
}

export class WeChatBillParser extends BaseBillParser {
  readonly type = 'WECHAT'
  readonly name = '微信支付账单解析器'
  readonly supportedFileTypes = ['csv']

  protected getDescription(): string {
    return '解析微信支付账单CSV文件，支持收入、支出和转账记录，包含完整的元数据解析'
  }

  canParse(file: Buffer, fileName: string): boolean {
    if (!this.isFileTypeSupported(fileName)) {
      return false
    }

    // 检查文件内容是否包含微信账单的特征
    const content = file.toString('utf-8')
    return content.includes('微信昵称') ||
           content.includes('微信支付账单') ||
           (content.includes('交易时间') && content.includes('交易类型') && content.includes('交易对方'))
  }

  async parse(file: Buffer, fileName: string): Promise<ParseResult> {
    try {
      const content = file.toString('utf-8')
      const wechatBill = this.parseWeChatBill(content)

      const transactions: ParsedTransaction[] = []
      const errors: string[] = []

      // 转换微信交易为标准交易格式
      wechatBill.transactions.forEach((wechatTx, index) => {
        try {
          const transaction = this.convertWeChatTransaction(wechatTx)
          if (transaction) {
            transactions.push(transaction)
          }
        } catch (error) {
          errors.push(`第${index + 1}行解析失败: ${error instanceof Error ? error.message : '未知错误'}`)
        }
      })

      return this.createPartialResult(transactions, errors)
    } catch (error) {
      return this.createErrorResult([`文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`])
    }
  }

  private parseWeChatBill(csvContent: string): WeChatBill {
    const lines = csvContent.split('\n').map(line => line.trim())

    // Parse metadata
    const metadata: WeChatMetadata = {
      nickname: this.extractValue(lines, '微信昵称：'),
      startDate: this.extractValue(lines, '起始时间：'),
      endDate: this.extractValue(lines, '终止时间：'),
      exportType: this.extractValue(lines, '导出类型：'),
      exportTime: this.extractValue(lines, '导出时间：'),
      summary: {
        totalCount: this.extractNumber(lines, '共'),
        incomeCount: this.extractNumber(lines, '收入：'),
        incomeAmount: this.extractAmount(lines, '收入：'),
        expenseCount: this.extractNumber(lines, '支出：'),
        expenseAmount: this.extractAmount(lines, '支出：'),
        neutralCount: this.extractNumber(lines, '中性交易：'),
        neutralAmount: this.extractAmount(lines, '中性交易：'),
      }
    }

    // Find transactions start index
    const transactionStartIndex = lines.findIndex(line =>
      line.startsWith('交易时间,交易类型,交易对方'))

    if (transactionStartIndex === -1) {
      throw new Error('未找到交易数据表头')
    }

    // Parse transactions
    const transactions: WeChatTransaction[] = lines
      .slice(transactionStartIndex + 1)
      .filter(line => line && !line.startsWith('-') && !line.startsWith('注：') && !line.startsWith('共'))
      .map(line => this.parseWeChatTransactionLine(line))
      .filter(tx => tx !== null) as WeChatTransaction[]

    return { metadata, transactions }
  }

  private parseWeChatTransactionLine(line: string): WeChatTransaction | null {
    // 更精确的CSV解析，处理引号内的逗号
    const fields: string[] = []
    let field = ''
    let inQuotes = false

    for (let i = 0; i < line.length; i++) {
      const char = line[i]

      if (char === '"') {
        inQuotes = !inQuotes
        continue
      }

      if (char === ',' && !inQuotes) {
        fields.push(field.trim())
        field = ''
        continue
      }

      field += char
    }
    fields.push(field.trim()) // 添加最后一个字段

    if (fields.length < 11) {
      return null // 字段不足，跳过
    }

    const [
      timestamp, type, counterparty, goods, direction,
      amount, paymentMethod, status, transactionId,
      merchantOrderId, memo
    ] = fields

    return {
      timestamp,
      type,
      counterparty,
      goods,
      direction: this.parseDirection(direction),
      amount: this.parseAmount(amount),
      paymentMethod,
      status,
      transactionId,
      merchantOrderId,
      memo
    }
  }

  private convertWeChatTransaction(wechatTx: WeChatTransaction): ParsedTransaction | null {
    // 只处理成功的交易
    if (wechatTx.status !== '支付成功' && wechatTx.status !== '已收钱' && wechatTx.status !== '交易成功') {
      return null
    }

    // 解析交易类型
    const transactionType = this.parseWeChatTransactionType(wechatTx.type, wechatTx.direction)

    // 解析日期
    const transactionDate = this.parseDate(wechatTx.timestamp)

    // 生成外部ID（用于去重）
    const externalId = this.generateWeChatExternalId(wechatTx)

    return {
      externalId,
      type: transactionType,
      amount: Math.abs(wechatTx.amount), // 确保金额为正数
      description: this.buildWeChatDescription(wechatTx),
      category: this.inferCategory(wechatTx.counterparty, wechatTx.goods),
      transactionDate,
      location: wechatTx.counterparty,
      notes: `支付方式: ${wechatTx.paymentMethod}${wechatTx.memo ? ` | 备注: ${wechatTx.memo}` : ''}`,
    }
  }

  private extractValue(lines: string[], prefix: string): string {
    const line = lines.find(line => line.startsWith(prefix))
    return line ? line.split('：')[1]?.replace(/[\[\]]/g, '') || '' : ''
  }

  private extractNumber(lines: string[], prefix: string): number {
    const line = lines.find(line => line.startsWith(prefix))
    return line ? parseInt(line.match(/\d+/)?.[0] || '0') : 0
  }

  private extractAmount(lines: string[], prefix: string): number {
    const line = lines.find(line => line.startsWith(prefix))
    return line ? this.parseAmount(line.match(/\d+\.\d+/)?.[0] || '0') : 0
  }

  private parseDirection(direction: string): 'income' | 'expense' | 'neutral' {
    if (direction.includes('收入')) return 'income'
    if (direction.includes('支出')) return 'expense'
    return 'neutral'
  }

  private parseWeChatTransactionType(typeStr: string, direction: 'income' | 'expense' | 'neutral'): TransactionType {
    if (direction === 'income' || typeStr.includes('收入') || typeStr.includes('收钱') || typeStr.includes('退款')) {
      return TransactionType.INCOME
    } else if (typeStr.includes('转账')) {
      return TransactionType.TRANSFER
    } else {
      return TransactionType.EXPENSE
    }
  }

  private buildWeChatDescription(wechatTx: WeChatTransaction): string {
    const parts = [wechatTx.type]
    if (wechatTx.goods) {
      parts.push(wechatTx.goods)
    }
    if (wechatTx.counterparty) {
      parts.push(`- ${wechatTx.counterparty}`)
    }
    return parts.join(' ')
  }

  private generateWeChatExternalId(wechatTx: WeChatTransaction): string {
    // 使用交易时间、金额、交易对方、交易单号生成唯一ID
    const key = `${wechatTx.timestamp}_${wechatTx.amount}_${wechatTx.counterparty}_${wechatTx.transactionId}`
    return Buffer.from(key).toString('base64').substring(0, 20)
  }

  // 重写parseDate方法以支持微信的时间格式
  protected parseDate(dateStr: string): Date {
    // 微信时间格式: 2024-01-15 14:30:00
    const wechatFormat = /(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})/
    const match = dateStr.match(wechatFormat)

    if (match) {
      const [, year, month, day, hour, minute, second] = match
      return new Date(
        parseInt(year),
        parseInt(month) - 1, // 月份从0开始
        parseInt(day),
        parseInt(hour),
        parseInt(minute),
        parseInt(second)
      )
    }

    // 如果不匹配微信格式，使用父类的方法
    return super.parseDate(dateStr)
  }



  private inferCategory(merchant: string, product: string): string {
    const text = `${merchant} ${product}`.toLowerCase()

    if (text.includes('餐') || text.includes('食') || text.includes('饭') || text.includes('咖啡')) {
      return '餐饮'
    } else if (text.includes('交通') || text.includes('地铁') || text.includes('公交') || text.includes('打车')) {
      return '交通'
    } else if (text.includes('购物') || text.includes('商城') || text.includes('超市')) {
      return '购物'
    } else if (text.includes('娱乐') || text.includes('电影') || text.includes('游戏')) {
      return '娱乐'
    } else if (text.includes('医') || text.includes('药')) {
      return '医疗'
    } else if (text.includes('教育') || text.includes('学')) {
      return '教育'
    } else if (text.includes('房') || text.includes('租')) {
      return '住房'
    } else {
      return '其他'
    }
  }
}
