import { prisma } from '@/lib/prisma'
import { ParserFactory } from '@/lib/parsers/factory'
import { BillImportStatus, TransactionStatus } from '@prisma/client'
import { StandardTransaction } from '@/lib/parsers/types'

export interface ImportBillOptions {
  cardId: string
  userId: string
  file: Buffer
  fileName: string
  fileSize: number
}

export interface ImportResult {
  billImportId: string
  success: boolean
  totalCount: number
  successCount: number
  errorCount: number
  duplicateCount: number
  errors: string[]
}

export class BillImportService {
  async importBill(options: ImportBillOptions): Promise<ImportResult> {
    const { cardId, userId, file, fileName, fileSize } = options

    // 创建账单导入记录
    const billImport = await prisma.billImport.create({
      data: {
        fileName,
        fileType: this.getFileType(fileName),
        fileSize,
        status: BillImportStatus.PROCESSING,
        cardId,
      },
    })

    try {
      // 获取卡片信息
      const card = await prisma.card.findUnique({
        where: { id: cardId },
      })

      if (!card) {
        await this.updateBillImportStatus(billImport.id, BillImportStatus.FAILED, {
          errorLog: '卡片不存在',
        })
        return {
          billImportId: billImport.id,
          success: false,
          totalCount: 0,
          successCount: 0,
          errorCount: 1,
          duplicateCount: 0,
          errors: ['卡片不存在'],
        }
      }

      // 获取合适的解析器
      const parser = ParserFactory.getParser(fileName, card.type)
      if (!parser) {
        await this.updateBillImportStatus(billImport.id, BillImportStatus.FAILED, {
          errorLog: '未找到合适的解析器',
        })
        return {
          billImportId: billImport.id,
          success: false,
          totalCount: 0,
          successCount: 0,
          errorCount: 1,
          duplicateCount: 0,
          errors: ['未找到合适的解析器'],
        }
      }

      // 解析文件
      const parseResult = await parser.parse(file, card.type)
      
      if (!parseResult.success && parseResult.transactions.length === 0) {
        await this.updateBillImportStatus(billImport.id, BillImportStatus.FAILED, {
          errorLog: parseResult.errors.join('\n'),
        })
        return {
          billImportId: billImport.id,
          success: false,
          totalCount: parseResult.summary.totalCount,
          successCount: 0,
          errorCount: parseResult.summary.errorCount,
          duplicateCount: 0,
          errors: parseResult.errors,
        }
      }

      // 导入交易记录
      const importResult = await this.importTransactions(
        parseResult.transactions,
        billImport.id,
        cardId,
        userId
      )

      // 更新账单导入状态
      await this.updateBillImportStatus(billImport.id, BillImportStatus.COMPLETED, {
        totalCount: parseResult.summary.totalCount,
        successCount: importResult.successCount,
        errorCount: parseResult.summary.errorCount + importResult.errorCount,
        errorLog: [...parseResult.errors, ...importResult.errors].join('\n'),
      })

      return {
        billImportId: billImport.id,
        success: true,
        totalCount: parseResult.summary.totalCount,
        successCount: importResult.successCount,
        errorCount: parseResult.summary.errorCount + importResult.errorCount,
        duplicateCount: importResult.duplicateCount,
        errors: [...parseResult.errors, ...importResult.errors],
      }
    } catch (error) {
      await this.updateBillImportStatus(billImport.id, BillImportStatus.FAILED, {
        errorLog: error instanceof Error ? error.message : '未知错误',
      })
      
      return {
        billImportId: billImport.id,
        success: false,
        totalCount: 0,
        successCount: 0,
        errorCount: 1,
        duplicateCount: 0,
        errors: [error instanceof Error ? error.message : '未知错误'],
      }
    }
  }

  private async importTransactions(
    transactions: StandardTransaction[],
    billImportId: string,
    cardId: string,
    userId: string
  ): Promise<{
    successCount: number
    errorCount: number
    duplicateCount: number
    errors: string[]
  }> {
    let successCount = 0
    let errorCount = 0
    let duplicateCount = 0
    const errors: string[] = []

    for (const transaction of transactions) {
      try {
        // 检查是否已存在相同的交易（基于外部ID和卡片ID）
        if (transaction.externalId) {
          const existing = await prisma.transaction.findFirst({
            where: {
              externalId: transaction.externalId,
              cardId,
            },
          })

          if (existing) {
            duplicateCount++
            continue
          }
        }

        // 创建交易记录
        await prisma.transaction.create({
          data: {
            externalId: transaction.externalId,
            type: transaction.type,
            status: TransactionStatus.CONFIRMED,
            amount: transaction.amount,
            description: transaction.description,
            category: transaction.category,
            subcategory: transaction.subcategory,
            transactionDate: transaction.transactionDate,
            location: transaction.location,
            notes: transaction.notes,
            userId,
            cardId,
            billImportId,
          },
        })

        successCount++
      } catch (error) {
        errorCount++
        errors.push(`交易导入失败: ${error instanceof Error ? error.message : '未知错误'}`)
      }
    }

    return { successCount, errorCount, duplicateCount, errors }
  }

  private async updateBillImportStatus(
    billImportId: string,
    status: BillImportStatus,
    data: {
      totalCount?: number
      successCount?: number
      errorCount?: number
      errorLog?: string
    }
  ): Promise<void> {
    await prisma.billImport.update({
      where: { id: billImportId },
      data: {
        status,
        ...data,
        updatedAt: new Date(),
      },
    })
  }

  private getFileType(fileName: string): string {
    const extension = fileName.toLowerCase().split('.').pop()
    return extension || 'unknown'
  }

  // 获取支持的文件类型
  getSupportedFileTypes(): string[] {
    return ParserFactory.getSupportedFileTypes()
  }

  // 获取解析器信息
  getParserInfo() {
    return ParserFactory.getAllParsers().map(parser => ({
      name: parser.name,
      supportedCardTypes: parser.supportedCardTypes,
      supportedFileTypes: parser.supportedFileTypes,
    }))
  }
}

export const billImportService = new BillImportService()
