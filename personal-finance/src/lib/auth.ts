import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { NextRequest } from 'next/server'
import { prisma } from './prisma'

const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'
const BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12')

export interface JWTPayload {
  userId: string
  email: string
  familyId?: string
  familyRole?: string
}

export interface AuthUser {
  id: string
  email: string
  name: string
  avatar?: string
  familyId?: string
  familyRole?: string
  family?: {
    id: string
    name: string
    role: string
  }
}

// 密码加密
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, BCRYPT_ROUNDS)
}

// 密码验证
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

// 生成JWT令牌
export function generateToken(payload: JWTPayload): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })
}

// 验证JWT令牌
export function verifyToken(token: string): JWTPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as JWTPayload
  } catch (error) {
    return null
  }
}

// 从请求中获取用户信息
export async function getUserFromRequest(request: NextRequest): Promise<AuthUser | null> {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null
    }

    const token = authHeader.substring(7)
    const payload = verifyToken(token)
    if (!payload) {
      return null
    }

    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      include: {
        family: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    if (!user || !user.isActive) {
      return null
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar || undefined,
      familyId: user.familyId || undefined,
      familyRole: user.familyRole,
      family: user.family ? {
        id: user.family.id,
        name: user.family.name,
        role: user.familyRole,
      } : undefined,
    }
  } catch (error) {
    console.error('Error getting user from request:', error)
    return null
  }
}

// 中间件：验证用户认证
export function requireAuth(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {
  return async (request: NextRequest) => {
    const user = await getUserFromRequest(request)
    if (!user) {
      return new Response(
        JSON.stringify({ success: false, error: '未授权访问' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }
    return handler(request, user)
  }
}

// 中间件：验证家庭成员权限
export function requireFamilyMember(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {
  return async (request: NextRequest) => {
    const user = await getUserFromRequest(request)
    if (!user) {
      return new Response(
        JSON.stringify({ success: false, error: '未授权访问' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }

    if (!user.familyId) {
      return new Response(
        JSON.stringify({ success: false, error: '您还没有加入任何家庭' }),
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      )
    }

    return handler(request, user)
  }
}

// 中间件：验证家庭管理员权限
export function requireFamilyAdmin(handler: (request: NextRequest, user: AuthUser) => Promise<Response>) {
  return async (request: NextRequest) => {
    const user = await getUserFromRequest(request)
    if (!user) {
      return new Response(
        JSON.stringify({ success: false, error: '未授权访问' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }

    if (!user.familyId) {
      return new Response(
        JSON.stringify({ success: false, error: '您还没有加入任何家庭' }),
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      )
    }

    if (user.familyRole !== 'OWNER' && user.familyRole !== 'ADMIN') {
      return new Response(
        JSON.stringify({ success: false, error: '需要管理员权限' }),
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      )
    }

    return handler(request, user)
  }
}

// 验证邮箱格式
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 验证密码强度
export function isValidPassword(password: string): { valid: boolean; message?: string } {
  if (password.length < 8) {
    return { valid: false, message: '密码长度至少8位' }
  }
  
  if (!/(?=.*[a-z])/.test(password)) {
    return { valid: false, message: '密码必须包含小写字母' }
  }
  
  if (!/(?=.*[A-Z])/.test(password)) {
    return { valid: false, message: '密码必须包含大写字母' }
  }
  
  if (!/(?=.*\d)/.test(password)) {
    return { valid: false, message: '密码必须包含数字' }
  }
  
  return { valid: true }
}
