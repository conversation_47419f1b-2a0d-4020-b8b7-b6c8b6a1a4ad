export interface WeChatMetadata {
  nickname: string
  startDate: string
  endDate: string
  exportType: string
  exportTime: string
  summary: {
    totalCount: number
    incomeCount: number
    incomeAmount: number
    expenseCount: number
    expenseAmount: number
    neutralCount: number
    neutralAmount: number
  }
}

export interface WeChatTransaction {
  timestamp: string
  type: string
  counterparty: string
  goods: string
  direction: 'income' | 'expense' | 'neutral'
  amount: number
  paymentMethod: string
  status: string
  transactionId: string
  merchantOrderId: string
  memo: string
}

export interface WeChatBill {
  metadata: WeChatMetadata
  transactions: WeChatTransaction[]
}
