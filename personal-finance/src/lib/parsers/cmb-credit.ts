// 招商银行信用卡账单解析器

import { CardType } from '@prisma/client'
import { BaseBillParser } from './base'
import { RawTransaction, ParseResult } from './types'

export class CmbCreditParser extends BaseBillParser {
  name = '招商银行信用卡账单解析器'
  supportedCardTypes = [CardType.CREDIT_CARD]
  supportedFileTypes = ['csv', 'xls', 'xlsx']

  async parse(fileContent: string | Buffer, cardType: CardType): Promise<ParseResult> {
    try {
      const content = fileContent.toString('utf-8')
      const lines = this.parseCSV(content)
      
      // 查找表头行
      let headerIndex = -1
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].some(cell => 
          cell.includes('交易日期') || 
          cell.includes('记账日期') ||
          cell.includes('交易时间')
        )) {
          headerIndex = i
          break
        }
      }

      if (headerIndex === -1) {
        return this.createParseResult([], ['未找到有效的表头'])
      }

      const headers = lines[headerIndex]
      const dataLines = lines.slice(headerIndex + 1)

      // 解析每一行数据
      const transactions: RawTransaction[] = []
      const errors: string[] = []

      for (let i = 0; i < dataLines.length; i++) {
        try {
          const row = dataLines[i]
          if (row.length < headers.length || row.every(cell => !cell.trim())) {
            continue // 跳过空行或不完整行
          }

          const transaction = this.parseCmbCreditRow(row, headers)
          if (transaction) {
            transactions.push(transaction)
          }
        } catch (error) {
          errors.push(`第${headerIndex + i + 2}行解析失败: ${error}`)
        }
      }

      // 标准化交易数据
      const standardTransactions = transactions.map(raw => 
        this.standardizeTransaction(raw, cardType)
      )

      return this.createParseResult(standardTransactions, errors)
    } catch (error) {
      return this.createParseResult([], [`文件解析失败: ${error}`])
    }
  }

  private parseCmbCreditRow(row: string[], headers: string[]): RawTransaction | null {
    // 创建字段映射
    const fieldMap: Record<string, string> = {}
    headers.forEach((header, index) => {
      if (row[index]) {
        fieldMap[header.trim()] = row[index].trim()
      }
    })

    // 招商银行信用卡账单常见字段映射
    const dateField = this.findField(fieldMap, ['交易日期', '记账日期', '交易时间'])
    const typeField = this.findField(fieldMap, ['交易类型', '摘要', '业务类型'])
    const descField = this.findField(fieldMap, ['交易说明', '备注', '摘要说明', '商户名称'])
    const amountField = this.findField(fieldMap, ['交易金额', '金额', '人民币金额'])
    const currencyField = this.findField(fieldMap, ['币种', '交易币种'])
    const merchantField = this.findField(fieldMap, ['商户名称', '商户'])
    const referenceField = this.findField(fieldMap, ['交易流水号', '流水号', '凭证号'])
    const statusField = this.findField(fieldMap, ['交易状态', '状态'])

    if (!dateField || !amountField) {
      return null
    }

    // 解析时间
    const date = this.parseDate(dateField)
    if (!date) {
      throw new Error(`无效的时间格式: ${dateField}`)
    }

    // 解析金额
    let amount = this.parseAmount(amountField)
    
    // 信用卡：正数表示支出（消费），负数表示收入（还款/退款）
    // 招商银行信用卡通常用+/-符号或者单独的借贷标识
    if (amountField.includes('-') || amountField.includes('还款') || amountField.includes('退款')) {
      amount = -Math.abs(amount) // 负数表示还款/退款（对信用卡来说是收入）
    } else if (amountField.includes('+') || typeField?.includes('消费')) {
      amount = Math.abs(amount) // 正数表示消费（对信用卡来说是支出）
    } else {
      // 根据交易类型判断
      if (typeField) {
        if (typeField.includes('还款') || 
            typeField.includes('退款') ||
            typeField.includes('溢缴款') ||
            typeField.includes('调整')) {
          amount = -Math.abs(amount) // 收入
        } else {
          amount = Math.abs(amount) // 支出
        }
      }
    }

    // 构建描述
    let description = typeField || '信用卡交易'
    if (merchantField) {
      description += ` - ${merchantField}`
    }
    if (descField && descField !== merchantField) {
      description += ` - ${descField}`
    }

    // 推断分类
    let category = '其他'
    if (typeField) {
      if (typeField.includes('还款')) category = '信用卡还款'
      else if (typeField.includes('取现')) category = '信用卡取现'
      else if (typeField.includes('消费') || typeField.includes('刷卡')) category = '日常消费'
      else if (typeField.includes('分期')) category = '分期付款'
      else if (typeField.includes('年费')) category = '银行费用'
      else if (typeField.includes('利息')) category = '利息费用'
      else if (typeField.includes('退款')) category = '退款'
      else if (typeField.includes('转账')) category = '转账'
    }

    // 根据商户名称进一步细化分类
    if (merchantField && category === '日常消费') {
      if (merchantField.includes('超市') || merchantField.includes('便利店')) {
        category = '超市购物'
      } else if (merchantField.includes('餐厅') || merchantField.includes('饭店')) {
        category = '餐饮美食'
      } else if (merchantField.includes('加油站')) {
        category = '交通出行'
      } else if (merchantField.includes('医院') || merchantField.includes('药店')) {
        category = '医疗健康'
      }
    }

    return {
      externalId: referenceField,
      date: date.toISOString(),
      amount,
      description: description.trim(),
      category,
      location: merchantField,
      notes: `币种: ${currencyField || 'CNY'}, 状态: ${statusField || '未知'}`,
      rawData: fieldMap
    }
  }

  private findField(fieldMap: Record<string, string>, possibleNames: string[]): string | undefined {
    for (const name of possibleNames) {
      for (const key of Object.keys(fieldMap)) {
        if (key.includes(name)) {
          return fieldMap[key]
        }
      }
    }
    return undefined
  }
}
