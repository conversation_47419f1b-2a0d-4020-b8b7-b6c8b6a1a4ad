// 账单解析器类型定义

import { CardType, TransactionType } from '@prisma/client'

// 原始交易数据
export interface RawTransaction {
  externalId?: string          // 外部交易ID
  date: string                 // 交易日期
  amount: number               // 金额（原始值，可能为正负）
  description: string          // 交易描述
  transactionType?: string     // 交易类型（如：商户消费、微信红包、账户利息等）
  counterparty?: string        // 交易对方（如：商家名称、个人姓名等）
  category?: string            // 分类
  subcategory?: string         // 子分类
  location?: string            // 交易地点
  balance?: number             // 余额
  notes?: string               // 备注
  rawData?: Record<string, unknown> // 原始数据
}

// 标准化交易数据
export interface StandardTransaction {
  externalId?: string
  type: TransactionType        // 标准化后的交易类型
  amount: number               // 标准化后的金额（绝对值）
  description: string
  transactionType?: string     // 交易类型（如：商户消费、微信红包、账户利息等）
  counterParty?: string        // 交易对方（如：商家名称、个人姓名等）
  category?: string
  subcategory?: string
  transactionDate: Date
  location?: string
  notes?: string
  balance?: number
}

// 解析结果
export interface ParseResult {
  success: boolean
  transactions: StandardTransaction[]
  errors: string[]
  summary: {
    totalCount: number
    successCount: number
    errorCount: number
    duplicateCount: number
  }
}

// 解析器接口
export interface BillParser {
  // 解析器名称
  name: string
  
  // 支持的卡片类型
  supportedCardTypes: CardType[]
  
  // 支持的文件类型
  supportedFileTypes: string[]
  
  // 检查是否支持该文件
  canParse(fileName: string, cardType: CardType): boolean
  
  // 解析账单文件
  parse(fileContent: string | Buffer, cardType: CardType): Promise<ParseResult>
}

// 卡片类型配置
export interface CardTypeConfig {
  // 金额方向映射
  // true: 正数表示收入，负数表示支出
  // false: 正数表示支出，负数表示收入（如信用卡）
  positiveIsIncome: boolean
  
  // 默认分类映射
  defaultCategories: Record<string, string>
}

// 卡片类型配置映射
export const CARD_TYPE_CONFIGS: Record<CardType, CardTypeConfig> = {
  [CardType.DEBIT_CARD]: {
    positiveIsIncome: true,  // 借记卡：正数=收入，负数=支出
    defaultCategories: {
      '转账': '转账',
      '取现': '取现',
      '存款': '存款',
      '利息': '利息收入',
    }
  },
  [CardType.CREDIT_CARD]: {
    positiveIsIncome: false, // 信用卡：正数=支出，负数=收入（还款）
    defaultCategories: {
      '还款': '信用卡还款',
      '消费': '日常消费',
      '取现': '信用卡取现',
    }
  },
  [CardType.WECHAT]: {
    positiveIsIncome: true,  // 微信：正数=收入，负数=支出
    defaultCategories: {
      '转账': '转账',
      '红包': '红包',
      '消费': '日常消费',
      '充值': '充值',
    }
  },
  [CardType.ALIPAY]: {
    positiveIsIncome: true,  // 支付宝：正数=收入，负数=支出
    defaultCategories: {
      '转账': '转账',
      '红包': '红包',
      '消费': '日常消费',
      '充值': '充值',
    }
  },
  [CardType.CASH]: {
    positiveIsIncome: true,  // 现金：正数=收入，负数=支出
    defaultCategories: {}
  },
  [CardType.OTHER]: {
    positiveIsIncome: true,  // 其他：正数=收入，负数=支出
    defaultCategories: {}
  }
}
