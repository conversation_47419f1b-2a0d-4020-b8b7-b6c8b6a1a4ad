// 基础解析器类

import { CardType, TransactionType } from '@prisma/client'
import { 
  BillParser, 
  RawTransaction, 
  StandardTransaction, 
  ParseResult,
  CARD_TYPE_CONFIGS,
  CardTypeConfig 
} from './types'

export abstract class BaseBillParser implements BillParser {
  abstract name: string
  abstract supportedCardTypes: CardType[]
  abstract supportedFileTypes: string[]

  /**
   * 检查是否支持该文件
   */
  canParse(fileName: string, cardType: CardType): boolean {
    // 检查卡片类型
    if (!this.supportedCardTypes.includes(cardType)) {
      return false
    }

    // 检查文件扩展名
    const ext = fileName.toLowerCase().split('.').pop()
    return ext ? this.supportedFileTypes.includes(ext) : false
  }

  /**
   * 解析账单文件
   */
  abstract parse(fileContent: string | Buffer, cardType: CardType): Promise<ParseResult>

  /**
   * 标准化交易数据
   */
  protected standardizeTransaction(
    raw: RawTransaction, 
    cardType: CardType
  ): StandardTransaction {
    const config = CARD_TYPE_CONFIGS[cardType]
    
    // 确定交易类型和金额
    let type: TransactionType
    let amount: number

    if (raw.amount === 0) {
      type = TransactionType.TRANSFER
      amount = 0
    } else if (config.positiveIsIncome) {
      // 借记卡、微信、支付宝等：正数=收入，负数=支出
      if (raw.amount > 0) {
        type = TransactionType.INCOME
        amount = raw.amount
      } else {
        type = TransactionType.EXPENSE
        amount = Math.abs(raw.amount)
      }
    } else {
      // 信用卡：正数=支出，负数=收入
      if (raw.amount > 0) {
        type = TransactionType.EXPENSE
        amount = raw.amount
      } else {
        type = TransactionType.INCOME
        amount = Math.abs(raw.amount)
      }
    }

    return {
      externalId: raw.externalId,
      type,
      amount,
      description: raw.description.trim(),
      transactionType: raw.transactionType,
      counterParty: raw.counterparty,
      category: raw.category || this.inferCategory(raw.description, config),
      subcategory: raw.subcategory,
      transactionDate: new Date(raw.date),
      location: raw.location,
      notes: raw.notes,
      balance: raw.balance,
    }
  }

  /**
   * 推断交易分类
   */
  protected inferCategory(description: string, config: CardTypeConfig): string {
    for (const [keyword, category] of Object.entries(config.defaultCategories)) {
      if (description.includes(keyword)) {
        return category as string
      }
    }
    return '其他'
  }

  /**
   * 解析CSV内容
   */
  protected parseCSV(content: string): string[][] {
    const lines = content.split('\n').filter(line => line.trim())
    return lines.map(line => {
      // 简单的CSV解析，处理逗号分隔和引号包围的字段
      const fields: string[] = []
      let current = ''
      let inQuotes = false
      
      for (let i = 0; i < line.length; i++) {
        const char = line[i]
        
        if (char === '"') {
          inQuotes = !inQuotes
        } else if (char === ',' && !inQuotes) {
          fields.push(current.trim())
          current = ''
        } else {
          current += char
        }
      }
      
      fields.push(current.trim())
      return fields.map(field => field.replace(/^"|"$/g, '')) // 移除引号
    })
  }

  /**
   * 验证日期格式
   */
  protected parseDate(dateStr: string): Date | null {
    // 支持多种日期格式
    const formats = [
      /^\d{4}-\d{2}-\d{2}$/,           // 2024-01-01
      /^\d{4}\/\d{2}\/\d{2}$/,         // 2024/01/01
      /^\d{4}\.\d{2}\.\d{2}$/,         // 2024.01.01
      /^\d{2}-\d{2}-\d{4}$/,           // 01-01-2024
      /^\d{2}\/\d{2}\/\d{4}$/,         // 01/01/2024
    ]

    for (const format of formats) {
      if (format.test(dateStr)) {
        const date = new Date(dateStr)
        if (!isNaN(date.getTime())) {
          return date
        }
      }
    }

    return null
  }

  /**
   * 解析金额
   */
  protected parseAmount(amountStr: string): number {
    // 移除货币符号和空格
    const cleaned = amountStr.replace(/[￥$¥,\s]/g, '')
    const amount = parseFloat(cleaned)
    return isNaN(amount) ? 0 : amount
  }

  /**
   * 生成解析结果
   */
  protected createParseResult(
    transactions: StandardTransaction[],
    errors: string[]
  ): ParseResult {
    return {
      success: errors.length === 0,
      transactions,
      errors,
      summary: {
        totalCount: transactions.length + errors.length,
        successCount: transactions.length,
        errorCount: errors.length,
        duplicateCount: 0, // 将在服务层处理重复检测
      }
    }
  }
}
