// 招商银行借记卡账单解析器

import { CardType } from '@prisma/client'
import { BaseBillParser } from './base'
import { RawTransaction, ParseResult } from './types'

export class CmbDebitParser extends BaseBillParser {
  name = '招商银行借记卡账单解析器'
  supportedCardTypes = [CardType.DEBIT_CARD]
  supportedFileTypes = ['csv', 'xls', 'xlsx']

  async parse(fileContent: string | Buffer, cardType: CardType): Promise<ParseResult> {
    try {
      const content = fileContent.toString('utf-8')
      const lines = this.parseCSV(content)
      
      // 查找表头行
      let headerIndex = -1
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].some(cell => 
          cell.includes('交易日期') || 
          cell.includes('记账日期') ||
          cell.includes('交易时间')
        )) {
          headerIndex = i
          break
        }
      }

      if (headerIndex === -1) {
        return this.createParseResult([], ['未找到有效的表头'])
      }

      const headers = lines[headerIndex]
      const dataLines = lines.slice(headerIndex + 1)

      // 解析每一行数据
      const transactions: RawTransaction[] = []
      const errors: string[] = []

      for (let i = 0; i < dataLines.length; i++) {
        try {
          const row = dataLines[i]
          if (row.length < headers.length || row.every(cell => !cell.trim())) {
            continue // 跳过空行或不完整行
          }

          const transaction = this.parseCmbDebitRow(row, headers)
          if (transaction) {
            transactions.push(transaction)
          }
        } catch (error) {
          errors.push(`第${headerIndex + i + 2}行解析失败: ${error}`)
        }
      }

      // 标准化交易数据
      const standardTransactions = transactions.map(raw => 
        this.standardizeTransaction(raw, cardType)
      )

      return this.createParseResult(standardTransactions, errors)
    } catch (error) {
      return this.createParseResult([], [`文件解析失败: ${error}`])
    }
  }

  private parseCmbDebitRow(row: string[], headers: string[]): RawTransaction | null {
    // 创建字段映射
    const fieldMap: Record<string, string> = {}
    headers.forEach((header, index) => {
      if (row[index]) {
        fieldMap[header.trim()] = row[index].trim()
      }
    })

    // 招商银行借记卡账单常见字段映射
    const dateField = this.findField(fieldMap, ['交易日期', '记账日期', '交易时间'])
    const typeField = this.findField(fieldMap, ['交易类型', '摘要', '业务类型'])
    const descField = this.findField(fieldMap, ['交易说明', '备注', '摘要说明'])
    const amountField = this.findField(fieldMap, ['交易金额', '金额', '发生额'])
    const balanceField = this.findField(fieldMap, ['账户余额', '余额', '可用余额'])
    const counterpartyField = this.findField(fieldMap, ['对方户名', '对方账户', '交易对方'])
    const channelField = this.findField(fieldMap, ['交易渠道', '渠道'])
    const referenceField = this.findField(fieldMap, ['交易流水号', '流水号', '凭证号'])

    if (!dateField || !amountField) {
      return null
    }

    // 解析时间
    const date = this.parseDate(dateField)
    if (!date) {
      throw new Error(`无效的时间格式: ${dateField}`)
    }

    // 解析金额
    let amount = this.parseAmount(amountField)
    
    // 借记卡：正数表示收入（存款），负数表示支出（取款/消费）
    // 招商银行通常用+/-符号或者单独的借贷标识
    if (amountField.includes('-') || amountField.includes('支出')) {
      amount = -Math.abs(amount)
    } else if (amountField.includes('+') || amountField.includes('收入')) {
      amount = Math.abs(amount)
    } else {
      // 根据交易类型判断
      if (typeField) {
        if (typeField.includes('支出') || 
            typeField.includes('消费') || 
            typeField.includes('取现') ||
            typeField.includes('转出') ||
            typeField.includes('扣费')) {
          amount = -Math.abs(amount)
        } else if (typeField.includes('存入') || 
                   typeField.includes('转入') ||
                   typeField.includes('利息') ||
                   typeField.includes('退款')) {
          amount = Math.abs(amount)
        }
      }
    }

    // 构建描述
    let description = typeField || '银行交易'
    if (descField) {
      description += ` - ${descField}`
    }
    if (counterpartyField) {
      description += ` - ${counterpartyField}`
    }

    // 推断分类
    let category = '其他'
    if (typeField) {
      if (typeField.includes('转账')) category = '转账'
      else if (typeField.includes('取现') || typeField.includes('ATM')) category = '取现'
      else if (typeField.includes('存款') || typeField.includes('存入')) category = '存款'
      else if (typeField.includes('利息')) category = '利息收入'
      else if (typeField.includes('工资') || typeField.includes('代发')) category = '工资收入'
      else if (typeField.includes('消费') || typeField.includes('刷卡')) category = '日常消费'
      else if (typeField.includes('还款')) category = '还款'
      else if (typeField.includes('手续费') || typeField.includes('服务费')) category = '银行费用'
    }

    // 解析余额
    let balance: number | undefined
    if (balanceField) {
      balance = this.parseAmount(balanceField)
    }

    return {
      externalId: referenceField,
      date: date.toISOString(),
      amount,
      description: description.trim(),
      category,
      location: counterpartyField,
      balance,
      notes: `交易渠道: ${channelField || '未知'}`,
      rawData: fieldMap
    }
  }

  private findField(fieldMap: Record<string, string>, possibleNames: string[]): string | undefined {
    for (const name of possibleNames) {
      for (const key of Object.keys(fieldMap)) {
        if (key.includes(name)) {
          return fieldMap[key]
        }
      }
    }
    return undefined
  }
}
