// 账单解析器工厂

import { CardType } from '@prisma/client'
import { BillParser } from './types'
import { WechatParser } from './wechat'
import { AlipayParser } from './alipay'
import { CmbDebitParser } from './cmb-debit'
import { CmbCreditParser } from './cmb-credit'

// 注册所有解析器
const PARSERS: BillParser[] = [
  new WechatParser(),
  new AlipayParser(),
  new CmbDebitParser(),
  new CmbCreditParser(),
]

export class ParserFactory {
  /**
   * 获取支持指定卡片类型和文件的解析器
   */
  static getParser(fileName: string, cardType: CardType): BillParser | null {
    for (const parser of PARSERS) {
      if (parser.canParse(fileName, cardType)) {
        return parser
      }
    }
    return null
  }

  /**
   * 获取支持指定卡片类型的所有解析器
   */
  static getParsersForCardType(cardType: CardType): BillParser[] {
    return PARSERS.filter(parser => 
      parser.supportedCardTypes.includes(cardType)
    )
  }

  /**
   * 获取所有注册的解析器
   */
  static getAllParsers(): BillParser[] {
    return [...PARSERS]
  }

  /**
   * 获取支持的文件类型
   */
  static getSupportedFileTypes(): string[] {
    const types = new Set<string>()
    PARSERS.forEach(parser => {
      parser.supportedFileTypes.forEach(type => types.add(type))
    })
    return Array.from(types)
  }
}
