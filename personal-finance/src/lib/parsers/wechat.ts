// 微信账单解析器

import { CardType } from '@prisma/client'
import { BaseBillParser } from './base'
import { RawTransaction, ParseResult } from './types'
import { WeChatBill, WeChatTransaction } from '../types/bill'

export class WechatParser extends BaseBillParser {
  name = '微信支付账单解析器'
  supportedCardTypes = [CardType.WECHAT]
  supportedFileTypes = ['csv']

  async parse(fileContent: string | Buffer, cardType: CardType): Promise<ParseResult> {
    try {
      const content = fileContent.toString('utf-8')
      const wechatBill = this.parseWeChatBill(content)

      // 转换微信交易为标准交易格式
      const transactions: RawTransaction[] = []
      const errors: string[] = []

      wechatBill.transactions.forEach((wechatTx, index) => {
        try {
          const transaction = this.convertWeChatTransaction(wechatTx)
          if (transaction) {
            transactions.push(transaction)
          }
        } catch (error) {
          errors.push(`第${index + 1}行解析失败: ${error instanceof Error ? error.message : '未知错误'}`)
        }
      })

      // 标准化交易数据
      const standardTransactions = transactions.map(raw =>
        this.standardizeTransaction(raw, cardType)
      )

      return this.createParseResult(standardTransactions, errors)
    } catch (error) {
      return this.createParseResult([], [`文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`])
    }
  }

  private parseWeChatBill(csvContent: string): WeChatBill {
    const lines = csvContent.split('\n').map(line => line.trim())

    // Parse metadata
    const metadata = {
      nickname: this.extractValue(lines, '微信昵称：'),
      startDate: this.extractValue(lines, '起始时间：'),
      endDate: this.extractValue(lines, '终止时间：'),
      exportType: this.extractValue(lines, '导出类型：'),
      exportTime: this.extractValue(lines, '导出时间：'),
      summary: {
        totalCount: this.extractNumber(lines, '共'),
        incomeCount: this.extractNumber(lines, '收入：'),
        incomeAmount: this.extractAmount(lines, '收入：'),
        expenseCount: this.extractNumber(lines, '支出：'),
        expenseAmount: this.extractAmount(lines, '支出：'),
        neutralCount: this.extractNumber(lines, '中性交易：'),
        neutralAmount: this.extractAmount(lines, '中性交易：'),
      }
    }

    // Find transactions start index
    const transactionStartIndex = lines.findIndex(line =>
      line.startsWith('交易时间,交易类型,交易对方'))

    if (transactionStartIndex === -1) {
      throw new Error('未找到交易数据表头')
    }

    // Parse transactions
    const transactions: WeChatTransaction[] = lines
      .slice(transactionStartIndex + 1)
      .filter(line => line && !line.startsWith('-') && !line.startsWith('注：') && !line.startsWith('共'))
      .map(line => this.parseWeChatTransactionLine(line))
      .filter(tx => tx !== null) as WeChatTransaction[]

    return { metadata, transactions }
  }

  private parseWeChatTransactionLine(line: string): WeChatTransaction | null {
    // 更精确的CSV解析，处理引号内的逗号
    const fields: string[] = []
    let field = ''
    let inQuotes = false

    for (let i = 0; i < line.length; i++) {
      const char = line[i]

      if (char === '"') {
        inQuotes = !inQuotes
        continue
      }

      if (char === ',' && !inQuotes) {
        fields.push(field.trim())
        field = ''
        continue
      }

      field += char
    }
    fields.push(field.trim()) // 添加最后一个字段

    if (fields.length < 11) {
      //log line
      console.log('line', line)
      return null // 字段不足，跳过
    }

    const [
      timestamp, type, counterparty, goods, direction,
      amount, paymentMethod, status, transactionId,
      merchantOrderId, memo
    ] = fields

    return {
      timestamp,
      type,
      counterparty,
      goods,
      direction: this.parseDirection(direction),
      amount: this.parseAmount(amount),
      paymentMethod,
      status,
      transactionId,
      merchantOrderId,
      memo
    }
  }

  private convertWeChatTransaction(wechatTx: WeChatTransaction): RawTransaction | null {
    // 只处理成功的交易
    if (wechatTx.status !== '支付成功' && wechatTx.status !== '已收钱' && wechatTx.status !== '交易成功') {
      return null
    }

    // 解析日期
    const date = this.parseWeChatDate(wechatTx.timestamp)
    if (!date) {
      throw new Error(`无效的时间格式: ${wechatTx.timestamp}`)
    }

    // 根据方向确定金额符号
    let amount = Math.abs(wechatTx.amount)
    if (wechatTx.direction === 'expense') {
      amount = -amount
    }

    // 构建描述
    let description = wechatTx.type
    if (wechatTx.goods) {
      description += ` - ${wechatTx.goods}`
    }
    if (wechatTx.counterparty) {
      description += ` - ${wechatTx.counterparty}`
    }

    // 推断分类
    let category = '其他'
    if (wechatTx.type.includes('转账')) category = '转账'
    else if (wechatTx.type.includes('红包')) category = '红包'
    else if (wechatTx.type.includes('充值')) category = '充值'
    else if (wechatTx.type.includes('提现')) category = '提现'
    else if (wechatTx.type.includes('还款')) category = '还款'
    else if (wechatTx.type.includes('消费') || wechatTx.type.includes('支付')) category = '日常消费'

    return {
      externalId: wechatTx.transactionId,
      date: date.toISOString(),
      amount,
      description: description.trim(),
      category,
      location: wechatTx.counterparty,
      notes: `支付方式: ${wechatTx.paymentMethod}${wechatTx.memo ? ` | 备注: ${wechatTx.memo}` : ''}`,
      rawData: wechatTx
    }
  }

  private extractValue(lines: string[], prefix: string): string {
    const line = lines.find(line => line.startsWith(prefix))
    return line ? line.split('：')[1]?.replace(/[\[\]]/g, '') || '' : ''
  }

  private extractNumber(lines: string[], prefix: string): number {
    const line = lines.find(line => line.startsWith(prefix))
    return line ? parseInt(line.match(/\d+/)?.[0] || '0') : 0
  }

  private extractAmount(lines: string[], prefix: string): number {
    const line = lines.find(line => line.startsWith(prefix))
    return line ? this.parseAmount(line.match(/\d+\.\d+/)?.[0] || '0') : 0
  }

  private parseDirection(direction: string): 'income' | 'expense' | 'neutral' {
    if (direction.includes('收入')) return 'income'
    if (direction.includes('支出')) return 'expense'
    return 'neutral'
  }

  private parseWeChatDate(dateStr: string): Date | null {
    // 微信时间格式: 2024-01-15 14:30:00
    const wechatFormat = /(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})/
    const match = dateStr.match(wechatFormat)

    if (match) {
      const [, year, month, day, hour, minute, second] = match
      return new Date(
        parseInt(year),
        parseInt(month) - 1, // 月份从0开始
        parseInt(day),
        parseInt(hour),
        parseInt(minute),
        parseInt(second)
      )
    }

    // 如果不匹配微信格式，尝试其他格式
    const date = this.parseDate(dateStr)
    return date || null
  }


}
