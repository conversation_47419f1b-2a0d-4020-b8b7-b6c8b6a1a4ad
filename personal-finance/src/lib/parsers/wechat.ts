// 微信账单解析器

import { CardType } from '@prisma/client'
import { BaseBillParser } from './base'
import { RawTransaction, ParseResult } from './types'

export class WechatParser extends BaseBillParser {
  name = '微信支付账单解析器'
  supportedCardTypes = [CardType.WECHAT]
  supportedFileTypes = ['csv']

  async parse(fileContent: string | Buffer, cardType: CardType): Promise<ParseResult> {
    try {
      const content = fileContent.toString('utf-8')
      const lines = this.parseCSV(content)
      
      // 查找表头行
      let headerIndex = -1
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].some(cell => cell.includes('交易时间'))) {
          headerIndex = i
          break
        }
      }

      if (headerIndex === -1) {
        return this.createParseResult([], ['未找到有效的表头'])
      }

      const headers = lines[headerIndex]
      const dataLines = lines.slice(headerIndex + 1)

      // 解析每一行数据
      const transactions: RawTransaction[] = []
      const errors: string[] = []

      for (let i = 0; i < dataLines.length; i++) {
        try {
          const row = dataLines[i]
          if (row.length < headers.length || row.every(cell => !cell.trim())) {
            continue // 跳过空行或不完整行
          }

          const transaction = this.parseWechatRow(row, headers)
          if (transaction) {
            transactions.push(transaction)
          }
        } catch (error) {
          errors.push(`第${headerIndex + i + 2}行解析失败: ${error}`)
        }
      }

      // 标准化交易数据
      const standardTransactions = transactions.map(raw => 
        this.standardizeTransaction(raw, cardType)
      )

      return this.createParseResult(standardTransactions, errors)
    } catch (error) {
      return this.createParseResult([], [`文件解析失败: ${error}`])
    }
  }

  private parseWechatRow(row: string[], headers: string[]): RawTransaction | null {
    // 创建字段映射
    const fieldMap: Record<string, string> = {}
    headers.forEach((header, index) => {
      if (row[index]) {
        fieldMap[header.trim()] = row[index].trim()
      }
    })

    // 微信账单常见字段映射
    const timeField = this.findField(fieldMap, ['交易时间', '时间'])
    const typeField = this.findField(fieldMap, ['交易类型', '类型'])
    const counterpartyField = this.findField(fieldMap, ['交易对方', '对方'])
    const productField = this.findField(fieldMap, ['商品', '商品说明', '备注'])
    const statusField = this.findField(fieldMap, ['当前状态', '状态'])
    const amountField = this.findField(fieldMap, ['金额(元)', '金额', '收支'])
    const paymentField = this.findField(fieldMap, ['支付方式', '付款方式'])
    const orderField = this.findField(fieldMap, ['交易单号', '单号'])

    if (!timeField || !amountField) {
      return null
    }

    // 解析时间
    const date = this.parseDate(timeField)
    if (!date) {
      throw new Error(`无效的时间格式: ${timeField}`)
    }

    // 解析金额
    let amount = this.parseAmount(amountField)
    
    // 微信账单中，收入通常标记为正数，支出为负数
    // 但有些格式可能不同，需要根据交易类型判断
    if (typeField && typeField.includes('支出')) {
      amount = -Math.abs(amount)
    } else if (typeField && typeField.includes('收入')) {
      amount = Math.abs(amount)
    }

    // 构建描述
    let description = typeField || '微信交易'
    if (counterpartyField) {
      description += ` - ${counterpartyField}`
    }
    if (productField) {
      description += ` - ${productField}`
    }

    // 推断分类
    let category = '其他'
    if (typeField) {
      if (typeField.includes('转账')) category = '转账'
      else if (typeField.includes('红包')) category = '红包'
      else if (typeField.includes('充值')) category = '充值'
      else if (typeField.includes('提现')) category = '提现'
      else if (typeField.includes('还款')) category = '还款'
      else if (typeField.includes('消费') || typeField.includes('支付')) category = '日常消费'
    }

    return {
      externalId: orderField,
      date: date.toISOString(),
      amount,
      description: description.trim(),
      category,
      location: counterpartyField,
      notes: `支付方式: ${paymentField || '未知'}, 状态: ${statusField || '未知'}`,
      rawData: fieldMap
    }
  }

  private findField(fieldMap: Record<string, string>, possibleNames: string[]): string | undefined {
    for (const name of possibleNames) {
      for (const key of Object.keys(fieldMap)) {
        if (key.includes(name)) {
          return fieldMap[key]
        }
      }
    }
    return undefined
  }
}
