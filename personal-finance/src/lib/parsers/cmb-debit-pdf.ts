// 招商银行借记卡PDF账单解析器

import { CardType } from '@prisma/client'
import { BaseBillParser } from './base'
import { RawTransaction, ParseResult } from './types'
import crypto from 'crypto'

interface TextItem {
  str: string
  dir?: string
  width?: number
  height?: number
  transform?: number[]
  fontName?: string
}

interface PageContent {
  pageNumber: number
  textContent: {
    items: TextItem[]
  }
}

interface PDFResult {
  numpages: number
  info: Record<string, unknown>
  metadata: Record<string, unknown>
  version: string
  pages: PageContent[]
}

interface BillRecord {
  date: string
  currency: string
  amount: string
  balance: string
  summary: string
  counterparty: string
  sequence?: number
  hash?: string
}

export class CmbDebitPdfParser extends BaseBillParser {
  name = '招商银行借记卡PDF账单解析器'
  supportedCardTypes = [CardType.DEBIT_CARD]
  supportedFileTypes = ['pdf']

  // 定义分页相关的常量
  private readonly PAGINATION_MARKERS = ['/', '第', '页', '共', '温馨提示', '一网通', 'www.cmbchina.com']
  private readonly HEADER_MARKERS = ['记账日期', '货币', '交易金额', '联机余额', '交易摘要', '对手信息']
  private readonly ENGLISH_HEADERS = ['Date', 'Currency', 'Transaction', 'Amount', 'Balance', 'Counter Party']

  async parse(fileContent: string | Buffer, cardType: CardType): Promise<ParseResult> {
    try {
      const buffer = Buffer.isBuffer(fileContent) ? fileContent : Buffer.from(fileContent)

      // 检查是否是真实的PDF文件
      if (buffer.length < 4 || buffer.toString('ascii', 0, 4) !== '%PDF') {
        // 如果不是PDF文件，返回错误
        return this.createParseResult([], ['文件不是有效的PDF格式'])
      }

      // 使用require导入pdf-parse（已在next.config.ts中配置为外部包）
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const pdfParse = require('pdf-parse')

      // 解析PDF到JSON结构
      const pdfResult = await this.extractJsonFromPdf(buffer, pdfParse)

      // 从JSON结构中解析交易记录
      const transactions = await this.parseTransactions(pdfResult)

      // 转换为标准交易格式，并添加行号到hash计算中
      const rawTransactions: RawTransaction[] = transactions.map((transaction, index) =>
        this.convertToRawTransaction(transaction, index + 1)
      )

      // 标准化交易数据
      const standardTransactions = rawTransactions.map(raw =>
        this.standardizeTransaction(raw, cardType)
      )

      return this.createParseResult(standardTransactions, [])
    } catch (error) {
      return this.createParseResult([], [`PDF解析失败: ${error instanceof Error ? error.message : '未知错误'}`])
    }
  }



  private async extractJsonFromPdf(dataBuffer: Buffer, pdfParse: unknown): Promise<PDFResult> {
    let currentPage = 1
    const pages: PageContent[] = []
    let currentPageContent: PageContent = {
      pageNumber: currentPage,
      textContent: {
        items: []
      }
    }

    const options = {
      pagerender: function(pageData: unknown) {
        return (pageData as { getTextContent: () => Promise<{ items: unknown[] }> }).getTextContent().then(function(textContent: { items: unknown[] }) {
          if (textContent.items.length > 0) {
            currentPageContent = {
              pageNumber: currentPage++,
              textContent: {
                items: textContent.items.map((item: unknown) => {
                  const pdfItem = item as {
                    str?: string
                    dir?: string
                    width?: number
                    height?: number
                    transform?: number[]
                    fontName?: string
                  }
                  return {
                    str: pdfItem.str || '',
                    dir: pdfItem.dir || 'ltr',
                    width: pdfItem.width || 0,
                    height: pdfItem.height || 0,
                    transform: pdfItem.transform || [1, 0, 0, 1, 0, 0],
                    fontName: pdfItem.fontName || ''
                  }
                })
              }
            }
            pages.push(currentPageContent)
          }
          return textContent.items
        })
      }
    }
    
    const pdfParseFunction = pdfParse as (buffer: Buffer, options?: unknown) => Promise<{
      numpages: number
      info: unknown
      metadata: unknown
      version: string
    }>

    const data = await pdfParseFunction(dataBuffer, options)

    return {
      numpages: data.numpages,
      info: data.info,
      metadata: data.metadata,
      version: data.version,
      pages: pages
    }
  }

  private async parseTransactions(pdfResult: PDFResult): Promise<BillRecord[]> {
    const transactions: BillRecord[] = []
    let currentTransaction: BillRecord | null = null
    let sequence = 1

    for (const page of pdfResult.pages) {
      if (!page.textContent) continue

      const items = page.textContent.items || []
      let supassPagination = false
      
      for (const item of items) {
        const text = item.str?.trim() || ''
        
        // 检查是否是日期格式
        if (text.length === 10 && text.split('-').length === 3) {
          supassPagination = true
          try {
            if (this.isValidDate(text)) {
              // 处理前一个交易记录
              if (currentTransaction && this.isValidRow(currentTransaction)) {
                currentTransaction.sequence = sequence++
                currentTransaction.hash = this.computeTransactionHash(currentTransaction)
                transactions.push(currentTransaction)
              }
              
              // 创建新的交易记录
              currentTransaction = {
                date: text,
                currency: '',
                amount: '',
                balance: '',
                summary: '',
                counterparty: '',
                sequence: 0,
                hash: ''
              }
            }
          } catch {
            continue
          }
        }
        else if (!supassPagination) {
          continue
        }
        // 处理交易记录的其他字段
        else if (currentTransaction) {
          if (text === 'CNY' && !currentTransaction.currency) {
            currentTransaction.currency = text
          }
          else if (this.hasNumbers(text) && !currentTransaction.summary) {
            if (!currentTransaction.amount) {
              currentTransaction.amount = text
            } else if (!currentTransaction.balance) {
              currentTransaction.balance = text
            }
          }
          else if (!currentTransaction.summary) {
            currentTransaction.summary = text
          }
          else if (currentTransaction.summary) {
            currentTransaction.counterparty = currentTransaction.counterparty ? 
              currentTransaction.counterparty + '-' + text : text
          }
        }
      }
    }

    // 处理最后一个交易记录
    if (currentTransaction && this.isValidRow(currentTransaction)) {
      currentTransaction.sequence = sequence
      currentTransaction.hash = this.computeTransactionHash(currentTransaction)
      transactions.push(currentTransaction)
    }

    return transactions
  }

  private convertToRawTransaction(billRecord: BillRecord, lineNumber?: number): RawTransaction {
    // 解析金额，判断收入还是支出
    const amount = this.parseAmount(billRecord.amount)

    // 生成外部ID，包含行号以避免同一天多笔交易的重复识别
    const externalId = billRecord.hash || this.generateExternalId(billRecord, lineNumber)
    
    // 构建描述
    const description = `${billRecord.summary}${billRecord.counterparty ? ` - ${billRecord.counterparty}` : ''}`
    
    // 推断分类
    const category = this.inferCategory(billRecord.summary, billRecord.counterparty)
    
    return {
      externalId,
      date: billRecord.date,
      amount,
      balance: this.parseBalance(billRecord.balance),
      description: description.trim(),
      transactionType: billRecord.summary, // 交易摘要作为交易类型
      counterparty: billRecord.counterparty, // 对手信息作为交易对方
      category,
      location: billRecord.counterparty,
      notes: `货币: ${billRecord.currency} | 余额: ${billRecord.balance}`,
      rawData: billRecord as unknown as Record<string, unknown>
    }
  }

  private computeTransactionHash(transaction: BillRecord, lineNumber?: number): string {
    const content = [
      transaction.date,
      transaction.currency,
      transaction.amount,
      transaction.balance,
      transaction.summary,
      transaction.counterparty,
      // 如果提供了行号，将其包含在hash中以避免同一天多笔交易的重复识别
      lineNumber ? `line:${lineNumber}` : ''
    ].join('|')

    return crypto.createHash('sha256')
      .update(content)
      .digest('hex')
      .substring(0, 16) // 取前16位作为短哈希
  }

  private generateExternalId(billRecord: BillRecord, lineNumber?: number): string {
    return this.computeTransactionHash(billRecord, lineNumber)
  }

  private parseBalance(balanceStr: string): number | undefined {
    if (!balanceStr || balanceStr.trim() === '') {
      return undefined
    }
    // 移除所有非数字和小数点的字符
    const cleanBalance = balanceStr.replace(/[^\d.]/g, '')
    const balance = parseFloat(cleanBalance)
    return isNaN(balance) ? undefined : balance
  }

  private hasNumbers(text: string): boolean {
    return /\d/.test(text)
  }

  private isValidDate(dateStr: string): boolean {
    const date = new Date(dateStr)
    return date instanceof Date && !isNaN(date.getTime()) && dateStr.match(/^\d{4}-\d{2}-\d{2}$/) !== null
  }

  private isValidRow(row: BillRecord): boolean {
    return Boolean(row.date && row.currency && row.amount)
  }

  private inferCategory(summary: string, counterparty: string): string {
    const text = `${summary} ${counterparty}`.toLowerCase()
    
    if (text.includes('餐') || text.includes('食') || text.includes('饭') || text.includes('咖啡')) {
      return '餐饮'
    } else if (text.includes('交通') || text.includes('地铁') || text.includes('公交') || text.includes('打车') || text.includes('滴滴')) {
      return '交通'
    } else if (text.includes('购物') || text.includes('商城') || text.includes('超市') || text.includes('淘宝') || text.includes('京东')) {
      return '购物'
    } else if (text.includes('娱乐') || text.includes('电影') || text.includes('游戏')) {
      return '娱乐'
    } else if (text.includes('医') || text.includes('药') || text.includes('医院')) {
      return '医疗'
    } else if (text.includes('教育') || text.includes('学') || text.includes('培训')) {
      return '教育'
    } else if (text.includes('房') || text.includes('租') || text.includes('物业')) {
      return '住房'
    } else if (text.includes('转账') || text.includes('汇款')) {
      return '转账'
    } else if (text.includes('工资') || text.includes('薪') || text.includes('奖金')) {
      return '工资'
    } else if (text.includes('利息') || text.includes('理财') || text.includes('投资')) {
      return '投资理财'
    } else {
      return '其他'
    }
  }
}
