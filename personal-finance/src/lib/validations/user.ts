import { z } from 'zod'
import { UserRole } from '@prisma/client'

export const createUserSchema = z.object({
  name: z.string().min(1, '姓名不能为空').max(50, '姓名不能超过50个字符'),
  email: z.string().email('邮箱格式不正确').optional(),
  role: z.nativeEnum(UserRole).default(UserRole.MEMBER),
})

export const updateUserSchema = z.object({
  name: z.string().min(1, '姓名不能为空').max(50, '姓名不能超过50个字符').optional(),
  email: z.string().email('邮箱格式不正确').optional(),
  role: z.nativeEnum(UserRole).optional(),
  isActive: z.boolean().optional(),
})

export type CreateUserInput = z.infer<typeof createUserSchema>
export type UpdateUserInput = z.infer<typeof updateUserSchema>
