import { z } from 'zod'

// 用户注册
export const registerSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(8, '密码长度至少8位'),
  name: z.string().min(1, '姓名不能为空').max(50, '姓名不能超过50个字符'),
})

// 用户登录
export const loginSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(1, '密码不能为空'),
})

// 更新用户信息
export const updateUserSchema = z.object({
  name: z.string().min(1, '姓名不能为空').max(50, '姓名不能超过50个字符').optional(),
  avatar: z.string().url('头像URL格式不正确').optional(),
})

// 修改密码
export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, '当前密码不能为空'),
  newPassword: z.string().min(8, '新密码长度至少8位'),
  confirmPassword: z.string().min(1, '确认密码不能为空'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
})

// 创建家庭
export const createFamilySchema = z.object({
  name: z.string().min(1, '家庭名称不能为空').max(100, '家庭名称不能超过100个字符'),
  description: z.string().max(500, '家庭描述不能超过500个字符').optional(),
  avatar: z.string().url('头像URL格式不正确').optional(),
})

// 邀请家庭成员
export const inviteFamilyMemberSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  role: z.enum(['OWNER', 'ADMIN', 'MEMBER']).default('MEMBER'),
  message: z.string().max(500, '邀请消息不能超过500个字符').optional(),
})

// 响应邀请
export const respondInvitationSchema = z.object({
  action: z.enum(['accept', 'decline']),
})

export type RegisterInput = z.infer<typeof registerSchema>
export type LoginInput = z.infer<typeof loginSchema>
export type UpdateUserInput = z.infer<typeof updateUserSchema>
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>
export type CreateFamilyInput = z.infer<typeof createFamilySchema>
export type InviteFamilyMemberInput = z.infer<typeof inviteFamilyMemberSchema>
export type RespondInvitationInput = z.infer<typeof respondInvitationSchema>
