import { z } from 'zod'
import { CardType } from '@prisma/client'

export const createCardSchema = z.object({
  name: z.string().min(1, '卡片名称不能为空').max(100, '卡片名称不能超过100个字符'),
  type: z.nativeEnum(CardType),
  accountNo: z.string().max(50, '账号不能超过50个字符').optional(),
  bankName: z.string().max(100, '银行名称不能超过100个字符').optional(),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
  userId: z.string().cuid('用户ID格式不正确'),
})

export const updateCardSchema = z.object({
  name: z.string().min(1, '卡片名称不能为空').max(100, '卡片名称不能超过100个字符').optional(),
  type: z.nativeEnum(CardType).optional(),
  accountNo: z.string().max(50, '账号不能超过50个字符').optional(),
  bankName: z.string().max(100, '银行名称不能超过100个字符').optional(),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
  isActive: z.boolean().optional(),
})

export type CreateCardInput = z.infer<typeof createCardSchema>
export type UpdateCardInput = z.infer<typeof updateCardSchema>
