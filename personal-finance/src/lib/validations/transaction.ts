import { z } from 'zod'
import { TransactionType, TransactionStatus } from '@prisma/client'

export const createTransactionSchema = z.object({
  type: z.nativeEnum(TransactionType),
  amount: z.number().positive('金额必须大于0'),
  description: z.string().min(1, '交易描述不能为空').max(200, '交易描述不能超过200个字符'),
  category: z.string().max(50, '分类不能超过50个字符').optional(),
  subcategory: z.string().max(50, '子分类不能超过50个字符').optional(),
  transactionDate: z.string().datetime('交易时间格式不正确'),
  location: z.string().max(100, '交易地点不能超过100个字符').optional(),
  notes: z.string().max(500, '备注不能超过500个字符').optional(),
  userId: z.string().cuid('用户ID格式不正确'),
  cardId: z.string().cuid('卡片ID格式不正确').optional(),
  externalId: z.string().max(100, '外部ID不能超过100个字符').optional(),
})

export const updateTransactionSchema = z.object({
  type: z.nativeEnum(TransactionType).optional(),
  status: z.nativeEnum(TransactionStatus).optional(),
  amount: z.number().positive('金额必须大于0').optional(),
  description: z.string().min(1, '交易描述不能为空').max(200, '交易描述不能超过200个字符').optional(),
  category: z.string().max(50, '分类不能超过50个字符').optional(),
  subcategory: z.string().max(50, '子分类不能超过50个字符').optional(),
  transactionDate: z.string().datetime('交易时间格式不正确').optional(),
  location: z.string().max(100, '交易地点不能超过100个字符').optional(),
  notes: z.string().max(500, '备注不能超过500个字符').optional(),
  cardId: z.string().cuid('卡片ID格式不正确').optional(),
})

export const transactionQuerySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().min(1)).default('1'),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).default('20'),
  type: z.nativeEnum(TransactionType).optional(),
  status: z.nativeEnum(TransactionStatus).optional(),
  cardId: z.string().cuid().optional(),
  category: z.string().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  search: z.string().optional(),
})

export type CreateTransactionInput = z.infer<typeof createTransactionSchema>
export type UpdateTransactionInput = z.infer<typeof updateTransactionSchema>
export type TransactionQuery = z.infer<typeof transactionQuerySchema>
