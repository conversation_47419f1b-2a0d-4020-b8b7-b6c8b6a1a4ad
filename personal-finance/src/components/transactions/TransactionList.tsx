'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { 
  formatCurrency, 
  formatDate, 
  getTransactionType<PERSON>abel, 
  getCardTypeLabel,
  getTransactionStatusLabel,
  getStatusColor,
  getTypeColor 
} from '@/lib/utils'

interface Transaction {
  id: string
  type: string
  status: string
  amount: number
  description: string
  category?: string
  transactionDate: string
  location?: string
  notes?: string
  user: {
    id: string
    name: string
  }
  card?: {
    id: string
    name: string
    type: string
  }
  linkedTransaction?: {
    id: string
    description: string
    amount: number
  }
}

interface TransactionListProps {
  transactions: Transaction[]
  onEdit?: (transaction: Transaction) => void
  onDelete?: (transactionId: string) => void
  onLink?: (transactionId: string) => void
}

export function TransactionList({ transactions, onEdit, onDelete, onLink }: TransactionListProps) {
  const [selectedTransactions, setSelectedTransactions] = useState<string[]>([])

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedTransactions(transactions.map(t => t.id))
    } else {
      setSelectedTransactions([])
    }
  }

  const handleSelectTransaction = (transactionId: string, checked: boolean) => {
    if (checked) {
      setSelectedTransactions(prev => [...prev, transactionId])
    } else {
      setSelectedTransactions(prev => prev.filter(id => id !== transactionId))
    }
  }

  const isAllSelected = transactions.length > 0 && selectedTransactions.length === transactions.length
  const isPartiallySelected = selectedTransactions.length > 0 && selectedTransactions.length < transactions.length

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">交易记录</h3>
          <div className="flex space-x-2">
            {selectedTransactions.length > 0 && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // 批量关联
                    console.log('批量关联:', selectedTransactions)
                  }}
                >
                  批量关联
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // 批量操作
                    console.log('批量操作:', selectedTransactions)
                  }}
                >
                  批量操作 ({selectedTransactions.length})
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={input => {
                    if (input) input.indeterminate = isPartiallySelected
                  }}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                交易信息
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                类型
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                金额
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                卡片
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                时间
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {transactions.map((transaction) => (
              <tr key={transaction.id} className="hover:bg-gray-50">
                <td className="px-6 py-4">
                  <input
                    type="checkbox"
                    checked={selectedTransactions.includes(transaction.id)}
                    onChange={(e) => handleSelectTransaction(transaction.id, e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </td>
                <td className="px-6 py-4">
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {transaction.description}
                    </div>
                    {transaction.category && (
                      <div className="text-sm text-gray-500">
                        分类: {transaction.category}
                      </div>
                    )}
                    {transaction.location && (
                      <div className="text-sm text-gray-500">
                        地点: {transaction.location}
                      </div>
                    )}
                    {transaction.linkedTransaction && (
                      <div className="text-sm text-blue-600">
                        关联: {transaction.linkedTransaction.description}
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    transaction.type === 'INCOME' 
                      ? 'bg-green-100 text-green-800'
                      : transaction.type === 'EXPENSE'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-blue-100 text-blue-800'
                  }`}>
                    {getTransactionTypeLabel(transaction.type)}
                  </span>
                </td>
                <td className="px-6 py-4">
                  <span className={`text-sm font-medium ${getTypeColor(transaction.type)}`}>
                    {transaction.type === 'EXPENSE' ? '-' : '+'}
                    {formatCurrency(transaction.amount)}
                  </span>
                </td>
                <td className="px-6 py-4">
                  {transaction.card ? (
                    <div className="text-sm">
                      <div className="text-gray-900">{transaction.card.name}</div>
                      <div className="text-gray-500">{getCardTypeLabel(transaction.card.type)}</div>
                    </div>
                  ) : (
                    <span className="text-sm text-gray-500">未指定</span>
                  )}
                </td>
                <td className="px-6 py-4">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(transaction.status)}`}>
                    {getTransactionStatusLabel(transaction.status)}
                  </span>
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">
                  {formatDate(transaction.transactionDate, 'time')}
                </td>
                <td className="px-6 py-4 text-right text-sm font-medium space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit?.(transaction)}
                  >
                    编辑
                  </Button>
                  {transaction.status !== 'LINKED' && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onLink?.(transaction.id)}
                    >
                      关联
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDelete?.(transaction.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    删除
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {transactions.length === 0 && (
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">暂无交易记录</h3>
          <p className="mt-1 text-sm text-gray-500">开始添加您的第一笔交易或导入账单</p>
        </div>
      )}
    </div>
  )
}
