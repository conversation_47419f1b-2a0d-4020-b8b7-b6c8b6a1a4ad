'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { ClientOnly } from '@/components/ClientOnly'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireFamily?: boolean // 是否需要用户已加入家庭
  requireFamilyAdmin?: boolean // 是否需要家庭管理员权限
}

const LoadingSpinner = () => (
  <div className="min-h-screen bg-gray-50 flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      <p className="mt-4 text-gray-600">加载中...</p>
    </div>
  </div>
)

function ProtectedRouteContent({
  children,
  requireFamily = false,
  requireFamilyAdmin = false
}: ProtectedRouteProps) {
  const router = useRouter()
  const { user, loading, isAuthenticated } = useAuth()

  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated) {
        router.push('/auth/login')
        return
      }

      if (requireFamily && !user?.familyId) {
        router.push('/family')
        return
      }

      if (requireFamilyAdmin && user?.familyRole !== 'OWNER' && user?.familyRole !== 'ADMIN') {
        router.push('/dashboard')
        return
      }
    }
  }, [loading, isAuthenticated, user, router, requireFamily, requireFamilyAdmin])

  if (loading) {
    return <LoadingSpinner />
  }

  if (!isAuthenticated) {
    return null // 将重定向到登录页面
  }

  if (requireFamily && !user?.familyId) {
    return null // 将重定向到家庭页面
  }

  if (requireFamilyAdmin && user?.familyRole !== 'OWNER' && user?.familyRole !== 'ADMIN') {
    return null // 将重定向到仪表板
  }

  return <>{children}</>
}

export function ProtectedRoute(props: ProtectedRouteProps) {
  return (
    <ClientOnly fallback={<LoadingSpinner />}>
      <ProtectedRouteContent {...props} />
    </ClientOnly>
  )
}
