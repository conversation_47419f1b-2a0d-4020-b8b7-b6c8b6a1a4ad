'use client'

import { useState } from 'react'
import { ChevronDownIcon, CheckIcon } from '@heroicons/react/24/outline'

interface Bank {
  id: string
  name: string
  shortName: string
  logo?: string
}

// 主流银行列表
const BANKS: Bank[] = [
  { id: 'icbc', name: '中国工商银行', shortName: '工商银行' },
  { id: 'abc', name: '中国农业银行', shortName: '农业银行' },
  { id: 'boc', name: '中国银行', shortName: '中国银行' },
  { id: 'ccb', name: '中国建设银行', shortName: '建设银行' },
  { id: 'bcom', name: '交通银行', shortName: '交通银行' },
  { id: 'cmb', name: '招商银行', shortName: '招商银行' },
  { id: 'cib', name: '兴业银行', shortName: '兴业银行' },
  { id: 'spdb', name: '浦发银行', shortName: '浦发银行' },
  { id: 'cmbc', name: '中国民生银行', shortName: '民生银行' },
  { id: 'citic', name: '中信银行', shortName: '中信银行' },
  { id: 'hxb', name: '华夏银行', shortName: '华夏银行' },
  { id: 'gdb', name: '广发银行', shortName: '广发银行' },
  { id: 'ping_an', name: '平安银行', shortName: '平安银行' },
  { id: 'ceb', name: '光大银行', shortName: '光大银行' },
  { id: 'psbc', name: '中国邮政储蓄银行', shortName: '邮储银行' },
  { id: 'bob', name: '北京银行', shortName: '北京银行' },
  { id: 'njcb', name: '南京银行', shortName: '南京银行' },
  { id: 'nbcb', name: '宁波银行', shortName: '宁波银行' },
  { id: 'other', name: '其他银行', shortName: '其他' },
]

interface BankSelectProps {
  value?: string
  onChange: (bankName: string) => void
  placeholder?: string
  disabled?: boolean
}

export function BankSelect({ value, onChange, placeholder = '请选择银行', disabled = false }: BankSelectProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  const selectedBank = BANKS.find(bank => bank.name === value || bank.shortName === value)
  
  const filteredBanks = BANKS.filter(bank =>
    bank.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    bank.shortName.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleSelect = (bank: Bank) => {
    onChange(bank.name)
    setIsOpen(false)
    setSearchTerm('')
  }

  return (
    <div className="relative">
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          relative w-full cursor-default rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-left shadow-sm 
          focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 sm:text-sm
          ${disabled ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : 'hover:border-gray-400'}
        `}
      >
        <span className="block truncate">
          {selectedBank ? selectedBank.name : placeholder}
        </span>
        <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
          <ChevronDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
        </span>
      </button>

      {isOpen && !disabled && (
        <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
          {/* 搜索框 */}
          <div className="sticky top-0 z-10 bg-white px-3 py-2 border-b border-gray-200">
            <input
              type="text"
              className="w-full rounded-md border border-gray-300 px-3 py-1 text-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
              placeholder="搜索银行..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onClick={(e) => e.stopPropagation()}
            />
          </div>

          {/* 银行列表 */}
          {filteredBanks.length === 0 ? (
            <div className="px-3 py-2 text-gray-500 text-sm">
              未找到匹配的银行
            </div>
          ) : (
            filteredBanks.map((bank) => (
              <div
                key={bank.id}
                className={`
                  relative cursor-default select-none py-2 pl-3 pr-9 hover:bg-indigo-600 hover:text-white
                  ${selectedBank?.id === bank.id ? 'bg-indigo-600 text-white' : 'text-gray-900'}
                `}
                onClick={() => handleSelect(bank)}
              >
                <div className="flex items-center">
                  <span className={`block truncate ${selectedBank?.id === bank.id ? 'font-medium' : 'font-normal'}`}>
                    {bank.name}
                  </span>
                  {bank.shortName !== bank.name && (
                    <span className="ml-2 text-sm text-gray-500">
                      ({bank.shortName})
                    </span>
                  )}
                </div>

                {selectedBank?.id === bank.id && (
                  <span className="absolute inset-y-0 right-0 flex items-center pr-4">
                    <CheckIcon className="h-5 w-5" aria-hidden="true" />
                  </span>
                )}
              </div>
            ))
          )}
        </div>
      )}

      {/* 点击外部关闭下拉框 */}
      {isOpen && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}
