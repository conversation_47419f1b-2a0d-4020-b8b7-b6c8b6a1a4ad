'use client'

import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'

interface Card {
  id: string
  name: string
  type: string
}

interface CalendarFiltersProps {
  cards: Card[]
  selectedCardId: string
  onCardChange: (cardId: string) => void
  currentDate: Date
  onPrevMonth: () => void
  onNextMonth: () => void
}

const CARD_TYPE_NAMES = {
  WECHAT: '微信',
  ALIPAY: '支付宝',
  DEBIT_CARD: '借记卡',
  CREDIT_CARD: '信用卡',
  CASH: '现金',
}

const MONTH_NAMES = [
  '一月', '二月', '三月', '四月', '五月', '六月',
  '七月', '八月', '九月', '十月', '十一月', '十二月'
]

export function CalendarFilters({
  cards,
  selectedCardId,
  onCardChange,
  currentDate,
  onPrevMonth,
  onNextMonth,
}: CalendarFiltersProps) {
  const year = currentDate.getFullYear()
  const month = currentDate.getMonth()

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        {/* 月份导航 */}
        <div className="flex items-center space-x-4">
          <button
            onClick={onPrevMonth}
            className="p-2 rounded-md border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <ChevronLeftIcon className="h-5 w-5 text-gray-600" />
          </button>
          
          <div className="text-center min-w-[120px]">
            <h2 className="text-xl font-semibold text-gray-900">
              {year}年 {MONTH_NAMES[month]}
            </h2>
          </div>
          
          <button
            onClick={onNextMonth}
            className="p-2 rounded-md border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <ChevronRightIcon className="h-5 w-5 text-gray-600" />
          </button>
        </div>

        {/* 卡片筛选器 */}
        <div className="flex items-center space-x-3">
          <label className="text-sm font-medium text-gray-700">
            筛选卡片:
          </label>
          <select
            value={selectedCardId}
            onChange={(e) => onCardChange(e.target.value)}
            className="rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">所有卡片</option>
            {cards.map((card) => (
              <option key={card.id} value={card.id}>
                {card.name} ({CARD_TYPE_NAMES[card.type as keyof typeof CARD_TYPE_NAMES] || card.type})
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  )
}
