'use client'

import { CalendarDay } from './CalendarDay'
import { CalendarDayDetail } from './CalendarDayDetail'

interface DailyStats {
  date: string
  totalTransactions: number
  totalIncome: number
  totalExpense: number
  netAmount: number
  cardStats: {
    cardId: string
    cardName: string
    cardType: string
    transactionCount: number
    income: number
    expense: number
    netAmount: number
  }[]
}

interface CalendarViewProps {
  year: number
  month: number
  calendarStats: { [date: string]: DailyStats }
  selectedDate: string | null
  onDateSelect: (date: string) => void
}

const WEEKDAYS = ['日', '一', '二', '三', '四', '五', '六']

export function CalendarView({
  year,
  month,
  calendarStats,
  selectedDate,
  onDateSelect,
}: CalendarViewProps) {
  // 生成日历网格
  const generateCalendarDays = () => {
    const firstDay = new Date(year, month - 1, 1)
    const lastDay = new Date(year, month, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()
    
    const days = []
    
    // 添加上个月的空白天数
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }
    
    // 添加当月的天数
    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
      days.push({
        day,
        dateStr,
        stats: calendarStats[dateStr] || null,
      })
    }
    
    return days
  }

  const calendarDays = generateCalendarDays()
  const selectedDayStats = selectedDate ? calendarStats[selectedDate] : null

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* 日历头部 - 星期 */}
      <div className="grid grid-cols-7 border-b border-gray-200">
        {WEEKDAYS.map((weekday, index) => (
          <div
            key={weekday}
            className={`p-4 text-center text-sm font-medium ${
              index === 0 || index === 6 ? 'text-red-600' : 'text-gray-700'
            } bg-gray-50`}
          >
            {weekday}
          </div>
        ))}
      </div>

      {/* 日历网格 */}
      <div className="grid grid-cols-7">
        {calendarDays.map((dayData, index) => (
          <CalendarDay
            key={index}
            dayData={dayData}
            isSelected={dayData?.dateStr === selectedDate}
            onSelect={onDateSelect}
          />
        ))}
      </div>

      {/* 选中日期的详细信息 */}
      {selectedDayStats && (
        <CalendarDayDetail
          stats={selectedDayStats}
          onClose={() => onDateSelect('')}
        />
      )}
    </div>
  )
}
