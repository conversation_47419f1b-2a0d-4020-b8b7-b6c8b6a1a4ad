'use client'

import { ArrowUpIcon, ArrowDownIcon, CurrencyYenIcon, DocumentTextIcon } from '@heroicons/react/24/outline'

interface MonthlyTotal {
  totalTransactions: number
  totalIncome: number
  totalExpense: number
  netAmount: number
}

interface CalendarStatsProps {
  monthlyTotal: MonthlyTotal
  year: number
  month: number
}

export function CalendarStats({ monthlyTotal, year, month }: CalendarStatsProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const stats = [
    {
      name: '交易笔数',
      value: monthlyTotal.totalTransactions.toString(),
      icon: DocumentTextIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      name: '总收入',
      value: formatCurrency(monthlyTotal.totalIncome),
      icon: ArrowUpIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      name: '总支出',
      value: formatCurrency(monthlyTotal.totalExpense),
      icon: ArrowDownIcon,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
    },
    {
      name: '净收入',
      value: formatCurrency(monthlyTotal.netAmount),
      icon: CurrencyYenIcon,
      color: monthlyTotal.netAmount >= 0 ? 'text-green-600' : 'text-red-600',
      bgColor: monthlyTotal.netAmount >= 0 ? 'bg-green-100' : 'bg-red-100',
    },
  ]

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        {year}年{month}月统计
      </h3>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat) => (
          <div
            key={stat.name}
            className="relative overflow-hidden rounded-lg border border-gray-200 p-4"
          >
            <div className="flex items-center">
              <div className={`flex-shrink-0 rounded-md p-3 ${stat.bgColor}`}>
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                <p className={`text-lg font-semibold ${stat.color}`}>
                  {stat.value}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
