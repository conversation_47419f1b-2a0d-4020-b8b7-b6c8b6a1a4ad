'use client'

interface DailyStats {
  date: string
  totalTransactions: number
  totalIncome: number
  totalExpense: number
  netAmount: number
  cardStats: {
    cardId: string
    cardName: string
    cardType: string
    transactionCount: number
    income: number
    expense: number
    netAmount: number
  }[]
}

interface DayData {
  day: number
  dateStr: string
  stats: DailyStats | null
}

interface CalendarDayProps {
  dayData: DayData | null
  isSelected: boolean
  onSelect: (date: string) => void
}

export function CalendarDay({ dayData, isSelected, onSelect }: CalendarDayProps) {
  if (!dayData) {
    // 空白日期
    return <div className="h-24 border-r border-b border-gray-200"></div>
  }

  const { day, dateStr, stats } = dayData
  const hasTransactions = stats && stats.totalTransactions > 0
  const isToday = dateStr === new Date().toISOString().split('T')[0]

  const formatCurrency = (amount: number) => {
    if (amount === 0) return '¥0'
    if (Math.abs(amount) >= 10000) {
      return `¥${(amount / 10000).toFixed(1)}万`
    }
    return `¥${amount.toFixed(0)}`
  }

  const getNetAmountColor = (amount: number) => {
    if (amount > 0) return 'text-green-600'
    if (amount < 0) return 'text-red-600'
    return 'text-gray-500'
  }

  return (
    <div
      className={`
        h-24 border-r border-b border-gray-200 p-2 cursor-pointer transition-colors
        ${isSelected ? 'bg-blue-50 border-blue-300' : 'hover:bg-gray-50'}
        ${isToday ? 'bg-blue-100' : ''}
      `}
      onClick={() => onSelect(dateStr)}
    >
      {/* 日期数字 */}
      <div className="flex justify-between items-start mb-1">
        <span
          className={`
            text-sm font-medium
            ${isToday ? 'text-blue-600 font-bold' : 'text-gray-900'}
            ${isSelected ? 'text-blue-600' : ''}
          `}
        >
          {day}
        </span>
        
        {/* 交易数量指示器 */}
        {hasTransactions && (
          <span className="inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-white bg-blue-600 rounded-full">
            {stats.totalTransactions}
          </span>
        )}
      </div>

      {/* 交易统计 */}
      {hasTransactions && (
        <div className="space-y-1">
          {/* 收入 */}
          {stats.totalIncome > 0 && (
            <div className="text-xs text-green-600">
              +{formatCurrency(stats.totalIncome)}
            </div>
          )}
          
          {/* 支出 */}
          {stats.totalExpense > 0 && (
            <div className="text-xs text-red-600">
              -{formatCurrency(stats.totalExpense)}
            </div>
          )}
          
          {/* 净收入 */}
          {stats.netAmount !== 0 && (
            <div className={`text-xs font-medium ${getNetAmountColor(stats.netAmount)}`}>
              净: {formatCurrency(stats.netAmount)}
            </div>
          )}
        </div>
      )}

      {/* 卡片数量指示器 */}
      {hasTransactions && stats.cardStats.length > 1 && (
        <div className="mt-1">
          <div className="flex space-x-1">
            {stats.cardStats.slice(0, 3).map((cardStat, index) => (
              <div
                key={cardStat.cardId}
                className="w-2 h-2 rounded-full bg-gray-400"
                title={cardStat.cardName}
              />
            ))}
            {stats.cardStats.length > 3 && (
              <div className="text-xs text-gray-500">+{stats.cardStats.length - 3}</div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
