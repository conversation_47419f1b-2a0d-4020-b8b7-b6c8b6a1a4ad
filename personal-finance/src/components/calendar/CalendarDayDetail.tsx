'use client'

import { XMarkIcon } from '@heroicons/react/24/outline'

interface DailyStats {
  date: string
  totalTransactions: number
  totalIncome: number
  totalExpense: number
  netAmount: number
  cardStats: {
    cardId: string
    cardName: string
    cardType: string
    transactionCount: number
    income: number
    expense: number
    netAmount: number
  }[]
}

interface CalendarDayDetailProps {
  stats: DailyStats
  onClose: () => void
}

const CARD_TYPE_NAMES = {
  WECHAT: '微信',
  ALIPAY: '支付宝',
  DEBIT_CARD: '借记卡',
  CREDIT_CARD: '信用卡',
  CASH: '现金',
}

export function CalendarDayDetail({ stats, onClose }: CalendarDayDetailProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long',
    })
  }

  const getNetAmountColor = (amount: number) => {
    if (amount > 0) return 'text-green-600'
    if (amount < 0) return 'text-red-600'
    return 'text-gray-500'
  }

  return (
    <div className="border-t border-gray-200 bg-gray-50 p-6">
      {/* 头部 */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">
          {formatDate(stats.date)}
        </h3>
        <button
          onClick={onClose}
          className="p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-200"
        >
          <XMarkIcon className="h-5 w-5" />
        </button>
      </div>

      {/* 日总计 */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <p className="text-sm text-gray-500">交易笔数</p>
          <p className="text-xl font-semibold text-blue-600">{stats.totalTransactions}</p>
        </div>
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <p className="text-sm text-gray-500">总收入</p>
          <p className="text-xl font-semibold text-green-600">
            {formatCurrency(stats.totalIncome)}
          </p>
        </div>
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <p className="text-sm text-gray-500">总支出</p>
          <p className="text-xl font-semibold text-red-600">
            {formatCurrency(stats.totalExpense)}
          </p>
        </div>
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <p className="text-sm text-gray-500">净收入</p>
          <p className={`text-xl font-semibold ${getNetAmountColor(stats.netAmount)}`}>
            {formatCurrency(stats.netAmount)}
          </p>
        </div>
      </div>

      {/* 按卡片分组统计 */}
      {stats.cardStats.length > 0 && (
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-3">按卡片统计</h4>
          <div className="space-y-3">
            {stats.cardStats.map((cardStat) => (
              <div
                key={cardStat.cardId}
                className="bg-white rounded-lg p-4 border border-gray-200"
              >
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <h5 className="font-medium text-gray-900">{cardStat.cardName}</h5>
                    <p className="text-sm text-gray-500">
                      {CARD_TYPE_NAMES[cardStat.cardType as keyof typeof CARD_TYPE_NAMES] || cardStat.cardType}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">{cardStat.transactionCount} 笔交易</p>
                    <p className={`font-medium ${getNetAmountColor(cardStat.netAmount)}`}>
                      {formatCurrency(cardStat.netAmount)}
                    </p>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 mt-3">
                  <div>
                    <p className="text-xs text-gray-500">收入</p>
                    <p className="text-sm font-medium text-green-600">
                      {formatCurrency(cardStat.income)}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">支出</p>
                    <p className="text-sm font-medium text-red-600">
                      {formatCurrency(cardStat.expense)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
