'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useToast } from '@/hooks/useToast'
import { Button } from '@/components/ui/Button'

interface Card {
  id: string
  name: string
  type: string
  accountNo?: string
  bankName?: string
  balance: number
  description?: string
  isActive: boolean
}

interface CardFormProps {
  card?: Card | null
  onSuccess: () => void
  onCancel: () => void
}

const CARD_TYPES = [
  { value: 'DEBIT_CARD', label: '借记卡' },
  { value: 'CREDIT_CARD', label: '信用卡' },
  { value: 'WECHAT', label: '微信' },
  { value: 'ALIPAY', label: '支付宝' },
  { value: 'CASH', label: '现金' },
  { value: 'OTHER', label: '其他' },
]

export function CardForm({ card, onSuccess, onCancel }: CardFormProps) {
  const { apiCall } = useAuth()
  const { showSuccess, showError } = useToast()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    type: 'DEBIT_CARD',
    accountNo: '',
    bankName: '',
    balance: 0,
    description: '',
    isActive: true,
  })

  useEffect(() => {
    if (card) {
      setFormData({
        name: card.name,
        type: card.type,
        accountNo: card.accountNo || '',
        bankName: card.bankName || '',
        balance: card.balance,
        description: card.description || '',
        isActive: card.isActive,
      })
    }
  }, [card])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const url = card ? `/api/cards/${card.id}` : '/api/cards'
      const method = card ? 'PUT' : 'POST'

      const response = await apiCall(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()
      if (data.success) {
        showSuccess(card ? '卡片更新成功' : '卡片创建成功')
        onSuccess()
      } else {
        showError('操作失败', data.error || '请检查输入信息')
      }
    } catch (error) {
      console.error('Form submission failed:', error)
      showError('操作失败', '网络错误，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : 
              type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-bold mb-4">
          {card ? '编辑卡片' : '添加卡片'}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 卡片名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              卡片名称 *
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
              placeholder="例如：招商银行储蓄卡"
            />
          </div>

          {/* 卡片类型 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              卡片类型 *
            </label>
            <select
              name="type"
              value={formData.type}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
            >
              {CARD_TYPES.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          {/* 账号 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              账号/卡号
            </label>
            <input
              type="text"
              name="accountNo"
              value={formData.accountNo}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
              placeholder="例如：****1234"
            />
          </div>

          {/* 银行名称 */}
          {(formData.type === 'DEBIT_CARD' || formData.type === 'CREDIT_CARD') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                银行名称
              </label>
              <input
                type="text"
                name="bankName"
                value={formData.bankName}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                placeholder="例如：招商银行"
              />
            </div>
          )}

          {/* 余额 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              当前余额
            </label>
            <input
              type="number"
              name="balance"
              value={formData.balance}
              onChange={handleChange}
              step="0.01"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
              placeholder="0.00"
            />
          </div>

          {/* 描述 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              描述
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
              placeholder="可选的描述信息"
            />
          </div>

          {/* 状态 */}
          <div className="flex items-center">
            <input
              type="checkbox"
              name="isActive"
              checked={formData.isActive}
              onChange={handleChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-700">
              启用此卡片
            </label>
          </div>

          {/* 按钮 */}
          <div className="flex space-x-3 pt-4">
            <Button
              type="submit"
              disabled={loading}
              className="flex-1"
            >
              {loading ? '保存中...' : (card ? '更新' : '创建')}
            </Button>
            <Button
              type="button"
              variant="secondary"
              onClick={onCancel}
              disabled={loading}
              className="flex-1"
            >
              取消
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
