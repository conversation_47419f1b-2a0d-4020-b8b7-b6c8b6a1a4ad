'use client'

import Link from 'next/link'
import { PencilIcon, TrashIcon, CalendarDaysIcon } from '@heroicons/react/24/outline'

interface Card {
  id: string
  name: string
  type: string
  accountNo?: string
  bankName?: string
  balance: number
  isActive: boolean
  createdAt: string
  user: {
    id: string
    name: string
  }
  _count: {
    transactions: number
    billImports: number
  }
}

interface CardGridProps {
  cards: Card[]
  onEdit: (card: Card) => void
  onDelete: (cardId: string) => void
  onViewCalendar?: (cardId: string) => void
}

export function CardGrid({ cards, onEdit, onDelete, onViewCalendar }: CardGridProps) {
  const getCardTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      DEBIT_CARD: '借记卡',
      CREDIT_CARD: '信用卡',
      WECHAT: '微信',
      ALIPAY: '支付宝',
      CASH: '现金',
      OTHER: '其他',
    }
    return labels[type] || type
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(amount)
  }

  const getCardColor = (type: string) => {
    const colors: Record<string, string> = {
      DEBIT_CARD: 'from-blue-500 to-blue-600',
      CREDIT_CARD: 'from-purple-500 to-purple-600',
      WECHAT: 'from-green-500 to-green-600',
      ALIPAY: 'from-blue-400 to-blue-500',
      CASH: 'from-gray-500 to-gray-600',
      OTHER: 'from-indigo-500 to-indigo-600',
    }
    return colors[type] || 'from-gray-500 to-gray-600'
  }

  if (cards.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 text-lg mb-4">暂无卡片</div>
        <p className="text-gray-400">点击上方"添加卡片"按钮创建您的第一张卡片</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {cards.filter(card => card.isActive).map((card) => (
        <div
          key={card.id}
          className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
        >
          {/* 卡片头部 - 模拟银行卡样式 */}
          <div className={`bg-gradient-to-r ${getCardColor(card.type)} p-6 text-white relative`}>
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold truncate">{card.name}</h3>
                <p className="text-sm opacity-90 mt-1">{getCardTypeLabel(card.type)}</p>
              </div>
              <div className="flex space-x-2">
                {onViewCalendar && (
                  <button
                    onClick={() => onViewCalendar(card.id)}
                    className="p-1 hover:bg-white/20 rounded-full transition-colors"
                    title="查看日历"
                  >
                    <CalendarDaysIcon className="h-4 w-4" />
                  </button>
                )}
                <button
                  onClick={() => onEdit(card)}
                  className="p-1 hover:bg-white/20 rounded-full transition-colors"
                  title="编辑卡片"
                >
                  <PencilIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => onDelete(card.id)}
                  className="p-1 hover:bg-white/20 rounded-full transition-colors"
                  title="删除卡片"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
            
            {card.accountNo && (
              <div className="mt-4">
                <p className="text-sm opacity-75">卡号</p>
                <p className="font-mono text-lg tracking-wider">{card.accountNo}</p>
              </div>
            )}
            
            <div className="mt-4">
              <p className="text-sm opacity-75">余额</p>
              <p className="text-2xl font-bold">{formatCurrency(card.balance)}</p>
            </div>
          </div>

          {/* 卡片内容 */}
          <div className="p-6">
            {card.bankName && (
              <div className="mb-4">
                <p className="text-sm text-gray-600">银行: {card.bankName}</p>
              </div>
            )}

            {/* 统计信息 */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{card._count.transactions}</div>
                <div className="text-sm text-gray-500">交易记录</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{card._count.billImports}</div>
                <div className="text-sm text-gray-500">账单导入</div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="space-y-3">
              <Link
                href={`/import?cardId=${card.id}`}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-center block"
              >
                账单导入
              </Link>
              <div className="grid grid-cols-2 gap-2">
                <Link
                  href={`/transactions?cardId=${card.id}`}
                  className="bg-gray-100 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-200 transition-colors text-center block text-sm"
                >
                  查看交易
                </Link>
                {onViewCalendar && (
                  <button
                    onClick={() => onViewCalendar(card.id)}
                    className="bg-green-100 text-green-700 py-2 px-3 rounded-lg hover:bg-green-200 transition-colors text-center text-sm flex items-center justify-center space-x-1"
                  >
                    <CalendarDaysIcon className="h-4 w-4" />
                    <span>日历</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
