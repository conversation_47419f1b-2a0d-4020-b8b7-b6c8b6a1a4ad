'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { getCardTypeLabel, getStatusColor, formatDate } from '@/lib/utils'

interface Card {
  id: string
  name: string
  type: string
  accountNo?: string
  bankName?: string
  isActive: boolean
  createdAt: string
  user: {
    id: string
    name: string
  }
  _count: {
    transactions: number
    billImports: number
  }
}

interface CardListProps {
  cards: Card[]
  onEdit?: (card: Card) => void
  onDelete?: (cardId: string) => void
  onToggleStatus?: (cardId: string, isActive: boolean) => void
}

export function CardList({ cards, onEdit, onDelete, onToggleStatus }: CardListProps) {
  const [selectedCards, setSelectedCards] = useState<string[]>([])

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCards(cards.map(card => card.id))
    } else {
      setSelectedCards([])
    }
  }

  const handleSelectCard = (cardId: string, checked: boolean) => {
    if (checked) {
      setSelectedCards(prev => [...prev, cardId])
    } else {
      setSelectedCards(prev => prev.filter(id => id !== cardId))
    }
  }

  const isAllSelected = cards.length > 0 && selectedCards.length === cards.length
  const isPartiallySelected = selectedCards.length > 0 && selectedCards.length < cards.length

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">卡片列表</h3>
          <div className="flex space-x-2">
            {selectedCards.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // 批量操作
                  console.log('批量操作:', selectedCards)
                }}
              >
                批量操作 ({selectedCards.length})
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={input => {
                    if (input) input.indeterminate = isPartiallySelected
                  }}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                卡片信息
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                类型
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                所属用户
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                统计
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建时间
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {cards.map((card) => (
              <tr key={card.id} className="hover:bg-gray-50">
                <td className="px-6 py-4">
                  <input
                    type="checkbox"
                    checked={selectedCards.includes(card.id)}
                    onChange={(e) => handleSelectCard(card.id, e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </td>
                <td className="px-6 py-4">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{card.name}</div>
                    {card.bankName && (
                      <div className="text-sm text-gray-500">{card.bankName}</div>
                    )}
                    {card.accountNo && (
                      <div className="text-sm text-gray-500">{card.accountNo}</div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                    {getCardTypeLabel(card.type)}
                  </span>
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {card.user.name}
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-900">
                    <div>交易: {card._count.transactions}</div>
                    <div>导入: {card._count.billImports}</div>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    card.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {card.isActive ? '活跃' : '禁用'}
                  </span>
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">
                  {formatDate(card.createdAt)}
                </td>
                <td className="px-6 py-4 text-right text-sm font-medium space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit?.(card)}
                  >
                    编辑
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onToggleStatus?.(card.id, !card.isActive)}
                  >
                    {card.isActive ? '禁用' : '启用'}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDelete?.(card.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    删除
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {cards.length === 0 && (
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">暂无卡片</h3>
          <p className="mt-1 text-sm text-gray-500">开始添加您的第一张卡片</p>
        </div>
      )}
    </div>
  )
}
