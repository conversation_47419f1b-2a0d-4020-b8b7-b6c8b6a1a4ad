'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'

interface User {
  id: string
  email: string
  name: string
  avatar?: string
  familyId?: string
  familyRole?: string
  family?: {
    id: string
    name: string
    role: string
  }
}

interface AuthState {
  user: User | null
  token: string | null
  loading: boolean
  isAuthenticated: boolean
}

export function useAuth() {
  const router = useRouter()
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    token: null,
    loading: true,
    isAuthenticated: false,
  })

  useEffect(() => {
    // 确保只在客户端执行
    if (typeof window === 'undefined') {
      setAuthState(prev => ({ ...prev, loading: false }))
      return
    }

    // 从localStorage获取认证信息
    const token = localStorage.getItem('token')
    const userStr = localStorage.getItem('user')

    if (token && userStr) {
      try {
        const user = JSON.parse(userStr)
        setAuthState({
          user,
          token,
          loading: false,
          isAuthenticated: true,
        })
      } catch (error) {
        console.error('Failed to parse user data:', error)
        logout()
      }
    } else {
      setAuthState(prev => ({ ...prev, loading: false }))
    }
  }, [logout])

  const login = async (email: string, password: string) => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      const data = await response.json()

      if (data.success) {
        const { user, token } = data.data

        if (typeof window !== 'undefined') {
          localStorage.setItem('token', token)
          localStorage.setItem('user', JSON.stringify(user))
        }

        setAuthState({
          user,
          token,
          loading: false,
          isAuthenticated: true,
        })

        return { success: true }
      } else {
        return { success: false, error: data.error }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: '网络错误，请重试' }
    }
  }

  const register = async (name: string, email: string, password: string) => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, email, password }),
      })

      const data = await response.json()

      if (data.success) {
        const { user, token } = data.data

        if (typeof window !== 'undefined') {
          localStorage.setItem('token', token)
          localStorage.setItem('user', JSON.stringify(user))
        }

        setAuthState({
          user,
          token,
          loading: false,
          isAuthenticated: true,
        })

        return { success: true }
      } else {
        return { success: false, error: data.error }
      }
    } catch (error) {
      console.error('Register error:', error)
      return { success: false, error: '网络错误，请重试' }
    }
  }

  const logout = useCallback(() => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token')
      localStorage.removeItem('user')
    }

    setAuthState({
      user: null,
      token: null,
      loading: false,
      isAuthenticated: false,
    })

    router.push('/auth/login')
  }, [router])

  const updateUser = (updatedUser: Partial<User>) => {
    if (authState.user) {
      const newUser = { ...authState.user, ...updatedUser }
      if (typeof window !== 'undefined') {
        localStorage.setItem('user', JSON.stringify(newUser))
      }
      setAuthState(prev => ({
        ...prev,
        user: newUser,
      }))
    }
  }

  const getAuthHeaders = useCallback(() => {
    if (authState.token) {
      return {
        'Authorization': `Bearer ${authState.token}`,
        'Content-Type': 'application/json',
      }
    }
    return {
      'Content-Type': 'application/json',
    }
  }, [authState.token])

  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {
    const headers = {
      ...getAuthHeaders(),
      ...options.headers,
    }

    const response = await fetch(url, {
      ...options,
      headers,
    })

    if (response.status === 401) {
      // Token过期或无效，自动登出
      logout()
      throw new Error('认证失败，请重新登录')
    }

    return response
  }, [getAuthHeaders, logout])

  return {
    ...authState,
    login,
    register,
    logout,
    updateUser,
    getAuthHeaders,
    apiCall,
  }
}
