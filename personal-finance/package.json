{"name": "personal-finance", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "15.3.5", "prisma": "^6.11.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "recharts": "^3.0.2", "tailwind-merge": "^3.3.1", "zod": "^3.25.75", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}